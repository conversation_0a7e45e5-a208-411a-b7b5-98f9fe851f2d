# 🚀 STEP-BY-STEP DEPLOYMENT GUIDE

## 📋 OVERVIEW
- **Upload**: FULL project folder to GitHub
- **Backend**: Deploy to Render Web Service
- **Database**: Render PostgreSQL
- **Frontend**: Deploy to Firebase Hosting
- **Cache**: Render Redis (optional but recommended)

---

## 🔐 STEP 1: SECURE YOUR REPOSITORY

### 1.1 Remove Sensitive Files (CRITICAL!)
Before uploading to GitHub, ensure these files are NOT included:

```bash
# Check these files are in .gitignore (they should be):
backend/.env
backend/secrets/firebase-service-account-key.json
backend/certs/
frontend/certs/
logs/
```

### 1.2 Verify .gitignore
Your `.gitignore` is already configured correctly. Double-check by running:
```bash
git status
```
Make sure NO sensitive files appear in the list.

---

## 📤 STEP 2: UPLOAD TO GITHUB

### 2.1 Initialize Git (if not already done)
```bash
git init
git add .
git commit -m "Initial commit - Customer Management System"
```

### 2.2 Create GitHub Repository
1. Go to [GitHub.com](https://github.com)
2. Click "New Repository"
3. Name: `customer-management-system` (or your preferred name)
4. Set to **Private** (recommended for business applications)
5. Don't initialize with README (you already have files)
6. Click "Create Repository"

### 2.3 Push to GitHub
```bash
git remote add origin https://github.com/YOUR_USERNAME/customer-management-system.git
git branch -M main
git push -u origin main
```

---

## 🔥 STEP 3: SETUP FIREBASE

### 3.1 Prepare Firebase Service Account
1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your project (or create new one)
3. Go to Project Settings > Service Accounts
4. Click "Generate new private key"
5. **SAVE THE JSON FILE** - you'll need it for Render

### 3.2 Get Firebase Configuration
1. In Firebase Console, go to Project Settings > General
2. Scroll to "Your apps" section
3. Copy these values (you'll need them later):
   - API Key
   - Auth Domain
   - Project ID
   - Storage Bucket
   - Messaging Sender ID
   - App ID

### 3.3 Enable Firebase Services
1. **Authentication**: Go to Authentication > Sign-in method > Enable Email/Password
2. **Storage**: Go to Storage > Get started
3. **Hosting**: Go to Hosting > Get started

---

## 🖥️ STEP 4: DEPLOY BACKEND TO RENDER

### 4.1 Create Render Account
1. Go to [Render.com](https://render.com)
2. Sign up/Login
3. Connect your GitHub account

### 4.2 Create PostgreSQL Database
1. Click "New +" > "PostgreSQL"
2. Name: `customer-management-db`
3. Database Name: `customer_management`
4. User: `customer_mgmt_user`
5. Region: Choose closest to your users
6. Plan: **Starter** (can upgrade later)
7. Click "Create Database"
8. **SAVE** the connection details (Internal/External Database URL)

### 4.3 Create Redis Cache (Optional but Recommended)
1. Click "New +" > "Redis"
2. Name: `customer-management-redis`
3. Plan: **Starter**
4. Click "Create Redis"

### 4.4 Create Web Service (Backend API)
1. Click "New +" > "Web Service"
2. Connect your GitHub repository
3. **Configuration**:
   - **Name**: `customer-management-api`
   - **Region**: Same as database
   - **Branch**: `main`
   - **Root Directory**: `backend`
   - **Runtime**: `Python 3`
   - **Build Command**: `pip install -r requirements.txt`
   - **Start Command**: `gunicorn --bind 0.0.0.0:$PORT --workers 2 --timeout 120 app:app`
   - **Plan**: **Starter** (can upgrade later)

### 4.5 Configure Environment Variables
In your Render web service, go to Environment tab and add:

```
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=[Leave empty - Render will auto-generate]
DATABASE_URL=[Will be auto-set by PostgreSQL service]
REDIS_URL=[Will be auto-set by Redis service]

# Firebase Configuration (CRITICAL!)
FIREBASE_CREDENTIALS_JSON=[Paste your Firebase service account JSON as ONE LINE]
FIREBASE_STORAGE_BUCKET=amspmdeploy.firebasestorage.app

# CORS Configuration (UPDATE AFTER FRONTEND DEPLOYMENT)
FRONTEND_URL=https://your-project.web.app
FIREBASE_HOSTING_DOMAIN=https://your-project.web.app

# Admin Configuration
ADMIN_EMAIL=<EMAIL>

# Cache Configuration
CACHE_TYPE=redis
CACHE_TIMEOUT=3600
CACHE_KEY_PREFIX=customer_mgmt_prod_

# Security Settings
RATE_LIMIT_ENABLED=True
SANITIZE_RESPONSES=True
LOG_FILE=/tmp/app.log
```

**IMPORTANT**: For `FIREBASE_CREDENTIALS_JSON`, copy the ENTIRE JSON from your service account file and paste it as ONE LINE (remove line breaks).

### 4.6 Deploy Backend
1. Click "Create Web Service"
2. Wait for deployment (5-10 minutes)
3. Check logs for any errors
4. Test health endpoint: `https://your-api.onrender.com/api/health`

---

## 🗄️ STEP 5: INITIALIZE DATABASE

### 5.1 Run Database Setup
Once your backend is deployed:
1. Go to your Render service dashboard
2. Open the "Shell" tab
3. Run: `python init_production_db.py`
4. Check for success messages

---

## 🌐 STEP 6: DEPLOY FRONTEND TO FIREBASE

### 6.1 Install Firebase CLI
```bash
npm install -g firebase-tools
firebase login
```

### 6.2 Configure Frontend Environment
Create `frontend/.env.production`:
```
VITE_API_URL=https://your-backend-api.onrender.com/api
VITE_FIREBASE_API_KEY=your-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
VITE_FIREBASE_APP_ID=your-app-id
VITE_FIREBASE_MEASUREMENT_ID=your-measurement-id
```

### 6.3 Update Firebase Configuration
Edit `frontend/.firebaserc`:
```json
{
  "projects": {
    "default": "your-actual-firebase-project-id"
  }
}
```

### 6.4 Build and Deploy Frontend
```bash
cd frontend
npm ci
npm run build
firebase deploy --only hosting
```

### 6.5 Get Frontend URL
After deployment, Firebase will show your hosting URL (e.g., `https://your-project.web.app`)

---

## 🔄 STEP 7: UPDATE BACKEND CORS

### 7.1 Update Backend Environment Variables
Go back to your Render backend service and update:
```
FRONTEND_URL=https://your-project.web.app
FIREBASE_HOSTING_DOMAIN=https://your-project.web.app
```

### 7.2 Redeploy Backend
Click "Manual Deploy" > "Deploy latest commit"

---

## ✅ STEP 8: FINAL TESTING

### 8.1 Test Complete Flow
1. Visit your frontend URL
2. Try to register/login
3. Test customer management
4. Test document upload
5. Test all major features

### 8.2 Change Admin Password
1. Login with admin credentials
2. Go to user management
3. Change the default password immediately

---

## 🚨 TROUBLESHOOTING

### Common Issues:

**CORS Errors:**
- Check FRONTEND_URL in backend environment variables
- Ensure URLs match exactly (including https://)

**Authentication Issues:**
- Verify Firebase configuration on both frontend and backend
- Check Firebase service account permissions

**Database Connection:**
- Check DATABASE_URL is properly set
- Verify PostgreSQL service is running

**Build Failures:**
- Check build logs in Render dashboard
- Verify all dependencies in requirements.txt

---

## 📊 MONITORING

### After Deployment:
1. Monitor Render service logs
2. Set up Render alerts
3. Monitor Firebase usage
4. Check application performance
5. Set up regular backups

---

## 🔐 SECURITY CHECKLIST

- [ ] No sensitive files in GitHub
- [ ] Strong admin password set
- [ ] Firebase security rules configured
- [ ] HTTPS enforced everywhere
- [ ] Environment variables properly set
- [ ] Rate limiting enabled
- [ ] Security headers configured

---

## 📞 SUPPORT

If you encounter issues:
1. Check service logs in Render dashboard
2. Verify environment variables
3. Test individual components
4. Check Firebase console for errors
5. Review deployment checklist
