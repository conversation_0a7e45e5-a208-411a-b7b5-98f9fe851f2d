"""
Main application entry point for production deployment.
This file is used by deployment platforms like Render, Heroku, etc.
"""

import os
import sys

# Set default environment variables for production
os.environ.setdefault('FLASK_ENV', 'production')
os.environ.setdefault('FLASK_DEBUG', 'False')

# Initialize app variable
app = None

try:
    # Import from the app package (not this file)
    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

    from app import create_app
    # Create the Flask application
    app, _ = create_app()
    print("✓ Flask app created successfully")
except Exception as e:
    print(f"✗ Failed to create Flask app: {e}")
    import traceback
    traceback.print_exc()

    # Try to create a minimal app for debugging
    try:
        from flask import Flask
        app = Flask(__name__)

        @app.route('/api/health')
        def health_check():
            return {'status': 'minimal', 'message': 'Minimal app running - check logs for errors'}, 200

        @app.route('/')
        def root():
            return {'error': 'Application failed to initialize properly', 'message': str(e)}, 500

        print("✓ Created minimal Flask app for debugging")
    except Exception as fallback_error:
        print(f"✗ Failed to create even minimal app: {fallback_error}")
        sys.exit(1)

# Add a health check endpoint for deployment platforms
# Only add if app was created successfully (not minimal app)
if hasattr(app, 'config') and app.config:
    @app.route('/api/health')
    def health_check():
        """Health check endpoint for monitoring services."""
        firebase_status = app.config.get('FIREBASE_INITIALIZED', False)
        return {
            'status': 'healthy',
            'message': 'Customer Management API is running',
            'firebase_initialized': firebase_status
        }, 200

if __name__ == "__main__":
    # This will only run if the file is executed directly
    # Most deployment platforms will use a WSGI server instead
    port = int(os.getenv("PORT", 5000))
    debug = os.getenv("FLASK_DEBUG", "False").lower() in ("true", "1", "t")
    
    app.run(host="0.0.0.0", port=port, debug=debug)
