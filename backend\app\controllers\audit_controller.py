from flask import Blueprint, request, jsonify
from app.services.audit_service import AuditService
from app.utils.security import token_required, role_required
from app.utils.rate_limit import rate_limit
from app.utils.cache_decorators import cached_list, cached_detail
from datetime import datetime, timedelta, timezone
import logging

audit_bp = Blueprint("audit", __name__)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

audit_service = AuditService()

@audit_bp.route("", methods=["GET"])
@token_required
@role_required("administrator")
@rate_limit("60/minute")
@cached_list(entity_type="audit", timeout=60)  # Cache for 1 minute
def get_audit_logs():
    """
    Get audit logs with optional filtering.

    Query parameters:
        page: The page number.
        per_page: The number of items per page.
        entity_type: Filter by entity type.
        action: Filter by action.
        user_id: Filter by user ID.
        start_date: Filter by start date (ISO format).
        end_date: Filter by end date (ISO format).

    Returns:
        A list of audit logs.
    """
    page = request.args.get("page", 1, type=int)
    per_page = request.args.get("per_page", 20, type=int)
    entity_type = request.args.get("entity_type")
    action = request.args.get("action")
    user_id = request.args.get("user_id", type=int)

    start_date = request.args.get("start_date")
    if start_date:
        try:
            start_date = datetime.fromisoformat(start_date)
        except ValueError:
            return jsonify({"error": "Invalid start_date format. Use ISO format (YYYY-MM-DDTHH:MM:SS)."}), 400

    end_date = request.args.get("end_date")
    if end_date:
        try:
            end_date = datetime.fromisoformat(end_date)
        except ValueError:
            return jsonify({"error": "Invalid end_date format. Use ISO format (YYYY-MM-DDTHH:MM:SS)."}), 400

    try:
        logs, total = audit_service.get_audit_logs(
            page=page,
            per_page=per_page,
            entity_type=entity_type,
            action=action,
            user_id=user_id,
            start_date=start_date,
            end_date=end_date
        )

        return jsonify({
            "logs": logs,
            "page": page,
            "per_page": per_page,
            "total": total
        }), 200
    except Exception as e:
        logger.error(f"Failed to get audit logs: {str(e)}")
        return jsonify({"error": str(e)}), 500

@audit_bp.route("/user/<int:user_id>", methods=["GET"])
@token_required
@role_required("administrator")
@rate_limit("60/minute")
@cached_list(entity_type="user_audit", timeout=60)  # Cache for 1 minute
def get_user_activity(user_id):
    """
    Get activity logs for a user.

    Args:
        user_id: The ID of the user.

    Query parameters:
        page: The page number.
        per_page: The number of items per page.

    Returns:
        A list of audit logs.
    """
    page = request.args.get("page", 1, type=int)
    per_page = request.args.get("per_page", 20, type=int)

    try:
        logs, total = audit_service.get_user_activity(user_id, page, per_page)

        return jsonify({
            "logs": logs,
            "page": page,
            "per_page": per_page,
            "total": total
        }), 200
    except Exception as e:
        logger.error(f"Failed to get user activity: {str(e)}")
        return jsonify({"error": str(e)}), 500

@audit_bp.route("/entity/<string:entity_type>/<int:entity_id>", methods=["GET"])
@token_required
@role_required("administrator")
@rate_limit("60/minute")
@cached_list(entity_type="entity_audit", timeout=60)  # Cache for 1 minute
def get_entity_history(entity_type, entity_id):
    """
    Get history logs for an entity.

    Args:
        entity_type: The type of entity.
        entity_id: The ID of the entity.

    Query parameters:
        page: The page number.
        per_page: The number of items per page.

    Returns:
        A list of audit logs.
    """
    page = request.args.get("page", 1, type=int)
    per_page = request.args.get("per_page", 20, type=int)

    try:
        logs, total = audit_service.get_entity_history(entity_type, entity_id, page, per_page)

        return jsonify({
            "logs": logs,
            "page": page,
            "per_page": per_page,
            "total": total
        }), 200
    except Exception as e:
        logger.error(f"Failed to get entity history: {str(e)}")
        return jsonify({"error": str(e)}), 500

@audit_bp.route("/recent", methods=["GET"])
@token_required
@rate_limit("60/minute")
@cached_list(entity_type="recent_activity", timeout=60)  # Cache for 1 minute
def get_recent_activities():
    """
    Get recent activity logs.

    Query parameters:
        limit: The maximum number of activities to return (default: 10).

    Returns:
        A list of recent activities formatted for the dashboard.
    """
    limit = request.args.get("limit", 10, type=int)

    try:
        # Get recent activities from the last 7 days
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=7)

        logs, _ = audit_service.get_audit_logs(
            page=1,
            per_page=limit,
            start_date=start_date,
            end_date=end_date
        )

        # Transform logs into activity format
        activities = []
        for log in logs:
            activity_type = log.get('entity_type', '')
            if activity_type in ['user', 'customer', 'document', 'event']:
                activity = {
                    'id': log.get('id'),
                    'type': activity_type,
                    'action': log.get('action', ''),
                    'subject': log.get('details', {}).get('name', str(log.get('entity_id', ''))),
                    'timestamp': log.get('timestamp'),
                    'user': {
                        'id': log.get('user_id'),
                        'name': log.get('user_name', 'Unknown User')
                    }
                }
                activities.append(activity)

        return jsonify({
            "activities": activities
        }), 200
    except Exception as e:
        logger.error(f"Failed to get recent activities: {str(e)}")
        return jsonify({"error": str(e)}), 500