"""
Customer note controller module.
This module provides API endpoints for customer notes.
"""
from flask import Blueprint, request, jsonify
from app.services.customer_note_service import CustomerNoteService
from app.utils.security import token_required, role_required, roles_required
from app.utils.rate_limit import rate_limit
from app.schemas.customer_note_schema import customer_note_schema
import logging
from marshmallow import ValidationError

# Define the blueprint for customer note-related routes
customer_note_bp = Blueprint("customer_note", __name__)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize the customer note service
customer_note_service = CustomerNoteService()

@customer_note_bp.route("/customer/<int:customer_id>", methods=["GET"], strict_slashes=False)
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("60/minute")
def get_customer_notes(customer_id):
    """
    Get all notes for a customer.

    Path Parameters:
        customer_id (int): The ID of the customer.

    Returns:
        JSON: List of customer notes.
    """
    try:
        notes = customer_note_service.get_notes_by_customer_id(customer_id)
        logger.info(f"Fetched notes for customer with ID {customer_id}")
        return jsonify(notes), 200
    except Exception as e:
        logger.error(f"Failed to fetch notes for customer {customer_id}: {str(e)}")
        return jsonify({"error": str(e)}), 404

@customer_note_bp.route("/<int:note_id>", methods=["GET"], strict_slashes=False)
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("60/minute")
def get_note(note_id):
    """
    Get a specific note by ID.

    Path Parameters:
        note_id (int): The ID of the note to retrieve.

    Returns:
        JSON: Note details.
    """
    try:
        note = customer_note_service.get_note_by_id(note_id)
        if not note:
            return jsonify({"error": f"Note with ID {note_id} not found"}), 404
        logger.info(f"Fetched note with ID {note_id}")
        return jsonify(note), 200
    except Exception as e:
        logger.error(f"Failed to fetch note {note_id}: {str(e)}")
        return jsonify({"error": str(e)}), 404

@customer_note_bp.route("", methods=["POST"], strict_slashes=False)
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("30/minute")
def create_note():
    """
    Create a new note.

    Request Body: Note data object containing customer_id, content
    Returns: JSON: The created note
    """
    data = request.get_json()
    if not data:
        logger.warning("Create note failed: No data provided")
        return jsonify({"error": "No data provided"}), 400

    # Add the current user's ID to the note data
    data['user_id'] = request.current_user.id

    try:
        # Validate the input data using the schema
        errors = customer_note_schema.validate(data)
        if errors:
            logger.warning(f"Create note validation failed: {errors}")
            return jsonify({"error": "Validation failed", "details": errors}), 400

        # Create the note
        note = customer_note_service.create_note(data)
        logger.info(f"Created note for customer ID: {data.get('customer_id')}")
        return jsonify(note), 201
    except ValidationError as e:
        logger.warning(f"Create note validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to create note: {str(e)}")
        return jsonify({"error": str(e)}), 400

@customer_note_bp.route("/<int:note_id>", methods=["PUT"], strict_slashes=False)
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("30/minute")
def update_note(note_id):
    """
    Update an existing note.

    Path Parameters:
        note_id (int): The ID of the note to update.

    Request Body: Note data object containing content
    Returns: JSON: The updated note
    """
    data = request.get_json()
    if not data:
        logger.warning(f"Update note {note_id} failed: No data provided")
        return jsonify({"error": "No data provided"}), 400

    try:
        # Update the note
        note = customer_note_service.update_note(note_id, data)
        logger.info(f"Updated note with ID {note_id}")
        return jsonify(note), 200
    except Exception as e:
        logger.error(f"Failed to update note {note_id}: {str(e)}")
        return jsonify({"error": str(e)}), 404

@customer_note_bp.route("/<int:note_id>", methods=["DELETE"], strict_slashes=False)
@roles_required("administrator", "verkoper", "monteur")
@rate_limit("30/minute")
def delete_note(note_id):
    """
    Delete a note by ID.

    Path Parameters:
        note_id (int): The ID of the note to delete.

    Returns:
        JSON: Success message.
    """
    try:
        customer_note_service.delete_note(note_id)
        logger.info(f"Deleted note with ID {note_id}")
        return jsonify({"message": "Note deleted successfully"}), 200
    except Exception as e:
        logger.error(f"Failed to delete note {note_id}: {str(e)}")
        return jsonify({"error": str(e)}), 404
