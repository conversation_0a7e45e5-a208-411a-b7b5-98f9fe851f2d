from flask import Blueprint, request, jsonify, send_file
from app.services.customer_service import CustomerService
from app.utils.security import token_required, role_required
from app.utils.rate_limit import rate_limit
import csv
import io
import json
import logging
import tempfile
import os
import pandas as pd
from openpyxl import load_workbook
from datetime import datetime

export_bp = Blueprint("export", __name__)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

customer_service = CustomerService()

def normalize_gender(gender_value):
    """
    Normalize gender values to match the expected format (M, V, O).
    Handles common variations and case differences.
    """
    if not gender_value or not str(gender_value).strip():
        return None

    gender_str = str(gender_value).strip().lower()

    # Map common gender variations
    gender_mapping = {
        'm': 'M',
        'v': 'V',
        'o': 'O',
        'man': 'M',
        'vrouw': 'V',
        'anders': 'O',
        'onbekend': 'O',
        'male': 'M',
        'female': 'V',
        'other': 'O',
        'unknown': 'O'
    }

    return gender_mapping.get(gender_str, gender_str.upper() if len(gender_str) == 1 else None)

@export_bp.route("/customers/csv", methods=["GET"])
@token_required
@role_required("administrator")
@rate_limit("60/minute")
def export_customers_csv():
    """
    Export all customers to a CSV file.

    Returns:
        A CSV file containing all customers.
    """
    try:
        # Get all customers
        customers, _ = customer_service.get_all_customers()

        if not customers:
            return jsonify({"error": "No customers found"}), 404

        # Create a CSV file in memory
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header row
        writer.writerow([
            "ID", "Name", "Code", "KVK Number", "Address", "Postal Code",
            "City", "Country", "Phone", "Email", "Website",
            "Contact Person", "Gender", "Notes", "Created At"
        ])

        # Write data rows
        for customer in customers:
            writer.writerow([
                customer.get("id", ""),
                customer.get("name", ""),
                customer.get("code", ""),
                customer.get("kvk_number", ""),
                customer.get("address", ""),
                customer.get("postal_code", ""),
                customer.get("city", ""),
                customer.get("country", ""),
                customer.get("phone", ""),
                customer.get("email", ""),
                customer.get("website", ""),
                customer.get("contact_person", ""),
                customer.get("gender", ""),
                customer.get("notes", ""),
                customer.get("created_at", "")
            ])

        # Prepare the output
        output.seek(0)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"customers_export_{timestamp}.csv"

        # Log the export
        logger.info(f"Exported {len(customers)} customers to CSV by user {request.current_user.id}")

        # Return the CSV file
        return send_file(
            io.BytesIO(output.getvalue().encode('utf-8')),
            mimetype="text/csv",
            as_attachment=True,
            download_name=filename
        )
    except Exception as e:
        logger.error(f"Failed to export customers to CSV: {str(e)}")
        return jsonify({"error": str(e)}), 500

@export_bp.route("/customers/json", methods=["GET"])
@token_required
@role_required("administrator")
@rate_limit("60/minute")
def export_customers_json():
    """
    Export all customers to a JSON file.

    Returns:
        A JSON file containing all customers.
    """
    try:
        # Get all customers
        customers, _ = customer_service.get_all_customers()

        if not customers:
            return jsonify({"error": "No customers found"}), 404

        # Prepare the output
        output = json.dumps(customers, indent=2)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"customers_export_{timestamp}.json"

        # Log the export
        logger.info(f"Exported {len(customers)} customers to JSON by user {request.current_user.id}")

        # Return the JSON file
        return send_file(
            io.BytesIO(output.encode('utf-8')),
            mimetype="application/json",
            as_attachment=True,
            download_name=filename
        )
    except Exception as e:
        logger.error(f"Failed to export customers to JSON: {str(e)}")
        return jsonify({"error": str(e)}), 500

@export_bp.route("/customers/excel", methods=["GET"])
@token_required
@role_required("administrator")
@rate_limit("60/minute")
def export_customers_excel():
    """
    Export all customers to an Excel file.

    Returns:
        An Excel file containing all customers.
    """
    try:
        # Get all customers
        customers, _ = customer_service.get_all_customers()

        if not customers:
            return jsonify({"error": "No customers found"}), 404

        # Create a pandas DataFrame
        df = pd.DataFrame(customers)

        # Rename columns to match the import format
        column_mapping = {
            "id": "ID",
            "name": "Name",
            "code": "Code",
            "kvk_number": "KVK Number",
            "address": "Address",
            "postal_code": "Postal Code",
            "city": "City",
            "country": "Country",
            "phone": "Phone",
            "email": "Email",
            "website": "Website",
            "contact_person": "Contact Person",
            "gender": "Gender",
            "notes": "Notes",
            "created_at": "Created At"
        }

        # Rename columns that exist in the DataFrame
        existing_columns = [col for col in column_mapping.keys() if col in df.columns]
        df = df.rename(columns={col: column_mapping[col] for col in existing_columns})

        # Create a temporary file to save the Excel file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        temp_file.close()

        try:
            # Save the DataFrame to an Excel file
            df.to_excel(temp_file.name, index=False, engine='openpyxl')

            # Prepare the filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"customers_export_{timestamp}.xlsx"

            # Log the export
            logger.info(f"Exported {len(customers)} customers to Excel by user {request.current_user.id}")

            # Return the Excel file
            return send_file(
                temp_file.name,
                mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                as_attachment=True,
                download_name=filename
            )
        finally:
            # Schedule the temporary file for deletion
            # Note: We can't delete it immediately because it's being sent to the client
            @request.after_this_request
            def remove_file(response):
                try:
                    os.unlink(temp_file.name)
                except Exception as e:
                    logger.warning(f"Failed to delete temporary file: {str(e)}")
                return response

    except Exception as e:
        logger.error(f"Failed to export customers to Excel: {str(e)}")
        return jsonify({"error": str(e)}), 500

@export_bp.route("/customers/import", methods=["POST"])
@token_required
@role_required("administrator")
@rate_limit("60/minute")
def import_customers():
    """
    Import customers from a CSV, JSON, or Excel file (.xlsx, .xls).

    Returns:
        A message indicating the number of customers imported.
    """
    try:
        if 'file' not in request.files:
            return jsonify({"error": "No file provided"}), 400

        file = request.files['file']
        if not file.filename or file.filename.strip() == '':
            return jsonify({"error": "No file selected"}), 400

        # Check file extension
        file_ext = os.path.splitext(file.filename)[1].lower()

        imported_count = 0
        failed_count = 0

        if file_ext == '.csv':
            # Process CSV file
            stream = io.StringIO(file.stream.read().decode("utf-8"))
            csv_reader = csv.DictReader(stream)

            for row in csv_reader:
                try:
                    # Map CSV fields to customer fields
                    customer_data = {
                        "name": row.get("Name", ""),
                        "code": row.get("Code", ""),
                        "kvk_number": row.get("KVK Number", ""),
                        "address": row.get("Address", ""),
                        "postal_code": row.get("Postal Code", ""),
                        "city": row.get("City", ""),
                        "country": row.get("Country", ""),
                        "phone": row.get("Phone", ""),
                        "email": row.get("Email", ""),
                        "website": row.get("Website", ""),
                        "contact_person": row.get("Contact Person", ""),
                        "gender": normalize_gender(row.get("Gender", "")),
                        "notes": row.get("Notes", "")
                    }

                    # Create customer
                    customer_service.create_customer(customer_data)
                    imported_count += 1
                except Exception as e:
                    logger.error(f"Failed to import customer from CSV: {str(e)}")
                    failed_count += 1

        elif file_ext == '.json':
            # Process JSON file
            data = json.loads(file.read().decode("utf-8"))

            if isinstance(data, list):
                for item in data:
                    try:
                        # Map JSON fields to customer fields
                        customer_data = {
                            "name": item.get("name", ""),
                            "code": item.get("code", ""),
                            "kvk_number": item.get("kvk_number", ""),
                            "address": item.get("address", ""),
                            "postal_code": item.get("postal_code", ""),
                            "city": item.get("city", ""),
                            "country": item.get("country", ""),
                            "phone": item.get("phone", ""),
                            "email": item.get("email", ""),
                            "website": item.get("website", ""),
                            "contact_person": item.get("contact_person", ""),
                            "gender": normalize_gender(item.get("gender", "")),
                            "notes": item.get("notes", "")
                        }

                        # Create customer
                        customer_service.create_customer(customer_data)
                        imported_count += 1
                    except Exception as e:
                        logger.error(f"Failed to import customer from JSON: {str(e)}")
                        failed_count += 1
            else:
                return jsonify({"error": "Invalid JSON format. Expected an array of customers."}), 400

        elif file_ext in ['.xlsx', '.xls']:
            # Process Excel file
            try:
                # Save the file to a temporary location
                temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=file_ext)
                file.save(temp_file.name)
                temp_file.close()

                # Method 1: Using pandas
                try:
                    # Try using pandas first (handles both .xls and .xlsx)
                    # For AMSPM format, we need to handle it specially
                    # First, let's check if this is the AMSPM format by looking at the first few rows
                    try:
                        preview_df = pd.read_excel(temp_file.name, nrows=10)
                        # Check if this looks like the AMSPM format (has 'AM Security' in first row)
                        is_amspm_format = False
                        for i in range(min(3, len(preview_df))):
                            for col in preview_df.columns:
                                cell_value = preview_df.iloc[i, preview_df.columns.get_loc(col)]
                                if isinstance(cell_value, str) and 'AM Security' in cell_value:
                                    is_amspm_format = True
                                    break
                            if is_amspm_format:
                                break

                        if is_amspm_format:
                            logger.info("Detected AMSPM format Excel file")
                            # For AMSPM format, read the file again with specific settings
                            # First, find where the actual data starts
                            data_start_row = None
                            header_row = None

                            for i in range(len(preview_df)):
                                row = preview_df.iloc[i]
                                # Check if this row contains headers like 'Datum', 'Code', 'Naam'
                                if any(col for col in row if isinstance(col, str) and col in ['Datum', 'Code', 'Naam']):
                                    header_row = i
                                    data_start_row = i + 1
                                    break

                            if header_row is not None:
                                # Read the file again with the correct header row
                                df = pd.read_excel(temp_file.name, header=header_row)
                                logger.info(f"Using header row {header_row}, data starts at row {data_start_row}")
                            else:
                                # Fallback to default behavior
                                df = pd.read_excel(temp_file.name)
                                logger.warning("Could not detect header row in AMSPM format, using default")
                        else:
                            # Standard format, just read normally
                            df = pd.read_excel(temp_file.name)
                    except Exception as e:
                        logger.warning(f"Error during format detection: {str(e)}")
                        # Fallback to default behavior
                        df = pd.read_excel(temp_file.name)

                    # Map Dutch column names to English field names
                    dutch_to_english = {
                        'Datum': 'date',
                        'Code': 'code',
                        'Naam': 'name',
                        'KvKnr': 'kvk_number',
                        'Contactpersoon': 'contact_person',
                        'Geslacht': 'gender',
                        'Aanhef': 'salutation',
                        'Adres': 'address',
                        'Postcode': 'postal_code',
                        'Plaats': 'city',
                        'Land': 'country',
                        'Telefoon': 'phone',
                        'Mobiel': 'mobile',
                        'Email': 'email',
                        'Website': 'website',
                        'BTW-nummer': 'vat_number',
                        'IBAN': 'iban',
                        'BIC': 'bic'
                    }

                    # Check if the first row contains headers
                    first_row = df.iloc[0] if not df.empty else None

                    if first_row is not None and isinstance(first_row[0], str) and first_row[0] in dutch_to_english:
                        # Use the first row as headers
                        headers = [str(val) if not pd.isna(val) else f'column_{i}' for i, val in enumerate(first_row)]
                        df = df.iloc[1:].reset_index(drop=True)  # Skip the header row
                        df.columns = headers
                        logger.info(f"Using first row as headers: {headers}")
                    else:
                        # Assuming columns are: Datum, Code, Naam, KvKnr, Contactpersoon, etc.
                        default_headers = ['Datum', 'Code', 'Naam', 'KvKnr', 'Contactpersoon']
                        if len(df.columns) >= len(default_headers):
                            headers = default_headers + [f'column_{i}' for i in range(len(default_headers), len(df.columns))]
                            df.columns = headers

                    # Convert DataFrame to dict records
                    records = df.to_dict('records')

                    for row in records:
                        try:
                            # Skip empty rows or header rows
                            if pd.isna(row.get('Naam')) or not row.get('Naam') or \
                               isinstance(row.get('Naam'), str) and row.get('Naam').lower() == 'naam':
                                continue

                            # Log the row for debugging
                            logger.info(f"Processing row: {row}")

                            # Map Excel fields to customer fields using Dutch column names
                            # Also handle column_X fields for unmapped columns
                            customer_data = {
                                "name": row.get("Naam", ""),
                                "code": row.get("Code", ""),
                                "kvk_number": str(row.get("KvKnr", "")).replace(".0", "") if row.get("KvKnr") else "",
                                "contact_person": row.get("Contactpersoon", ""),
                                # Try to get address from Adres or column_7 (common location in the Excel file)
                                "address": row.get("Adres", row.get("column_7", "")),
                                # Try to get postal code from Postcode or column_8
                                "postal_code": row.get("Postcode", row.get("column_8", "")),
                                # Try to get city from Plaats or column_9
                                "city": row.get("Plaats", row.get("column_9", "")),
                                # Try to get country from Land or column_10, default to Netherlands
                                "country": row.get("Land", row.get("column_10", "Netherlands")),
                                # Try to get phone from Telefoon or column_15
                                "phone": row.get("Telefoon", row.get("column_15", "")),
                                # Try to get email from Email or column_18
                                "email": row.get("Email", row.get("column_18", "")),
                                # Try to get website from Website or column_21
                                "website": row.get("Website", row.get("column_21", "")),
                                # Try to get gender from Geslacht or column_5
                                "gender": normalize_gender(row.get("Geslacht", row.get("column_5", "")))
                                # Removed notes field as it's not supported in the Customer model
                            }

                            # Log the customer data for debugging
                            logger.info(f"Created customer data: {customer_data}")

                            # Create customer
                            customer_service.create_customer(customer_data)
                            imported_count += 1
                        except Exception as e:
                            logger.error(f"Failed to import customer from Excel: {str(e)}")
                            failed_count += 1

                except Exception as pandas_error:
                    # Method 2: Fallback to openpyxl for .xlsx files
                    logger.warning(f"Pandas import failed, trying openpyxl: {str(pandas_error)}")

                    if file_ext == '.xlsx':
                        workbook = load_workbook(filename=temp_file.name)
                        worksheet = workbook.active

                        # Skip header rows (first 9 rows based on the example)
                        data_start_row = 10

                        # Get headers from row 9 (assuming this is the header row)
                        header_row = 9
                        headers = [cell.value for cell in worksheet[header_row]]

                        # Map headers to expected fields
                        header_mapping = {}
                        dutch_to_english = {
                            'Datum': 'date',
                            'Code': 'code',
                            'Naam': 'name',
                            'KvKnr': 'kvk_number',
                            'Contactpersoon': 'contact_person',
                            'Geslacht': 'gender',
                            'Aanhef': 'salutation',
                            'Adres': 'address',
                            'Postcode': 'postal_code',
                            'Plaats': 'city',
                            'Land': 'country',
                            'Telefoon': 'phone',
                            'Mobiel': 'mobile',
                            'Email': 'email',
                            'Website': 'website',
                            'BTW-nummer': 'vat_number',
                            'IBAN': 'iban',
                            'BIC': 'bic'
                        }

                        for i, header in enumerate(headers):
                            if header and isinstance(header, str):
                                # Direct mapping if header exists in our dictionary
                                if header in dutch_to_english:
                                    header_mapping[i] = dutch_to_english[header]
                                else:
                                    # Fallback to partial matching
                                    header_lower = header.lower()
                                    if 'datum' in header_lower:
                                        header_mapping[i] = 'date'
                                    elif 'code' in header_lower:
                                        header_mapping[i] = 'code'
                                    elif 'naam' in header_lower:
                                        header_mapping[i] = 'name'
                                    elif 'kvk' in header_lower:
                                        header_mapping[i] = 'kvk_number'
                                    elif 'contact' in header_lower:
                                        header_mapping[i] = 'contact_person'

                        # Process each row
                        for row_idx, row in enumerate(worksheet.iter_rows(min_row=data_start_row), start=data_start_row):
                            try:
                                # Create a dictionary from the row values using our mapping
                                row_data = {}
                                for i, cell in enumerate(row):
                                    if i in header_mapping:
                                        row_data[header_mapping[i]] = cell.value

                                # Skip empty rows - check both English and Dutch field names
                                if (not row_data.get('name') and not row_data.get('Naam')) or \
                                   (isinstance(row_data.get('name'), str) and row_data.get('name').lower() == 'naam') or \
                                   (isinstance(row_data.get('Naam'), str) and row_data.get('Naam').lower() == 'naam'):
                                    continue

                                # Map Excel fields to customer fields
                                # Handle both English and Dutch field names
                                customer_data = {
                                    "name": row_data.get("name", row_data.get("Naam", "")),
                                    "code": row_data.get("code", row_data.get("Code", "")),
                                    "kvk_number": str(row_data.get("kvk_number", row_data.get("KvKnr", ""))).replace(".0", "") if row_data.get("kvk_number") or row_data.get("KvKnr") else "",
                                    "contact_person": row_data.get("contact_person", row_data.get("Contactpersoon", "")),
                                    "address": row_data.get("address", row_data.get("Adres", "")),
                                    "postal_code": row_data.get("postal_code", row_data.get("Postcode", "")),
                                    "city": row_data.get("city", row_data.get("Plaats", "")),
                                    "country": row_data.get("country", row_data.get("Land", "Netherlands")),
                                    "phone": row_data.get("phone", row_data.get("Telefoon", "")),
                                    "email": row_data.get("email", row_data.get("Email", "")),
                                    "website": row_data.get("website", row_data.get("Website", "")),
                                    "gender": normalize_gender(row_data.get("gender", row_data.get("Geslacht", "")))
                                    # Removed notes field as it's not supported in the Customer model
                                }

                                # Create customer
                                customer_service.create_customer(customer_data)
                                imported_count += 1
                            except Exception as e:
                                logger.error(f"Failed to import customer from Excel row {row_idx}: {str(e)}")
                                failed_count += 1
                    else:
                        logger.error("Fallback to openpyxl failed: .xls files are not supported by openpyxl")
                        return jsonify({"error": "Failed to process .xls file. Please convert to .xlsx or .csv format."}), 400

                finally:
                    # Clean up the temporary file
                    try:
                        os.unlink(temp_file.name)
                    except Exception as e:
                        logger.warning(f"Failed to delete temporary file: {str(e)}")

            except Exception as e:
                logger.error(f"Failed to process Excel file: {str(e)}")
                return jsonify({"error": f"Failed to process Excel file: {str(e)}"}), 400

        else:
            return jsonify({"error": "Unsupported file format. Please upload a CSV, JSON, or Excel file (.xlsx, .xls)."}), 400

        # Log the import
        logger.info(f"Imported {imported_count} customers (failed: {failed_count}) by user {request.current_user.id}")

        return jsonify({
            "message": f"Successfully imported {imported_count} customers. Failed: {failed_count}.",
            "imported": imported_count,
            "failed": failed_count
        }), 200

    except Exception as e:
        logger.error(f"Failed to import customers: {str(e)}")
        return jsonify({"error": str(e)}), 500
