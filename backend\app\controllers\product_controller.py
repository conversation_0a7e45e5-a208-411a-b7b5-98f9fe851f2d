"""
Product controller module.
This module provides API endpoints for products.
"""
from flask import Blueprint, request, jsonify, send_file
from app.services.product_service import ProductService
from app.utils.security import token_required, role_required, roles_required
from app.utils.rate_limit import rate_limit
from app.schemas.product_schema import product_schema
import logging
from marshmallow import ValidationError
import os

# Define the blueprint for product-related routes
product_bp = Blueprint("product", __name__)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize the product service
product_service = ProductService()

@product_bp.route("", methods=["GET"], strict_slashes=False)
@roles_required("administrator", "verkoper")
@rate_limit("60/minute")
def get_all_products():
    """
    Get all products with pagination.

    Returns:
        JSON: List of products and pagination info.
    """
    try:
        page = int(request.args.get("page", 1))
        per_page = int(request.args.get("per_page", 20))

        products, total = product_service.get_all_products(page, per_page)

        return jsonify({
            "products": products,
            "total": total,
            "page": page,
            "per_page": per_page
        }), 200
    except Exception as e:
        logger.error(f"Failed to fetch products: {str(e)}")
        return jsonify({"error": str(e)}), 500

@product_bp.route("/<int:product_id>", methods=["GET"])
@roles_required("administrator", "verkoper")
@rate_limit("60/minute")
def get_product(product_id):
    """
    Get a product by ID.

    Args:
        product_id: Product ID

    Returns:
        JSON: Product data.
    """
    try:
        product = product_service.get_product_by_id(product_id)
        return jsonify(product), 200
    except Exception as e:
        logger.error(f"Failed to fetch product {product_id}: {str(e)}")
        return jsonify({"error": str(e)}), 404

@product_bp.route("/search", methods=["GET"])
@roles_required("administrator", "verkoper")
@rate_limit("60/minute")
def search_products():
    """
    Search products by name, description, or product code.

    Returns:
        JSON: List of matching products and pagination info.
    """
    try:
        search_term = request.args.get("q", "")
        page = int(request.args.get("page", 1))
        per_page = int(request.args.get("per_page", 20))
        category = request.args.get("category")

        products, total = product_service.search_products(search_term, page, per_page, category)

        return jsonify({
            "products": products,
            "total": total,
            "page": page,
            "per_page": per_page
        }), 200
    except Exception as e:
        logger.error(f"Failed to search products: {str(e)}")
        return jsonify({"error": str(e)}), 500

@product_bp.route("/categories", methods=["GET"])
@roles_required("administrator", "verkoper")
@rate_limit("60/minute")
def get_categories():
    """
    Get all unique product categories.

    Returns:
        JSON: List of categories.
    """
    try:
        categories = product_service.get_all_categories()

        return jsonify({
            "categories": categories
        }), 200
    except Exception as e:
        logger.error(f"Failed to get categories: {str(e)}")
        return jsonify({"error": str(e)}), 500

@product_bp.route("/category/<string:category>", methods=["GET"])
@roles_required("administrator", "verkoper")
@rate_limit("60/minute")
def get_products_by_category(category):
    """
    Get products by category.

    Args:
        category: Category to filter by

    Returns:
        JSON: List of products in the category and pagination info.
    """
    try:
        page = int(request.args.get("page", 1))
        per_page = int(request.args.get("per_page", 20))

        products, total = product_service.get_products_by_category(category, page, per_page)

        return jsonify({
            "products": products,
            "total": total,
            "page": page,
            "per_page": per_page,
            "category": category
        }), 200
    except Exception as e:
        logger.error(f"Failed to get products by category: {str(e)}")
        return jsonify({"error": str(e)}), 500

@product_bp.route("", methods=["POST"])
@role_required("administrator")
@rate_limit("60/minute")
def create_product():
    """
    Create a new product.

    Returns:
        JSON: Created product data.
    """
    try:
        product_data = request.json

        # Validate with schema
        errors = product_schema.validate(product_data)
        if errors:
            return jsonify({"error": "Validation failed", "details": errors}), 400

        # Create product
        product = product_service.create_product(product_data)

        return jsonify(product), 201
    except ValidationError as e:
        logger.warning(f"Product validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to create product: {str(e)}")
        return jsonify({"error": str(e)}), 500

@product_bp.route("/<int:product_id>", methods=["PUT"])
@role_required("administrator")
@rate_limit("60/minute")
def update_product(product_id):
    """
    Update a product.

    Args:
        product_id: Product ID

    Returns:
        JSON: Updated product data.
    """
    try:
        product_data = request.json

        # Validate with schema
        errors = product_schema.validate(product_data)
        if errors:
            return jsonify({"error": "Validation failed", "details": errors}), 400

        # Update product
        product = product_service.update_product(product_id, product_data)

        return jsonify(product), 200
    except ValidationError as e:
        logger.warning(f"Product validation error: {e.messages}")
        return jsonify({"error": "Validation error", "details": e.messages}), 400
    except Exception as e:
        logger.error(f"Failed to update product {product_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@product_bp.route("/<int:product_id>", methods=["DELETE"])
@role_required("administrator")
@rate_limit("60/minute")
def delete_product(product_id):
    """
    Delete a product.

    Args:
        product_id: Product ID

    Returns:
        JSON: Success message.
    """
    try:
        success = product_service.delete_product(product_id)

        if success:
            return jsonify({"message": "Product deleted successfully"}), 200
        else:
            return jsonify({"error": "Product not found"}), 404
    except Exception as e:
        logger.error(f"Failed to delete product {product_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@product_bp.route("/import", methods=["POST"])
@role_required("administrator")
@rate_limit("60/minute")
def import_products():
    """
    Import products from an Excel file.

    Returns:
        JSON: Import results.
    """
    try:
        # Check if file is provided
        if "file" not in request.files:
            return jsonify({"error": "No file part in the request"}), 400

        file = request.files["file"]

        # Check if file is empty
        if not file.filename or file.filename.strip() == "":
            return jsonify({"error": "No file selected"}), 400

        # Check file extension
        if not file.filename.endswith((".xlsx", ".xls")):
            return jsonify({"error": "Only Excel files (.xlsx, .xls) are allowed"}), 400

        # Import products
        result = product_service.import_products_from_excel(file)

        return jsonify(result), 200
    except Exception as e:
        logger.error(f"Failed to import products: {str(e)}")
        return jsonify({"error": str(e)}), 500

@product_bp.route("/export", methods=["GET"])
@role_required("administrator")
@rate_limit("60/minute")
def export_products():
    """
    Export all products to an Excel file.

    Returns:
        Excel file: All products.
    """
    try:
        # Export products
        file_path = product_service.export_products_to_excel()

        # Prepare the filename
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"products_export_{timestamp}.xlsx"

        # Return the Excel file
        return send_file(
            file_path,
            mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            as_attachment=True,
            download_name=filename
        )
    except Exception as e:
        logger.error(f"Failed to export products: {str(e)}")
        return jsonify({"error": str(e)}), 500

@product_bp.route("/delete-all", methods=["DELETE"])
@role_required("administrator")
@rate_limit("10/minute")
def delete_all_products():
    """
    Delete all products.

    Returns:
        JSON: Success message with count of deleted products.
    """
    try:
        count = product_service.delete_all_products()

        return jsonify({
            "message": f"Successfully deleted {count} products",
            "count": count
        }), 200
    except Exception as e:
        logger.error(f"Failed to delete all products: {str(e)}")
        return jsonify({"error": str(e)}), 500

@product_bp.route("/category/<string:category>/delete", methods=["DELETE"])
@role_required("administrator")
@rate_limit("20/minute")
def delete_products_by_category(category):
    """
    Delete all products in a specific category.

    Args:
        category: Category to delete products from

    Returns:
        JSON: Success message with count of deleted products.
    """
    try:
        count = product_service.delete_products_by_category(category)

        return jsonify({
            "message": f"Successfully deleted {count} products in category '{category}'",
            "count": count,
            "category": category
        }), 200
    except Exception as e:
        logger.error(f"Failed to delete products in category {category}: {str(e)}")
        return jsonify({"error": str(e)}), 500
