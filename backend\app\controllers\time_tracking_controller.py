"""
Time Tracking controller module.
This module defines the API endpoints for time and mileage entry operations.
"""
from flask import Blueprint, request, jsonify
from app.services.time_tracking_service import TimeTrackingService
from app.schemas.time_entry_schema import TimeEntrySchema, TimeEntryCreateSchema, TimeEntryUpdateSchema, TimeEntryApprovalSchema
from app.schemas.mileage_entry_schema import MileageEntrySchema, MileageEntryCreateSchema, MileageEntryUpdateSchema, MileageEntryApprovalSchema
from app.utils.security import token_required, role_required, roles_required
from app.utils.rate_limit import rate_limit
from app.utils.cache_decorators import cached_list, cached_detail
from marshmallow import ValidationError
import logging

time_tracking_bp = Blueprint("time_tracking", __name__)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

time_tracking_service = TimeTrackingService()

# Time Entry Endpoints

@time_tracking_bp.route("/time-entries", methods=["GET"])
@token_required
@rate_limit("120/minute")  # Verhoogd van 60 naar 120 per minuut
@cached_list(entity_type="time_entries", timeout=60)  # Cache for 1 minute
def get_time_entries():
    """
    Get time entries with optional filtering.

    Query parameters:
        page: The page number.
        per_page: The number of items per page.
        user_id: Filter by user ID.
        month: Filter by month.
        year: Filter by year.
        status: Filter by status.

    Returns:
        A list of time entries.
    """
    try:
        page = request.args.get("page", 1, type=int)
        per_page = request.args.get("per_page", 20, type=int)
        user_id = request.args.get("user_id", type=int)
        month = request.args.get("month", type=int)
        year = request.args.get("year", type=int)
        status = request.args.get("status")

        # Check if the current user is requesting their own entries
        current_user = request.current_user

        # If not an administrator, only allow access to own entries
        if current_user.role != "administrator" and user_id != current_user.id:
            user_id = current_user.id

        # If month and year are provided, get entries for that month and year
        if month and year:
            if user_id:
                result = time_tracking_service.get_time_entries_by_month_year(month, year, user_id, page, per_page)
            else:
                # Only administrators can view all entries for a month/year
                if current_user.role != "administrator":
                    return jsonify({"error": "Unauthorized"}), 403
                result = time_tracking_service.get_time_entries_by_month_year(month, year, None, page, per_page)
        # If user_id is provided, get entries for that user
        elif user_id:
            result = time_tracking_service.get_time_entries_by_user(user_id, page, per_page)
        # If status is provided, get entries with that status (admin only)
        elif status:
            if current_user.role != "administrator":
                return jsonify({"error": "Unauthorized"}), 403
            if status == "pending":
                result = time_tracking_service.get_pending_time_entries(page, per_page)
            else:
                return jsonify({"error": "Invalid status"}), 400
        # Otherwise, get all entries (admin only)
        else:
            if current_user.role != "administrator":
                return jsonify({"error": "Unauthorized"}), 403
            result = time_tracking_service.get_time_entries(page, per_page)

        return jsonify(result), 200
    except Exception as e:
        logger.error(f"Failed to get time entries: {str(e)}")
        return jsonify({"error": str(e)}), 500

@time_tracking_bp.route("/time-entries/<int:entry_id>", methods=["GET"])
@token_required
@rate_limit("60/minute")
@cached_detail(entity_type="time_entry", timeout=60)  # Cache for 1 minute
def get_time_entry(entry_id):
    """
    Get a time entry by ID.

    Path parameters:
        entry_id: The ID of the time entry.

    Returns:
        The time entry.
    """
    try:
        # Get the entry
        entry = time_tracking_service.get_time_entry_by_id(entry_id)

        # Check if the current user is authorized to view this entry
        current_user = request.current_user
        if current_user.role != "administrator" and entry["user_id"] != current_user.id:
            return jsonify({"error": "Unauthorized"}), 403

        return jsonify(entry), 200
    except Exception as e:
        logger.error(f"Failed to get time entry {entry_id}: {str(e)}")
        return jsonify({"error": str(e)}), 404

@time_tracking_bp.route("/time-entries", methods=["POST"])
@token_required
@rate_limit("60/minute")
def create_time_entry():
    """
    Create a new time entry.

    Request body:
        user_id: The ID of the user.
        date: The date of the entry (YYYY-MM-DD).
        start_time: The start time of the entry (HH:MM).
        end_time: The end time of the entry (HH:MM).
        description: Optional description of the entry.

    Returns:
        The created time entry.
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Validate the data
        schema = TimeEntryCreateSchema()
        try:
            validated_data = schema.load(data)
        except ValidationError as e:
            return jsonify({"error": "Validation error", "details": e.messages}), 400

        # Check if the current user is authorized to create this entry
        current_user = request.current_user
        if current_user.role != "administrator" and validated_data["user_id"] != current_user.id:
            return jsonify({"error": "Unauthorized"}), 403

        # Create the entry
        entry = time_tracking_service.create_time_entry(
            user_id=validated_data["user_id"],
            date_str=validated_data["date"].isoformat(),
            start_time_str=validated_data["start_time"].isoformat(timespec="minutes"),
            end_time_str=validated_data["end_time"].isoformat(timespec="minutes"),
            break_time=validated_data.get("break_time", 0),
            description=validated_data.get("description"),
            current_user_id=current_user.id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get("User-Agent")
        )

        return jsonify(entry), 201
    except Exception as e:
        logger.error(f"Failed to create time entry: {str(e)}")
        return jsonify({"error": str(e)}), 500

@time_tracking_bp.route("/time-entries/<int:entry_id>", methods=["PUT"])
@token_required
@rate_limit("60/minute")
def update_time_entry(entry_id):
    """
    Update a time entry.

    Path parameters:
        entry_id: The ID of the time entry.

    Request body:
        date: Optional new date of the entry (YYYY-MM-DD).
        start_time: Optional new start time of the entry (HH:MM).
        end_time: Optional new end time of the entry (HH:MM).
        description: Optional new description of the entry.

    Returns:
        The updated time entry.
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Validate the data
        schema = TimeEntryUpdateSchema()
        try:
            validated_data = schema.load(data)
        except ValidationError as e:
            return jsonify({"error": "Validation error", "details": e.messages}), 400

        # Get the entry to check authorization
        entry = time_tracking_service.get_time_entry_by_id(entry_id)

        # Check if the current user is authorized to update this entry
        current_user = request.current_user
        if current_user.role != "administrator" and entry["user_id"] != current_user.id:
            return jsonify({"error": "Unauthorized"}), 403

        # Update the entry
        updated_entry = time_tracking_service.update_time_entry(
            entry_id=entry_id,
            date_str=validated_data["date"].isoformat() if "date" in validated_data else None,
            start_time_str=validated_data["start_time"].isoformat(timespec="minutes") if "start_time" in validated_data else None,
            end_time_str=validated_data["end_time"].isoformat(timespec="minutes") if "end_time" in validated_data else None,
            break_time=validated_data.get("break_time"),
            description=validated_data.get("description"),
            current_user_id=current_user.id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get("User-Agent")
        )

        return jsonify(updated_entry), 200
    except Exception as e:
        logger.error(f"Failed to update time entry {entry_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@time_tracking_bp.route("/time-entries/<int:entry_id>/approve", methods=["POST"])
@role_required("administrator")
@rate_limit("60/minute")
def approve_time_entry(entry_id):
    """
    Approve a time entry.

    Path parameters:
        entry_id: The ID of the time entry.

    Returns:
        The approved time entry.
    """
    try:
        # Get the current user
        current_user = request.current_user

        # Approve the entry
        entry = time_tracking_service.approve_time_entry(
            entry_id=entry_id,
            approver_id=current_user.id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get("User-Agent")
        )

        return jsonify(entry), 200
    except Exception as e:
        logger.error(f"Failed to approve time entry {entry_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@time_tracking_bp.route("/time-entries/<int:entry_id>/reject", methods=["POST"])
@role_required("administrator")
@rate_limit("60/minute")
def reject_time_entry(entry_id):
    """
    Reject a time entry.

    Path parameters:
        entry_id: The ID of the time entry.

    Returns:
        The rejected time entry.
    """
    try:
        # Get the current user
        current_user = request.current_user

        # Reject the entry
        entry = time_tracking_service.reject_time_entry(
            entry_id=entry_id,
            approver_id=current_user.id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get("User-Agent")
        )

        return jsonify(entry), 200
    except Exception as e:
        logger.error(f"Failed to reject time entry {entry_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@time_tracking_bp.route("/time-entries/check-overlap", methods=["GET"])
@token_required
@rate_limit("60/minute")
def check_time_entry_overlap():
    """
    Check if a time entry overlaps with existing entries.

    Query parameters:
        user_id: The ID of the user.
        date: The date of the entry (YYYY-MM-DD).
        start_time: The start time of the entry (HH:MM).
        end_time: The end time of the entry (HH:MM).
        exclude_entry_id: Optional ID of the entry to exclude from the check.

    Returns:
        JSON with overlaps flag and list of overlapping entries.
    """
    try:
        # Get query parameters
        user_id = request.args.get("user_id", type=int)
        date_str = request.args.get("date")
        start_time_str = request.args.get("start_time")
        end_time_str = request.args.get("end_time")
        exclude_entry_id = request.args.get("exclude_entry_id", type=int)

        if not all([user_id, date_str, start_time_str, end_time_str]):
            return jsonify({"error": "Missing required parameters"}), 400

        # Check for overlaps
        result = time_tracking_service.check_time_entry_overlap(
            user_id=user_id,
            date_str=date_str,
            start_time_str=start_time_str,
            end_time_str=end_time_str,
            exclude_entry_id=exclude_entry_id
        )

        return jsonify(result), 200
    except Exception as e:
        logger.error(f"Failed to check time entry overlap: {str(e)}")
        return jsonify({"error": str(e)}), 500

@time_tracking_bp.route("/time-entries/<int:entry_id>", methods=["DELETE"])
@token_required
@rate_limit("60/minute")
def delete_time_entry(entry_id):
    """
    Delete a time entry.

    Path parameters:
        entry_id: The ID of the time entry.

    Returns:
        A success message.
    """
    try:
        # Get the entry to check authorization
        entry = time_tracking_service.get_time_entry_by_id(entry_id)

        # Check if the current user is authorized to delete this entry
        current_user = request.current_user
        if current_user.role != "administrator" and entry["user_id"] != current_user.id:
            return jsonify({"error": "Unauthorized"}), 403

        # Delete the entry
        result = time_tracking_service.delete_time_entry(
            entry_id=entry_id,
            current_user_id=current_user.id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get("User-Agent")
        )

        return jsonify(result), 200
    except Exception as e:
        logger.error(f"Failed to delete time entry {entry_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

# Mileage Entry Endpoints

@time_tracking_bp.route("/mileage-entries", methods=["GET"])
@token_required
@rate_limit("60/minute")
@cached_list(entity_type="mileage_entries", timeout=60)  # Cache for 1 minute
def get_mileage_entries():
    """
    Get mileage entries with optional filtering.

    Query parameters:
        page: The page number.
        per_page: The number of items per page.
        user_id: Filter by user ID.
        month: Filter by month.
        year: Filter by year.
        status: Filter by status.

    Returns:
        A list of mileage entries.
    """
    try:
        page = request.args.get("page", 1, type=int)
        per_page = request.args.get("per_page", 20, type=int)
        user_id = request.args.get("user_id", type=int)
        month = request.args.get("month", type=int)
        year = request.args.get("year", type=int)
        status = request.args.get("status")

        # Check if the current user is requesting their own entries
        current_user = request.current_user

        # If not an administrator, only allow access to own entries
        if current_user.role != "administrator" and user_id != current_user.id:
            user_id = current_user.id

        # If month and year are provided, get entries for that month and year
        if month and year:
            if user_id:
                result = time_tracking_service.get_mileage_entries_by_month_year(month, year, user_id, page, per_page)
            else:
                # Only administrators can view all entries for a month/year
                if current_user.role != "administrator":
                    return jsonify({"error": "Unauthorized"}), 403
                result = time_tracking_service.get_mileage_entries_by_month_year(month, year, None, page, per_page)
        # If user_id is provided, get entries for that user
        elif user_id:
            result = time_tracking_service.get_mileage_entries_by_user(user_id, page, per_page)
        # If status is provided, get entries with that status (admin only)
        elif status:
            if current_user.role != "administrator":
                return jsonify({"error": "Unauthorized"}), 403
            if status == "pending":
                result = time_tracking_service.get_pending_mileage_entries(page, per_page)
            else:
                return jsonify({"error": "Invalid status"}), 400
        # Otherwise, get all entries (admin only)
        else:
            if current_user.role != "administrator":
                return jsonify({"error": "Unauthorized"}), 403
            result = time_tracking_service.get_mileage_entries(page, per_page)

        return jsonify(result), 200
    except Exception as e:
        logger.error(f"Failed to get mileage entries: {str(e)}")
        return jsonify({"error": str(e)}), 500

@time_tracking_bp.route("/mileage-entries/<int:entry_id>", methods=["GET"])
@token_required
@rate_limit("60/minute")
@cached_detail(entity_type="mileage_entry", timeout=60)  # Cache for 1 minute
def get_mileage_entry(entry_id):
    """
    Get a mileage entry by ID.

    Path parameters:
        entry_id: The ID of the mileage entry.

    Returns:
        The mileage entry.
    """
    try:
        # Get the entry
        entry = time_tracking_service.get_mileage_entry_by_id(entry_id)

        # Check if the current user is authorized to view this entry
        current_user = request.current_user
        if current_user.role != "administrator" and entry["user_id"] != current_user.id:
            return jsonify({"error": "Unauthorized"}), 403

        return jsonify(entry), 200
    except Exception as e:
        logger.error(f"Failed to get mileage entry {entry_id}: {str(e)}")
        return jsonify({"error": str(e)}), 404

@time_tracking_bp.route("/mileage-entries", methods=["POST"])
@token_required
@rate_limit("60/minute")
def create_mileage_entry():
    """
    Create a new mileage entry.

    Request body:
        user_id: The ID of the user.
        date: The date of the entry (YYYY-MM-DD).
        kilometers: The number of kilometers driven.
        description: Optional description of the entry.

    Returns:
        The created mileage entry.
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Validate the data
        schema = MileageEntryCreateSchema()
        try:
            validated_data = schema.load(data)
        except ValidationError as e:
            return jsonify({"error": "Validation error", "details": e.messages}), 400

        # Check if the current user is authorized to create this entry
        current_user = request.current_user
        if current_user.role != "administrator" and validated_data["user_id"] != current_user.id:
            return jsonify({"error": "Unauthorized"}), 403

        # Create the entry
        entry = time_tracking_service.create_mileage_entry(
            user_id=validated_data["user_id"],
            date_str=validated_data["date"].isoformat(),
            license_plate=validated_data["license_plate"],
            start_odometer=validated_data["start_odometer"],
            end_odometer=validated_data["end_odometer"],
            reason=validated_data["reason"],
            description=validated_data.get("description"),
            current_user_id=current_user.id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get("User-Agent")
        )

        return jsonify(entry), 201
    except Exception as e:
        logger.error(f"Failed to create mileage entry: {str(e)}")
        return jsonify({"error": str(e)}), 500

@time_tracking_bp.route("/mileage-entries/<int:entry_id>", methods=["PUT"])
@token_required
@rate_limit("60/minute")
def update_mileage_entry(entry_id):
    """
    Update a mileage entry.

    Path parameters:
        entry_id: The ID of the mileage entry.

    Request body:
        date: Optional new date of the entry (YYYY-MM-DD).
        kilometers: Optional new number of kilometers driven.
        description: Optional new description of the entry.

    Returns:
        The updated mileage entry.
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        # Validate the data
        schema = MileageEntryUpdateSchema()
        try:
            validated_data = schema.load(data)
        except ValidationError as e:
            return jsonify({"error": "Validation error", "details": e.messages}), 400

        # Get the entry to check authorization
        entry = time_tracking_service.get_mileage_entry_by_id(entry_id)

        # Check if the current user is authorized to update this entry
        current_user = request.current_user
        if current_user.role != "administrator" and entry["user_id"] != current_user.id:
            return jsonify({"error": "Unauthorized"}), 403

        # Update the entry
        updated_entry = time_tracking_service.update_mileage_entry(
            entry_id=entry_id,
            date_str=validated_data["date"].isoformat() if "date" in validated_data else None,
            license_plate=validated_data.get("license_plate"),
            start_odometer=validated_data.get("start_odometer"),
            end_odometer=validated_data.get("end_odometer"),
            reason=validated_data.get("reason"),
            description=validated_data.get("description"),
            current_user_id=current_user.id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get("User-Agent")
        )

        return jsonify(updated_entry), 200
    except Exception as e:
        logger.error(f"Failed to update mileage entry {entry_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@time_tracking_bp.route("/mileage-entries/<int:entry_id>/approve", methods=["POST"])
@role_required("administrator")
@rate_limit("60/minute")
def approve_mileage_entry(entry_id):
    """
    Approve a mileage entry.

    Path parameters:
        entry_id: The ID of the mileage entry.

    Returns:
        The approved mileage entry.
    """
    try:
        # Get the current user
        current_user = request.current_user

        # Approve the entry
        entry = time_tracking_service.approve_mileage_entry(
            entry_id=entry_id,
            approver_id=current_user.id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get("User-Agent")
        )

        return jsonify(entry), 200
    except Exception as e:
        logger.error(f"Failed to approve mileage entry {entry_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@time_tracking_bp.route("/mileage-entries/<int:entry_id>/reject", methods=["POST"])
@role_required("administrator")
@rate_limit("60/minute")
def reject_mileage_entry(entry_id):
    """
    Reject a mileage entry.

    Path parameters:
        entry_id: The ID of the mileage entry.

    Returns:
        The rejected mileage entry.
    """
    try:
        # Get the current user
        current_user = request.current_user

        # Reject the entry
        entry = time_tracking_service.reject_mileage_entry(
            entry_id=entry_id,
            approver_id=current_user.id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get("User-Agent")
        )

        return jsonify(entry), 200
    except Exception as e:
        logger.error(f"Failed to reject mileage entry {entry_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

@time_tracking_bp.route("/mileage-entries/<int:entry_id>", methods=["DELETE"])
@token_required
@rate_limit("60/minute")
def delete_mileage_entry(entry_id):
    """
    Delete a mileage entry.

    Path parameters:
        entry_id: The ID of the mileage entry.

    Returns:
        A success message.
    """
    try:
        # Get the entry to check authorization
        entry = time_tracking_service.get_mileage_entry_by_id(entry_id)

        # Check if the current user is authorized to delete this entry
        current_user = request.current_user
        if current_user.role != "administrator" and entry["user_id"] != current_user.id:
            return jsonify({"error": "Unauthorized"}), 403

        # Delete the entry
        result = time_tracking_service.delete_mileage_entry(
            entry_id=entry_id,
            current_user_id=current_user.id,
            ip_address=request.remote_addr,
            user_agent=request.headers.get("User-Agent")
        )

        return jsonify(result), 200
    except Exception as e:
        logger.error(f"Failed to delete mileage entry {entry_id}: {str(e)}")
        return jsonify({"error": str(e)}), 500

# Combined Endpoints

@time_tracking_bp.route("/monthly-summary/<int:user_id>/<int:year>", methods=["GET"])
@token_required
@rate_limit("60/minute")
@cached_detail(entity_type="monthly_summary", timeout=300)  # Cache for 5 minutes
def get_monthly_summary(user_id, year):
    """
    Get a summary of time and mileage entries for each month of a year for a specific user.

    Path parameters:
        user_id: The ID of the user.
        year: The year to get the summary for.

    Returns:
        A summary of time and mileage entries for each month of the year.
    """
    try:
        # Check if the current user is authorized to view this summary
        current_user = request.current_user
        if current_user.role != "administrator" and user_id != current_user.id:
            return jsonify({"error": "Unauthorized"}), 403

        # Get the summary
        summary = time_tracking_service.get_monthly_summary(user_id, year)

        return jsonify(summary), 200
    except Exception as e:
        logger.error(f"Failed to get monthly summary for user {user_id} and year {year}: {str(e)}")
        return jsonify({"error": str(e)}), 500
