from app import db
from datetime import datetime, timezone

VALID_GENDER_VALUES = ["M", "V", "O"]

# Function kept for backward compatibility but no longer uses encryption
def get_field_type(field_type, length):
    """Helper function kept for backward compatibility"""
    return field_type(length)

class Customer(db.Model):
    __tablename__ = "customers"
    id = db.Column(db.Integer, primary_key=True)
    date = db.Column(db.DateTime, nullable=True)
    code = db.Column(db.String(50), nullable=True)
    name = db.Column(db.String(100), nullable=False)
    kvk_number = db.Column(db.String(20), nullable=True)
    contact_person = db.Column(db.String(100), nullable=True)
    gender = db.Column(db.String(20), nullable=True)  # M (Man), V (Vrouw), O (Anders)
    title = db.Column(db.String(20), nullable=True)  # Aanhef

    # Personal information - no longer encrypted
    address = db.Column(db.String(500), nullable=True)  # Increased size to accommodate previously encrypted data
    postal_code = db.Column(db.String(255), nullable=True)  # Increased size
    city = db.Column(db.String(255), nullable=True)  # Increased size
    country = db.Column(db.String(100), nullable=True)
    address2 = db.Column(db.String(500), nullable=True)  # Increased size
    postal_code2 = db.Column(db.String(255), nullable=True)  # Increased size
    city2 = db.Column(db.String(255), nullable=True)  # Increased size
    country2 = db.Column(db.String(100), nullable=True)

    # Contact information - no longer encrypted
    phone = db.Column(db.String(255), nullable=True)  # Increased size
    mobile = db.Column(db.String(255), nullable=True)  # Increased size
    fax = db.Column(db.String(255), nullable=True)  # Increased size
    email = db.Column(db.String(255), nullable=True)  # Increased size
    invoice_email = db.Column(db.String(255), nullable=True)  # Increased size
    reminder_email = db.Column(db.String(255), nullable=True)  # Increased size
    website = db.Column(db.String(200), nullable=True)

    # Financial information - no longer encrypted
    bank_account = db.Column(db.String(255), nullable=True)  # Increased size
    giro_account = db.Column(db.String(255), nullable=True)  # Increased size
    vat_number = db.Column(db.String(255), nullable=True)  # Increased size
    iban = db.Column(db.String(255), nullable=True)  # Increased size
    bic = db.Column(db.String(255), nullable=True)  # Increased size
    sepa_auth_type = db.Column(db.String(50), nullable=True)
    mandate_reference = db.Column(db.String(50), nullable=True)
    mandate_date = db.Column(db.DateTime, nullable=True)
    customer_type = db.Column(db.String(50), nullable=True)  # Soort
    no_email = db.Column(db.Boolean, default=False)
    payment_term = db.Column(db.Integer, nullable=True)
    newsletter_groups = db.Column(db.String(200), nullable=True)
    subscriptions = db.Column(db.String(200), nullable=True)
    notes = db.Column(db.Text, nullable=True)  # General notes about the customer
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

 
    def __init__(self, *args, **kwargs):
        if 'gender' in kwargs:
            kwargs['gender'] = self.validate_gender(kwargs['gender'])
        super().__init__(*args, **kwargs)

    @staticmethod
    def validate_gender(gender):
        """Validate gender field."""
        # Allow None, empty string, or valid gender values
        if gender and gender.strip() and gender not in VALID_GENDER_VALUES:
            raise ValueError(f"Invalid gender value. Must be one of: {VALID_GENDER_VALUES}")
        return gender if gender and gender.strip() else None

    def to_dict(self):
        # No need for decryption anymore
        return {
            "id": self.id,
            "date": self.date.isoformat() if self.date else None,
            "code": self.code,
            "name": self.name,
            "kvk_number": self.kvk_number,
            "contact_person": self.contact_person,
            "gender": self.gender,
            "title": self.title,
            "address": self.address,
            "postal_code": self.postal_code,
            "city": self.city,
            "country": self.country,
            "address2": self.address2,
            "postal_code2": self.postal_code2,
            "city2": self.city2,
            "country2": self.country2,
            "phone": self.phone,
            "mobile": self.mobile,
            "fax": self.fax,
            "email": self.email,
            "invoice_email": self.invoice_email,
            "reminder_email": self.reminder_email,
            "website": self.website,
            "bank_account": self.bank_account,
            "giro_account": self.giro_account,
            "vat_number": self.vat_number,
            "iban": self.iban,
            "bic": self.bic,
            "sepa_auth_type": self.sepa_auth_type,
            "mandate_reference": self.mandate_reference,
            "mandate_date": self.mandate_date.isoformat() if self.mandate_date else None,
            "customer_type": self.customer_type,
            "no_email": self.no_email,
            "payment_term": self.payment_term,
            "newsletter_groups": self.newsletter_groups,
            "subscriptions": self.subscriptions,
            "notes": self.notes,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
