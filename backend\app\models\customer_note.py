from app import db
from datetime import datetime, timezone

class CustomerNote(db.Model):
    """Model for customer notes."""
    __tablename__ = "customer_notes"
    
    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>("customers.id", ondelete="CASCADE"), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False)
    content = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    # Relationships
    customer = db.relationship("Customer", backref=db.backref("notes_list", lazy="dynamic", cascade="all, delete-orphan"))
    user = db.relationship("User", backref=db.backref("customer_notes", lazy="dynamic"))
    
    def to_dict(self):
        """Convert the note to a dictionary."""
        return {
            "id": self.id,
            "customer_id": self.customer_id,
            "user_id": self.user_id,
            "user_name": self.user.name if self.user.name else self.user.email,
            "content": self.content,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
