"""
Document Template model module.
This module defines the DocumentTemplate model for document templates.
"""
from app import db
from datetime import datetime

class DocumentTemplate(db.Model):
    """Model for document templates."""
    __tablename__ = "document_templates"

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    document_type = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text, nullable=True)
    file_path = db.Column(db.String(2000), nullable=False)
    file_type = db.Column(db.String(10), default="docx", nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship
    user = db.relationship("User", backref=db.backref("document_templates", lazy="dynamic"))

    def to_dict(self):
        """Convert model to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "document_type": self.document_type,
            "description": self.description,
            "file_path": self.file_path,
            "file_type": self.file_type,
            "created_by": self.created_by,
            "created_by_name": self.user.name if self.user.name else self.user.email,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
