from app import db
from datetime import datetime, timezone

class MileageEntry(db.Model):
    """Model for mileage entries."""
    __tablename__ = "mileage_entries"

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey("users.id"), nullable=False)
    date = db.Column(db.Date, nullable=False)
    license_plate = db.Column(db.String(20), nullable=False)  # License plate (kenteken)
    start_odometer = db.Column(db.Integer, nullable=False)  # Starting odometer reading
    end_odometer = db.Column(db.Integer, nullable=False)  # Ending odometer reading
    kilometers = db.Column(db.Float, nullable=False)  # Calculated kilometers (end - start)
    reason = db.Column(db.String(255), nullable=False)  # Reason for travel
    description = db.Column(db.Text, nullable=True)  # Additional description
    status = db.Column(db.String(20), default="pending")  # pending, approved, rejected
    approved_by = db.Column(db.Integer, db.<PERSON>("users.id"), nullable=True)
    approved_at = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relationships
    user = db.relationship("User", foreign_keys=[user_id], backref=db.backref("mileage_entries", lazy="dynamic"))
    approver = db.relationship("User", foreign_keys=[approved_by], backref=db.backref("approved_mileage_entries", lazy="dynamic"))

    def to_dict(self):
        """Convert the mileage entry to a dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "user_name": self.user.name if self.user.name else self.user.email,
            "date": self.date.isoformat(),
            "license_plate": self.license_plate,
            "start_odometer": self.start_odometer,
            "end_odometer": self.end_odometer,
            "kilometers": round(self.kilometers, 2),
            "reason": self.reason,
            "description": self.description,
            "status": self.status,
            "approved_by": self.approved_by,
            "approver_name": self.approver.name if self.approver and self.approver.name else (self.approver.email if self.approver else None),
            "approved_at": self.approved_at.isoformat() if self.approved_at else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
