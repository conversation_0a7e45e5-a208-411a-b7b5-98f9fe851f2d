"""
Product model module.
This module defines the Product model for the database.
"""
from app import db
from datetime import datetime

class Product(db.Model):
    """Model for products from the OSEC price list."""
    __tablename__ = "products"

    id = db.Column(db.Integer, primary_key=True)
    product_code = db.Column(db.String(50), nullable=True, index=True)  # Artikelnr
    name = db.Column(db.String(255), nullable=False)  # Artikel
    description = db.Column(db.Text, nullable=True)  # Info
    gross_price = db.Column(db.Float, nullable=True)  # Bruto prijs
    discount_percentage = db.Column(db.Float, nullable=True)  # Korting
    net_price = db.Column(db.Float, nullable=True)  # Netto prijs
    category = db.Column(db.String(100), nullable=True)  # Groep
    subcategory = db.Column(db.String(100), nullable=True)  # Groepnaam
    info = db.Column(db.Text, nullable=True)  # Extra info
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    @property
    def selling_price(self):
        """Calculate the selling price for this product (netto prijs * 1.6) and round to 2 decimals."""
        if self.net_price is not None:
            # Bereken de verkoopprijs en rond af op 2 decimalen
            return round(self.net_price * 1.6, 2)
        # Fallback naar bruto prijs als netto prijs niet beschikbaar is
        elif self.gross_price is not None:
            # Bereken de verkoopprijs en rond af op 2 decimalen
            return round(self.gross_price * 0.5 * 1.6, 2)
        return None

    def to_dict(self):
        """Convert the product to a dictionary."""
        return {
            "id": self.id,
            "product_code": self.product_code,
            "name": self.name,
            "description": self.description,
            "gross_price": self.gross_price,
            "discount_percentage": self.discount_percentage,
            "net_price": self.net_price,
            "selling_price": self.selling_price,
            "category": self.category,
            "subcategory": self.subcategory,
            "info": self.info,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
