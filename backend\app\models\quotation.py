"""
Quotation model module.
This module defines the Quotation and QuotationItem models for the database.
"""
from app import db
from datetime import datetime, timezone

class Quotation(db.Model):
    """Model for quotations."""
    __tablename__ = "quotations"

    id = db.Column(db.Integer, primary_key=True)
    customer_id = db.Column(db.Integer, db.ForeignKey("customers.id"), nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=False)
    quotation_number = db.Column(db.String(50), nullable=True, unique=True)
    title = db.Column(db.String(255), nullable=False)
    introduction = db.Column(db.Text, nullable=True)
    conclusion = db.Column(db.Text, nullable=True)
    discount_percentage = db.Column(db.Float, default=0)
    vat_percentage = db.Column(db.Float, default=21)
    status = db.Column(db.String(20), default="concept")  # concept, sent, accepted, rejected
    valid_until = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    document_id = db.Column(db.Integer, db.ForeignKey("documents.id"), nullable=True)

    # Relationships
    customer = db.relationship("Customer", backref=db.backref("quotations", lazy="dynamic"))
    user = db.relationship("User", backref=db.backref("quotations", lazy="dynamic"))
    document = db.relationship("Document", backref=db.backref("quotation", uselist=False))
    items = db.relationship("QuotationItem", backref="quotation", lazy="dynamic", cascade="all, delete-orphan")

    def to_dict(self):
        """Convert the quotation to a dictionary."""
        return {
            "id": self.id,
            "customer_id": self.customer_id,
            "customer_name": self.customer.name if self.customer else None,
            "created_by": self.created_by,
            "created_by_name": self.user.name if self.user else None,
            "quotation_number": self.quotation_number,
            "title": self.title,
            "introduction": self.introduction,
            "conclusion": self.conclusion,
            "discount_percentage": self.discount_percentage,
            "vat_percentage": self.vat_percentage,
            "status": self.status,
            "valid_until": (self.valid_until.replace(tzinfo=timezone.utc) if self.valid_until and self.valid_until.tzinfo is None else self.valid_until).isoformat() if self.valid_until else None,
            "created_at": (self.created_at.replace(tzinfo=timezone.utc) if self.created_at and self.created_at.tzinfo is None else self.created_at).isoformat() if self.created_at else None,
            "updated_at": (self.updated_at.replace(tzinfo=timezone.utc) if self.updated_at and self.updated_at.tzinfo is None else self.updated_at).isoformat() if self.updated_at else None,
            "document_id": self.document_id,
            "items": [item.to_dict() for item in self.items],
            "subtotal": sum(item.total_price for item in self.items),
            "discount_amount": sum(item.total_price for item in self.items) * (self.discount_percentage / 100) if self.discount_percentage else 0,
            "total_excl_vat": sum(item.total_price for item in self.items) * (1 - (self.discount_percentage / 100)) if self.discount_percentage else sum(item.total_price for item in self.items),
            "vat_amount": (sum(item.total_price for item in self.items) * (1 - (self.discount_percentage / 100)) if self.discount_percentage else sum(item.total_price for item in self.items)) * (self.vat_percentage / 100) if self.vat_percentage else 0,
            "total_incl_vat": (sum(item.total_price for item in self.items) * (1 - (self.discount_percentage / 100)) if self.discount_percentage else sum(item.total_price for item in self.items)) * (1 + (self.vat_percentage / 100)) if self.vat_percentage else sum(item.total_price for item in self.items)
        }


class QuotationItem(db.Model):
    """Model for quotation items."""
    __tablename__ = "quotation_items"

    id = db.Column(db.Integer, primary_key=True)
    quotation_id = db.Column(db.Integer, db.ForeignKey("quotations.id"), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey("products.id"), nullable=True)
    description = db.Column(db.Text, nullable=False)
    quantity = db.Column(db.Float, nullable=False, default=1)
    unit_price = db.Column(db.Float, nullable=False)
    discount_percentage = db.Column(db.Float, default=0)
    sort_order = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    product = db.relationship("Product", backref=db.backref("quotation_items", lazy="dynamic"))

    @property
    def total_price(self):
        """Calculate the total price for this item."""
        if self.discount_percentage:
            return self.quantity * self.unit_price * (1 - (self.discount_percentage / 100))
        return self.quantity * self.unit_price

    def to_dict(self):
        """Convert the quotation item to a dictionary."""
        return {
            "id": self.id,
            "quotation_id": self.quotation_id,
            "product_id": self.product_id,
            "product_name": self.product.name if self.product else None,
            "product_code": self.product.product_code if self.product else None,
            "description": self.description,
            "quantity": self.quantity,
            "unit_price": self.unit_price,
            "discount_percentage": self.discount_percentage,
            "sort_order": self.sort_order,
            "total_price": self.total_price,
            "created_at": (self.created_at.replace(tzinfo=timezone.utc) if self.created_at and self.created_at.tzinfo is None else self.created_at).isoformat() if self.created_at else None,
            "updated_at": (self.updated_at.replace(tzinfo=timezone.utc) if self.updated_at and self.updated_at.tzinfo is None else self.updated_at).isoformat() if self.updated_at else None
        }
