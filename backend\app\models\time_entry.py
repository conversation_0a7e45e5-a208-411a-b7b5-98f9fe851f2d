from app import db
from datetime import datetime, timezone

class TimeEntry(db.Model):
    """Model for time entries."""
    __tablename__ = "time_entries"

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey("users.id"), nullable=False)
    date = db.Column(db.Date, nullable=False)
    start_time = db.Column(db.Time, nullable=False)
    end_time = db.Column(db.Time, nullable=False)
    break_time = db.Column(db.Integer, default=0)  # Break time in minutes
    description = db.Column(db.Text, nullable=True)
    status = db.Column(db.String(20), default="pending")  # pending, approved, rejected
    approved_by = db.Column(db.Integer, db.ForeignKey("users.id"), nullable=True)
    approved_at = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relationships
    user = db.relationship("User", foreign_keys=[user_id], backref=db.backref("time_entries", lazy="dynamic"))
    approver = db.relationship("User", foreign_keys=[approved_by], backref=db.backref("approved_time_entries", lazy="dynamic"))

    def to_dict(self):
        """Convert the time entry to a dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "user_name": self.user.name if self.user.name else self.user.email,
            "date": self.date.isoformat(),
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat(),
            "break_time": self.break_time,
            "description": self.description,
            "status": self.status,
            "approved_by": self.approved_by,
            "approver_name": self.approver.name if self.approver and self.approver.name else (self.approver.email if self.approver else None),
            "approved_at": self.approved_at.isoformat() if self.approved_at else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
