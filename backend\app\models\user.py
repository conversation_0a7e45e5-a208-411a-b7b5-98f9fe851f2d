from app import db
from datetime import datetime

class User(db.Model):
    __tablename__ = "users"
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    firebase_uid = db.Column(db.String(128), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=True)
    role = db.Column(db.String(20), nullable=False)  # administrator, verkoper, monteur
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship with UserPermission is defined in the UserPermission model

    def to_dict(self):
        return {
            "id": self.id,
            "email": self.email,
            "name": self.name,
            "role": self.role,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

    def to_dict_with_permissions(self):
        permissions_dict = {}
        for permission in self.permissions:
            permissions_dict[permission.document_type] = {
                "can_view": permission.can_view,
                "can_upload": permission.can_upload
            }

        return {
            "id": self.id,
            "email": self.email,
            "name": self.name,
            "role": self.role,
            "permissions": permissions_dict,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }