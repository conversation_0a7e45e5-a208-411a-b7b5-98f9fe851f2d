from app import db
from app.models.audit_log import AuditLog
import json
import logging

logger = logging.getLogger(__name__)

class AuditLogRepository:
    def create(self, user_id, action, entity_type, entity_id=None, details=None, ip_address=None, user_agent=None):
        """
        Create a new audit log entry.
        
        Args:
            user_id: The ID of the user who performed the action.
            action: The action performed (e.g., 'create', 'update', 'delete').
            entity_type: The type of entity affected (e.g., 'user', 'customer', 'document').
            entity_id: The ID of the entity affected.
            details: Additional details about the action.
            ip_address: The IP address of the client.
            user_agent: The user agent of the client.
            
        Returns:
            The created audit log entry.
        """
        try:
            # Convert details to JSON string if it's a dict
            details_json = json.dumps(details) if details else None
            
            audit_log = AuditLog(
                user_id=user_id,
                action=action,
                entity_type=entity_type,
                entity_id=entity_id,
                details=details_json,
                ip_address=ip_address,
                user_agent=user_agent
            )
            db.session.add(audit_log)
            db.session.commit()
            logger.info(f"Created audit log: {action} on {entity_type} {entity_id} by user {user_id}")
            return audit_log
        except Exception as e:
            db.session.rollback()
            logger.error(f"Failed to create audit log: {str(e)}")
            raise
    
    def get_by_user_id(self, user_id, page=1, per_page=20):
        """
        Get audit logs for a user.
        
        Args:
            user_id: The ID of the user.
            page: The page number.
            per_page: The number of items per page.
            
        Returns:
            A list of audit logs.
        """
        try:
            query = AuditLog.query.filter_by(user_id=user_id).order_by(AuditLog.created_at.desc())
            total = query.count()
            logs = query.paginate(page=page, per_page=per_page, error_out=False).items
            return logs, total
        except Exception as e:
            logger.error(f"Failed to get audit logs for user {user_id}: {str(e)}")
            raise
    
    def get_by_entity(self, entity_type, entity_id, page=1, per_page=20):
        """
        Get audit logs for an entity.
        
        Args:
            entity_type: The type of entity.
            entity_id: The ID of the entity.
            page: The page number.
            per_page: The number of items per page.
            
        Returns:
            A list of audit logs.
        """
        try:
            query = AuditLog.query.filter_by(entity_type=entity_type, entity_id=entity_id).order_by(AuditLog.created_at.desc())
            total = query.count()
            logs = query.paginate(page=page, per_page=per_page, error_out=False).items
            return logs, total
        except Exception as e:
            logger.error(f"Failed to get audit logs for {entity_type} {entity_id}: {str(e)}")
            raise
    
    def get_all(self, page=1, per_page=20, entity_type=None, action=None, user_id=None, start_date=None, end_date=None):
        """
        Get all audit logs with optional filtering.
        
        Args:
            page: The page number.
            per_page: The number of items per page.
            entity_type: Filter by entity type.
            action: Filter by action.
            user_id: Filter by user ID.
            start_date: Filter by start date.
            end_date: Filter by end date.
            
        Returns:
            A list of audit logs.
        """
        try:
            query = AuditLog.query
            
            if entity_type:
                query = query.filter_by(entity_type=entity_type)
            
            if action:
                query = query.filter_by(action=action)
            
            if user_id:
                query = query.filter_by(user_id=user_id)
            
            if start_date:
                query = query.filter(AuditLog.created_at >= start_date)
            
            if end_date:
                query = query.filter(AuditLog.created_at <= end_date)
            
            query = query.order_by(AuditLog.created_at.desc())
            total = query.count()
            logs = query.paginate(page=page, per_page=per_page, error_out=False).items
            return logs, total
        except Exception as e:
            logger.error(f"Failed to get audit logs: {str(e)}")
            raise
