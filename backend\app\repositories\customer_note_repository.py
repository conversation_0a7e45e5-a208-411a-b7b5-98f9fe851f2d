"""
Customer note repository module.
This module provides data access methods for the CustomerNote model.
"""
from typing import List, Optional, Dict
from app import db
from app.models.customer_note import CustomerNote

class CustomerNoteRepository:
    """Repository for CustomerNote model."""
    
    def get_all_by_customer_id(self, customer_id: int) -> List[CustomerNote]:
        """Get all notes for a customer."""
        return CustomerNote.query.filter_by(customer_id=customer_id).order_by(CustomerNote.created_at.desc()).all()
    
    def get_by_id(self, note_id: int) -> Optional[CustomerNote]:
        """Get a note by ID."""
        return CustomerNote.query.get(note_id)
    
    def create(self, note_data: Dict) -> CustomerNote:
        """Create a new note."""
        note = CustomerNote(**note_data)
        db.session.add(note)
        db.session.commit()
        return note
    
    def update(self, note: CustomerNote, note_data: Dict) -> CustomerNote:
        """Update a note."""
        for key, value in note_data.items():
            setattr(note, key, value)
        db.session.commit()
        return note
    
    def delete(self, note: CustomerNote) -> None:
        """Delete a note."""
        db.session.delete(note)
        db.session.commit()
