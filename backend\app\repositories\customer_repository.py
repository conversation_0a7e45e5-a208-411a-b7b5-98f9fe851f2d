from app import db
from app.models.customer import Customer
from typing import List, Optional, Dict
from datetime import datetime, timezone

class CustomerRepository:
    def get_all(self, page: int = 1, per_page: int = 20) -> tuple[List[Customer], int]:
        customers = Customer.query.paginate(page=page, per_page=per_page, error_out=False)
        return customers.items, customers.total

    def get_by_id(self, customer_id: int) -> Optional[Customer]:
        return Customer.query.get(customer_id)

    def create(self, customer_data: Dict) -> Customer:
        # Set the date to current UTC time
        customer_data['date'] = datetime.now(timezone.utc)
        customer = Customer(**customer_data)
        db.session.add(customer)
        db.session.commit()
        return customer

    def update(self, customer: Customer, customer_data: Dict) -> Customer:
        for key, value in customer_data.items():
            setattr(customer, key, value)
        db.session.commit()
        return customer

    def delete(self, customer_id: int) -> bool:
        customer = self.get_by_id(customer_id)
        if not customer:
            return False
        db.session.delete(customer)
        db.session.commit()
        return True

    def search_by_name(self, search_term: str) -> List[Customer]:
        # Use ILIKE for case-insensitive search (PostgreSQL specific)
        # For other databases, you might need to use LOWER() or equivalent
        # Use parameterized query to prevent SQL injection
        return Customer.query.filter(Customer.name.ilike("%" + search_term + "%")).all()
