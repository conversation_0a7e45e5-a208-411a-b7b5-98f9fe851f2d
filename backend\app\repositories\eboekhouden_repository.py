import requests
from ..config import Config
from typing import List, Dict

class EBoekhoudenRepository:
    def __init__(self):
        self.api_key = Config.EBOEKHOUDEN_API_KEY
        self.base_url = Config.EBOEKHOUDEN_API_URL

    def get_customers(self) -> List[Dict]:
        """
        Fetch customers from e-Boekhouden API.

        Returns:
            List[Dict]: List of customer data.
        """
        try:
            url = f"{self.base_url}/relaties"
            headers = {"Authorization": f"Bearer {self.api_key}"}
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            customers = response.json().get("relaties", [])
            return [
                {
                    "eboekhouden_id": str(customer["id"]),
                    "name": customer["name"],
                    "email": customer.get("email"),
                    "phone": customer.get("phone"),
                    "address": customer.get("address")
                }
                for customer in customers
            ]
        except requests.RequestException as e:
            raise Exception(f"Failed to fetch customers from e-Boekhouden: {str(e)}")