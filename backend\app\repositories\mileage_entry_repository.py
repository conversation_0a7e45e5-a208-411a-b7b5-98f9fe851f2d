"""
Mileage Entry repository module.
This module defines the repository for MileageEntry model database operations.
"""
from app import db
from app.models.mileage_entry import MileageEntry
from datetime import datetime, timezone, date
from typing import List, Optional, Tuple
from sqlalchemy import extract, func

class MileageEntryRepository:
    """Repository for MileageEntry model."""

    def get_all(self, page: int = 1, per_page: int = 20) -> <PERSON>ple[List[MileageEntry], int]:
        """Get all mileage entries with pagination."""
        entries = MileageEntry.query.order_by(MileageEntry.date.desc()).paginate(page=page, per_page=per_page, error_out=False)
        return entries.items, entries.total

    def get_by_id(self, entry_id: int) -> Optional[MileageEntry]:
        """Get a mileage entry by ID."""
        return MileageEntry.query.get(entry_id)

    def get_by_user_id(self, user_id: int, page: int = 1, per_page: int = 20) -> <PERSON><PERSON>[List[MileageEntry], int]:
        """Get mileage entries for a specific user with pagination."""
        entries = MileageEntry.query.filter_by(user_id=user_id).order_by(MileageEntry.date.desc()).paginate(page=page, per_page=per_page, error_out=False)
        return entries.items, entries.total

    def get_by_user_id_and_status(self, user_id: int, status: str, page: int = 1, per_page: int = 20) -> Tuple[List[MileageEntry], int]:
        """Get mileage entries for a specific user with a specific status."""
        entries = MileageEntry.query.filter_by(user_id=user_id, status=status).order_by(MileageEntry.date.desc()).paginate(page=page, per_page=per_page, error_out=False)
        return entries.items, entries.total

    def get_by_status(self, status: str, page: int = 1, per_page: int = 20) -> Tuple[List[MileageEntry], int]:
        """Get mileage entries with a specific status."""
        entries = MileageEntry.query.filter_by(status=status).order_by(MileageEntry.date.desc()).paginate(page=page, per_page=per_page, error_out=False)
        return entries.items, entries.total

    def get_by_date_range(self, start_date: date, end_date: date, user_id: Optional[int] = None, page: int = 1, per_page: int = 20) -> Tuple[List[MileageEntry], int]:
        """Get mileage entries within a date range for a specific user (optional)."""
        query = MileageEntry.query.filter(MileageEntry.date >= start_date, MileageEntry.date <= end_date)
        if user_id:
            query = query.filter_by(user_id=user_id)
        entries = query.order_by(MileageEntry.date.desc()).paginate(page=page, per_page=per_page, error_out=False)
        return entries.items, entries.total

    def get_by_month_year(self, month: int, year: int, user_id: Optional[int] = None, page: int = 1, per_page: int = 20) -> Tuple[List[MileageEntry], int]:
        """Get mileage entries for a specific month and year for a specific user (optional)."""
        query = MileageEntry.query.filter(extract('month', MileageEntry.date) == month, extract('year', MileageEntry.date) == year)
        if user_id:
            query = query.filter_by(user_id=user_id)
        entries = query.order_by(MileageEntry.date.desc()).paginate(page=page, per_page=per_page, error_out=False)
        return entries.items, entries.total

    def create(self, user_id: int, date_val: date, license_plate: str, start_odometer: int, end_odometer: int, kilometers: float, reason: str, description: Optional[str] = None) -> MileageEntry:
        """Create a new mileage entry."""
        entry = MileageEntry(
            user_id=user_id,
            date=date_val,
            license_plate=license_plate,
            start_odometer=start_odometer,
            end_odometer=end_odometer,
            kilometers=kilometers,
            reason=reason,
            description=description,
            status="pending"
        )
        db.session.add(entry)
        db.session.commit()
        return entry

    def update(self, entry: MileageEntry, date_val: Optional[date] = None, license_plate: Optional[str] = None, start_odometer: Optional[int] = None, end_odometer: Optional[int] = None, kilometers: Optional[float] = None, reason: Optional[str] = None, description: Optional[str] = None) -> MileageEntry:
        """Update a mileage entry."""
        if date_val:
            entry.date = date_val
        if license_plate is not None:
            entry.license_plate = license_plate
        if start_odometer is not None:
            entry.start_odometer = start_odometer
        if end_odometer is not None:
            entry.end_odometer = end_odometer
        if kilometers is not None:
            entry.kilometers = kilometers
        if reason is not None:
            entry.reason = reason
        if description is not None:  # Allow empty string
            entry.description = description
        entry.updated_at = datetime.now(timezone.utc)
        db.session.commit()
        return entry

    def approve(self, entry: MileageEntry, approver_id: int) -> MileageEntry:
        """Approve a mileage entry."""
        entry.status = "approved"
        entry.approved_by = approver_id
        entry.approved_at = datetime.now(timezone.utc)
        entry.updated_at = datetime.now(timezone.utc)
        db.session.commit()
        return entry

    def reject(self, entry: MileageEntry, approver_id: int) -> MileageEntry:
        """Reject a mileage entry."""
        entry.status = "rejected"
        entry.approved_by = approver_id
        entry.approved_at = datetime.now(timezone.utc)
        entry.updated_at = datetime.now(timezone.utc)
        db.session.commit()
        return entry

    def delete(self, entry: MileageEntry) -> None:
        """Delete a mileage entry."""
        db.session.delete(entry)
        db.session.commit()

    def get_monthly_total_kilometers(self, user_id: int, month: int, year: int) -> float:
        """Get the total kilometers driven by a user in a specific month and year (excluding rejected)."""
        result = db.session.query(func.sum(MileageEntry.kilometers)).filter(
            MileageEntry.user_id == user_id,
            extract('month', MileageEntry.date) == month,
            extract('year', MileageEntry.date) == year,
            MileageEntry.status != "rejected"  # Exclude rejected entries
        ).scalar()

        return round(result or 0, 2)  # Round to 2 decimal places

    def get_monthly_approved_kilometers(self, user_id: int, month: int, year: int) -> float:
        """Get the total approved kilometers driven by a user in a specific month and year."""
        result = db.session.query(func.sum(MileageEntry.kilometers)).filter(
            MileageEntry.user_id == user_id,
            extract('month', MileageEntry.date) == month,
            extract('year', MileageEntry.date) == year,
            MileageEntry.status == "approved"  # Only approved entries
        ).scalar()

        return round(result or 0, 2)  # Round to 2 decimal places

    def get_monthly_pending_kilometers(self, user_id: int, month: int, year: int) -> float:
        """Get the total pending kilometers driven by a user in a specific month and year."""
        result = db.session.query(func.sum(MileageEntry.kilometers)).filter(
            MileageEntry.user_id == user_id,
            extract('month', MileageEntry.date) == month,
            extract('year', MileageEntry.date) == year,
            MileageEntry.status == "pending"  # Only pending entries
        ).scalar()

        return round(result or 0, 2)  # Round to 2 decimal places
