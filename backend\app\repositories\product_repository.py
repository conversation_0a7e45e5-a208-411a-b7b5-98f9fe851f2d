"""
Product repository module.
This module provides database operations for products.
"""
from typing import List, Dict, Optional, Tuple
from app import db
from app.models.product import Product
from sqlalchemy import or_

class ProductRepository:
    """Repository for Product model."""

    def get_all(self, page: int = 1, per_page: int = 20) -> Tuple[List[Product], int]:
        """
        Get all products with pagination.

        Args:
            page: Page number
            per_page: Number of items per page

        Returns:
            Tuple of products list and total count
        """
        products = Product.query.paginate(page=page, per_page=per_page, error_out=False)
        return products.items, products.total

    def get_by_id(self, product_id: int) -> Optional[Product]:
        """
        Get a product by ID.

        Args:
            product_id: Product ID

        Returns:
            Product or None if not found
        """
        return Product.query.get(product_id)

    def get_by_product_code(self, product_code: str) -> Optional[Product]:
        """
        Get a product by product code.

        Args:
            product_code: Product code

        Returns:
            Product or None if not found
        """
        return Product.query.filter_by(product_code=product_code).first()

    def search(self, search_term: str, page: int = 1, per_page: int = 20, category: str = None) -> Tuple[List[Product], int]:
        """
        Search products by name, description, or product code.

        Args:
            search_term: Search term
            page: Page number
            per_page: Number of items per page
            category: Filter by category (optional)

        Returns:
            Tuple of products list and total count
        """
        query = Product.query.filter(
            or_(
                Product.name.ilike(f"%{search_term}%"),
                Product.description.ilike(f"%{search_term}%"),
                Product.product_code.ilike(f"%{search_term}%")
            )
        )

        # Apply category filter if provided
        if category:
            query = query.filter(Product.category == category)

        products = query.paginate(page=page, per_page=per_page, error_out=False)
        return products.items, products.total

    def get_by_category(self, category: str, page: int = 1, per_page: int = 20) -> Tuple[List[Product], int]:
        """
        Get products by category with pagination.

        Args:
            category: Category to filter by
            page: Page number
            per_page: Number of items per page

        Returns:
            Tuple of products list and total count
        """
        query = Product.query.filter(Product.category == category)
        products = query.paginate(page=page, per_page=per_page, error_out=False)
        return products.items, products.total

    def get_all_categories(self) -> List[str]:
        """
        Get all unique categories.

        Returns:
            List of unique categories
        """
        # Query distinct categories and filter out None values
        categories = db.session.query(Product.category).distinct().all()
        return [category[0] for category in categories if category[0]]

    def create(self, product_data: Dict) -> Product:
        """
        Create a new product.

        Args:
            product_data: Product data

        Returns:
            Created product
        """
        product = Product(**product_data)
        db.session.add(product)
        db.session.commit()
        return product

    def update(self, product: Product, product_data: Dict) -> Product:
        """
        Update a product.

        Args:
            product: Product to update
            product_data: Updated product data

        Returns:
            Updated product
        """
        for key, value in product_data.items():
            setattr(product, key, value)

        db.session.commit()
        return product

    def delete(self, product_id: int) -> bool:
        """
        Delete a product.

        Args:
            product_id: Product ID

        Returns:
            True if deleted, False if not found
        """
        product = self.get_by_id(product_id)
        if not product:
            return False

        db.session.delete(product)
        db.session.commit()
        return True

    def delete_all(self) -> int:
        """
        Delete all products.

        Returns:
            Number of deleted products
        """
        count = Product.query.count()
        Product.query.delete()
        db.session.commit()
        return count

    def delete_by_category(self, category: str) -> int:
        """
        Delete all products in a specific category.

        Args:
            category: Category to delete products from

        Returns:
            Number of deleted products
        """
        products = Product.query.filter(Product.category == category).all()
        count = len(products)

        for product in products:
            db.session.delete(product)

        db.session.commit()
        return count

    def bulk_create(self, products_data: List[Dict]) -> List[Product]:
        """
        Create multiple products at once.

        Args:
            products_data: List of product data dictionaries

        Returns:
            List of created products
        """
        products = [Product(**product_data) for product_data in products_data]
        db.session.add_all(products)
        db.session.commit()
        return products
