"""
Quotation repository module.
This module provides database operations for quotations and quotation items.
"""
from typing import List, Dict, Optional, Tuple
from app import db
from app.models.quotation import Quotation, QuotationItem
from sqlalchemy import or_

class QuotationRepository:
    """Repository for Quotation model."""

    def get_all(self, page: int = 1, per_page: int = 20) -> <PERSON>ple[List[Quotation], int]:
        """
        Get all quotations with pagination.

        Args:
            page: Page number
            per_page: Number of items per page

        Returns:
            Tuple of quotations list and total count
        """
        quotations = Quotation.query.paginate(page=page, per_page=per_page, error_out=False)
        return quotations.items, quotations.total

    def get_by_id(self, quotation_id: int) -> Optional[Quotation]:
        """
        Get a quotation by ID.

        Args:
            quotation_id: Quotation ID

        Returns:
            Quotation or None if not found
        """
        return Quotation.query.get(quotation_id)

    def get_by_customer(self, customer_id: int, page: int = 1, per_page: int = 20) -> <PERSON><PERSON>[List[Quotation], int]:
        """
        Get quotations for a customer with pagination.

        Args:
            customer_id: Customer ID
            page: Page number
            per_page: Number of items per page

        Returns:
            Tuple of quotations list and total count
        """
        quotations = Quotation.query.filter_by(customer_id=customer_id).paginate(
            page=page, per_page=per_page, error_out=False
        )
        return quotations.items, quotations.total

    def get_by_status(self, status: str, page: int = 1, per_page: int = 20) -> Tuple[List[Quotation], int]:
        """
        Get quotations by status with pagination.

        Args:
            status: Quotation status
            page: Page number
            per_page: Number of items per page

        Returns:
            Tuple of quotations list and total count
        """
        quotations = Quotation.query.filter_by(status=status).paginate(
            page=page, per_page=per_page, error_out=False
        )
        return quotations.items, quotations.total

    def get_by_status_all(self, status: str) -> List[Quotation]:
        """
        Get all quotations with a specific status (no pagination).

        Args:
            status: Quotation status

        Returns:
            List of quotations
        """
        return Quotation.query.filter_by(status=status).all()

    def get_by_customer_and_not_status(self, customer_id: int, status: str) -> List[Quotation]:
        """
        Get all quotations for a customer that do not have the specified status.

        Args:
            customer_id: Customer ID
            status: Status to exclude

        Returns:
            List of quotations
        """
        return Quotation.query.filter(
            Quotation.customer_id == customer_id,
            Quotation.status != status
        ).all()

    def search(self, search_term: str, page: int = 1, per_page: int = 20) -> Tuple[List[Quotation], int]:
        """
        Search quotations by title, quotation number, or customer name.

        Args:
            search_term: Search term
            page: Page number
            per_page: Number of items per page

        Returns:
            Tuple of quotations list and total count
        """
        query = Quotation.query.join(Quotation.customer).filter(
            or_(
                Quotation.title.ilike(f"%{search_term}%"),
                Quotation.quotation_number.ilike(f"%{search_term}%"),
                Quotation.customer.has(name=search_term)
            )
        )

        quotations = query.paginate(page=page, per_page=per_page, error_out=False)
        return quotations.items, quotations.total

    def create(self, quotation_data: Dict) -> Quotation:
        """
        Create a new quotation.

        Args:
            quotation_data: Quotation data

        Returns:
            Created quotation
        """
        # Extract items data if present
        items_data = quotation_data.pop('items', [])

        # Create the quotation
        quotation = Quotation(**quotation_data)
        db.session.add(quotation)
        db.session.flush()  # Flush to get the quotation ID

        # Create items if provided
        for item_data in items_data:
            item_data['quotation_id'] = quotation.id
            item = QuotationItem(**item_data)
            db.session.add(item)

        db.session.commit()
        return quotation

    def update(self, quotation: Quotation, quotation_data: Dict) -> Quotation:
        """
        Update a quotation.

        Args:
            quotation: Quotation to update
            quotation_data: Updated quotation data

        Returns:
            Updated quotation
        """
        # Extract items data if present
        items_data = quotation_data.pop('items', None)

        # Filter out read-only fields that should not be updated
        read_only_fields = [
            'created_at', 'updated_at', 'customer_name', 'created_by_name',
            'subtotal', 'discount_amount', 'total_excl_vat', 'vat_amount',
            'total_incl_vat', 'id'
        ]

        # Update quotation fields
        for key, value in quotation_data.items():
            if key not in read_only_fields:
                setattr(quotation, key, value)

        # Update items if provided
        if items_data is not None:
            # Delete existing items
            QuotationItem.query.filter_by(quotation_id=quotation.id).delete()

            # Create new items
            for item_data in items_data:
                item_data['quotation_id'] = quotation.id
                item = QuotationItem(**item_data)
                db.session.add(item)

        db.session.commit()
        return quotation

    def delete(self, quotation_id: int) -> bool:
        """
        Delete a quotation.

        Args:
            quotation_id: Quotation ID

        Returns:
            True if deleted, False if not found
        """
        quotation = self.get_by_id(quotation_id)
        if not quotation:
            return False

        # Delete associated items (should be handled by cascade)
        db.session.delete(quotation)
        db.session.commit()
        return True

    def get_next_quotation_number(self) -> str:
        """
        Generate the next quotation number.

        Returns:
            Next quotation number
        """
        from datetime import datetime

        # Get the current year
        current_year = datetime.now().year

        # Find the highest quotation number for the current year
        latest_quotation = Quotation.query.filter(
            Quotation.quotation_number.like(f"{current_year}-%")
        ).order_by(Quotation.quotation_number.desc()).first()

        if latest_quotation and latest_quotation.quotation_number:
            # Extract the number part and increment
            try:
                year, number = latest_quotation.quotation_number.split('-')
                next_number = int(number) + 1
            except (ValueError, IndexError):
                # If the format is not as expected, start from 1
                next_number = 1
        else:
            # No quotations for this year yet
            next_number = 1

        # Format: YYYY-NNNN (e.g., 2023-0001)
        return f"{current_year}-{next_number:04d}"


class QuotationItemRepository:
    """Repository for QuotationItem model."""

    def get_by_id(self, item_id: int) -> Optional[QuotationItem]:
        """
        Get a quotation item by ID.

        Args:
            item_id: Item ID

        Returns:
            QuotationItem or None if not found
        """
        return QuotationItem.query.get(item_id)

    def get_by_quotation(self, quotation_id: int) -> List[QuotationItem]:
        """
        Get all items for a quotation.

        Args:
            quotation_id: Quotation ID

        Returns:
            List of quotation items
        """
        return QuotationItem.query.filter_by(quotation_id=quotation_id).order_by(QuotationItem.sort_order).all()

    def create(self, item_data: Dict) -> QuotationItem:
        """
        Create a new quotation item.

        Args:
            item_data: Item data

        Returns:
            Created quotation item
        """
        item = QuotationItem(**item_data)
        db.session.add(item)
        db.session.commit()
        return item

    def update(self, item: QuotationItem, item_data: Dict) -> QuotationItem:
        """
        Update a quotation item.

        Args:
            item: Item to update
            item_data: Updated item data

        Returns:
            Updated quotation item
        """
        for key, value in item_data.items():
            setattr(item, key, value)

        db.session.commit()
        return item

    def delete(self, item_id: int) -> bool:
        """
        Delete a quotation item.

        Args:
            item_id: Item ID

        Returns:
            True if deleted, False if not found
        """
        item = self.get_by_id(item_id)
        if not item:
            return False

        db.session.delete(item)
        db.session.commit()
        return True
