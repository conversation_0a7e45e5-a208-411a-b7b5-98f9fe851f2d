"""
Time Entry repository module.
This module defines the repository for TimeEntry model database operations.
"""
from app import db
from app.models.time_entry import TimeEntry
from datetime import datetime, timezone, date
from typing import List, Optional, Tuple
from sqlalchemy import extract, func

class TimeEntryRepository:
    """Repository for TimeEntry model."""

    def get_all(self, page: int = 1, per_page: int = 20) -> <PERSON><PERSON>[List[TimeEntry], int]:
        """Get all time entries with pagination."""
        entries = TimeEntry.query.order_by(TimeEntry.date.desc(), TimeEntry.start_time.desc()).paginate(page=page, per_page=per_page, error_out=False)
        return entries.items, entries.total

    def get_by_id(self, entry_id: int) -> Optional[TimeEntry]:
        """Get a time entry by ID."""
        return TimeEntry.query.get(entry_id)

    def get_by_user_id(self, user_id: int, page: int = 1, per_page: int = 20) -> <PERSON><PERSON>[List[TimeEntry], int]:
        """Get time entries for a specific user with pagination."""
        entries = TimeEntry.query.filter_by(user_id=user_id).order_by(TimeEntry.date.desc(), TimeEntry.start_time.desc()).paginate(page=page, per_page=per_page, error_out=False)
        return entries.items, entries.total

    def get_by_user_date(self, user_id: int, date_val: date) -> List[TimeEntry]:
        """Get all time entries for a specific user on a specific date."""
        entries = TimeEntry.query.filter_by(user_id=user_id, date=date_val).all()
        return entries

    def get_by_user_id_and_status(self, user_id: int, status: str, page: int = 1, per_page: int = 20) -> Tuple[List[TimeEntry], int]:
        """Get time entries for a specific user with a specific status."""
        entries = TimeEntry.query.filter_by(user_id=user_id, status=status).order_by(TimeEntry.date.desc(), TimeEntry.start_time.desc()).paginate(page=page, per_page=per_page, error_out=False)
        return entries.items, entries.total

    def get_by_status(self, status: str, page: int = 1, per_page: int = 20) -> Tuple[List[TimeEntry], int]:
        """Get time entries with a specific status."""
        entries = TimeEntry.query.filter_by(status=status).order_by(TimeEntry.date.desc(), TimeEntry.start_time.desc()).paginate(page=page, per_page=per_page, error_out=False)
        return entries.items, entries.total

    def get_by_date_range(self, start_date: date, end_date: date, user_id: Optional[int] = None, page: int = 1, per_page: int = 20) -> Tuple[List[TimeEntry], int]:
        """Get time entries within a date range for a specific user (optional)."""
        query = TimeEntry.query.filter(TimeEntry.date >= start_date, TimeEntry.date <= end_date)
        if user_id:
            query = query.filter_by(user_id=user_id)
        entries = query.order_by(TimeEntry.date.desc(), TimeEntry.start_time.desc()).paginate(page=page, per_page=per_page, error_out=False)
        return entries.items, entries.total

    def get_by_month_year(self, month: int, year: int, user_id: Optional[int] = None, page: int = 1, per_page: int = 20) -> Tuple[List[TimeEntry], int]:
        """Get time entries for a specific month and year for a specific user (optional)."""
        query = TimeEntry.query.filter(extract('month', TimeEntry.date) == month, extract('year', TimeEntry.date) == year)
        if user_id:
            query = query.filter_by(user_id=user_id)
        entries = query.order_by(TimeEntry.date.desc(), TimeEntry.start_time.desc()).paginate(page=page, per_page=per_page, error_out=False)
        return entries.items, entries.total

    def create(self, user_id: int, date_val: date, start_time, end_time, break_time: int = 0, description: Optional[str] = None) -> TimeEntry:
        """Create a new time entry."""
        entry = TimeEntry(
            user_id=user_id,
            date=date_val,
            start_time=start_time,
            end_time=end_time,
            break_time=break_time,
            description=description,
            status="pending"
        )
        db.session.add(entry)
        db.session.commit()
        return entry

    def update(self, entry: TimeEntry, date_val: Optional[date] = None, start_time = None, end_time = None, break_time: Optional[int] = None, description: Optional[str] = None) -> TimeEntry:
        """Update a time entry."""
        if date_val:
            entry.date = date_val
        if start_time:
            entry.start_time = start_time
        if end_time:
            entry.end_time = end_time
        if break_time is not None:
            entry.break_time = break_time
        if description is not None:  # Allow empty string
            entry.description = description
        entry.updated_at = datetime.now(timezone.utc)
        db.session.commit()
        return entry

    def approve(self, entry: TimeEntry, approver_id: int) -> TimeEntry:
        """Approve a time entry."""
        entry.status = "approved"
        entry.approved_by = approver_id
        entry.approved_at = datetime.now(timezone.utc)
        entry.updated_at = datetime.now(timezone.utc)
        db.session.commit()
        return entry

    def reject(self, entry: TimeEntry, approver_id: int) -> TimeEntry:
        """Reject a time entry."""
        entry.status = "rejected"
        entry.approved_by = approver_id
        entry.approved_at = datetime.now(timezone.utc)
        entry.updated_at = datetime.now(timezone.utc)
        db.session.commit()
        return entry

    def delete(self, entry: TimeEntry) -> None:
        """Delete a time entry."""
        db.session.delete(entry)
        db.session.commit()

    def get_monthly_total_hours(self, user_id: int, month: int, year: int) -> float:
        """Get the total hours worked by a user in a specific month and year (excluding rejected)."""
        entries = TimeEntry.query.filter(
            TimeEntry.user_id == user_id,
            extract('month', TimeEntry.date) == month,
            extract('year', TimeEntry.date) == year,
            TimeEntry.status != "rejected"  # Exclude rejected entries
        ).all()

        total_hours = 0
        for entry in entries:
            # Calculate hours between start_time and end_time
            start_datetime = datetime.combine(entry.date, entry.start_time)
            end_datetime = datetime.combine(entry.date, entry.end_time)
            hours = (end_datetime - start_datetime).total_seconds() / 3600
            # Subtract break time (convert minutes to hours)
            break_hours = (entry.break_time or 0) / 60
            hours -= break_hours
            total_hours += max(0, hours)  # Ensure non-negative

        return round(total_hours, 2)  # Round to 2 decimal places

    def get_monthly_approved_hours(self, user_id: int, month: int, year: int) -> float:
        """Get the total approved hours worked by a user in a specific month and year."""
        entries = TimeEntry.query.filter(
            TimeEntry.user_id == user_id,
            extract('month', TimeEntry.date) == month,
            extract('year', TimeEntry.date) == year,
            TimeEntry.status == "approved"  # Only approved entries
        ).all()

        total_hours = 0
        for entry in entries:
            # Calculate hours between start_time and end_time
            start_datetime = datetime.combine(entry.date, entry.start_time)
            end_datetime = datetime.combine(entry.date, entry.end_time)
            hours = (end_datetime - start_datetime).total_seconds() / 3600
            # Subtract break time (convert minutes to hours)
            break_hours = (entry.break_time or 0) / 60
            hours -= break_hours
            total_hours += max(0, hours)  # Ensure non-negative

        return round(total_hours, 2)  # Round to 2 decimal places

    def get_monthly_pending_hours(self, user_id: int, month: int, year: int) -> float:
        """Get the total pending hours worked by a user in a specific month and year."""
        entries = TimeEntry.query.filter(
            TimeEntry.user_id == user_id,
            extract('month', TimeEntry.date) == month,
            extract('year', TimeEntry.date) == year,
            TimeEntry.status == "pending"  # Only pending entries
        ).all()

        total_hours = 0
        for entry in entries:
            # Calculate hours between start_time and end_time
            start_datetime = datetime.combine(entry.date, entry.start_time)
            end_datetime = datetime.combine(entry.date, entry.end_time)
            hours = (end_datetime - start_datetime).total_seconds() / 3600
            # Subtract break time (convert minutes to hours)
            break_hours = (entry.break_time or 0) / 60
            hours -= break_hours
            total_hours += max(0, hours)  # Ensure non-negative

        return round(total_hours, 2)  # Round to 2 decimal places
