from app import db
from app.models.user import User
from typing import List, Optional

class UserRepository:
    def get_all(self, page: int = 1, per_page: int = 20) -> tuple[List[User], int]:
        users = User.query.paginate(page=page, per_page=per_page, error_out=False)
        return users.items, users.total

    def get_by_id(self, user_id: int) -> Optional[User]:
        return User.query.get(user_id)

    def get_by_firebase_uid(self, firebase_uid: str) -> Optional[User]:
        return User.query.filter_by(firebase_uid=firebase_uid).first()

    def create(self, firebase_uid: str, email: str, role: str, name: str = None) -> User:
        user = User(firebase_uid=firebase_uid, email=email, role=role, name=name)
        db.session.add(user)
        db.session.commit()
        return user

    def update_role(self, user: User, new_role: str) -> User:
        user.role = new_role
        db.session.commit()
        return user

    def update_name(self, user: User, new_name: str) -> User:
        user.name = new_name
        db.session.commit()
        return user

    def delete(self, user_id: int) -> bool:
        user = self.get_by_id(user_id)
        if not user:
            return False
        db.session.delete(user)
        db.session.commit()
        return True