@document_bp.route('/upcoming-expirations', methods=['GET'])
@jwt_required()
def get_upcoming_expirations():
    try:
        documents = document_service.get_upcoming_expirations()
        # Filter to only include active documents
        active_documents = [doc for doc in documents if doc.status == 'active']
        return jsonify({
            'documents': [doc.to_dict() for doc in active_documents],
            'message': 'Upcoming expirations retrieved successfully'
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500