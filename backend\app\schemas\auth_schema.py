"""
Auth schema module.
This module defines the schema for authentication-related validation.
"""
from marshmallow import Schema, fields, ValidationError, validate

def validate_token_format(token):
    """Validate token format."""
    if not token:
        raise ValidationError('Token is required')
    if len(token) < 10:
        raise ValidationError('Invalid token format')

def validate_optional_token_format(token):
    """Validate optional token format."""
    if token and len(token) < 10:
        raise ValidationError('Invalid token format')

class TokenVerificationSchema(Schema):
    """Schema for token verification."""

    token = fields.String(required=True, validate=validate_token_format)

class LogoutSchema(Schema):
    """Schema for logout."""

    token = fields.String(required=False, validate=validate_optional_token_format)

# Initialize schemas
token_verification_schema = TokenVerificationSchema()
logout_schema = LogoutSchema()
