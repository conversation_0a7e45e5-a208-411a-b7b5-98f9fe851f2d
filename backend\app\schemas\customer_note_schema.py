"""
Customer note schema module.
This module defines the schema for CustomerNote model validation.
"""
from marshmallow import fields
from app.schemas import ma
from app.models.customer_note import CustomerNote

class CustomerNoteSchema(ma.SQLAlchemySchema):
    """Schema for CustomerNote model."""
    
    class Meta:
        """Meta class for CustomerNoteSchema."""
        model = CustomerNote
        load_instance = True
    
    id = ma.auto_field(dump_only=True)
    customer_id = fields.Integer(required=True)
    user_id = fields.Integer(required=True)
    content = fields.String(required=True)
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)
    user_name = fields.String(dump_only=True)
    
    # Validation removed due to Marshmallow compatibility issues

# Initialize schemas
customer_note_schema = CustomerNoteSchema()
customer_notes_schema = CustomerNoteSchema(many=True)
