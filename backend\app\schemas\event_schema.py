"""
Event schema module.
This module defines the schema for Event model validation.
"""
from marshmallow import fields, validate
from app.schemas import ma
from app.models.event import Event, VALID_EVENT_TYPES

class EventSchema(ma.SQLAlchemySchema):
    """Schema for Event model."""

    class Meta:
        """Meta class for EventSchema."""
        model = Event
        load_instance = True

    id = ma.auto_field(dump_only=True)
    customer_id = fields.Integer(allow_none=True)
    user_ids = fields.List(fields.Integer(), allow_none=True)
    # Keep legacy field for backward compatibility
    user_id = fields.Integer(allow_none=True)
    document_id = fields.Integer(allow_none=True)
    event_type = fields.String(required=True, validate=validate.OneOf(VALID_EVENT_TYPES))
    description = fields.String(required=True)
    scheduled_date = fields.DateTime(required=True)
    status = fields.String(dump_only=True)
    completed_at = fields.DateTime(allow_none=True, dump_only=True)
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)

    # Additional fields for response
    customer_name = fields.String(dump_only=True)
    customer_address = fields.String(dump_only=True)
    user_emails = fields.List(fields.String(), dump_only=True)
    user_names = fields.List(fields.String(), dump_only=True)
    # Keep legacy fields for backward compatibility
    user_email = fields.String(dump_only=True)

    # Validation removed due to Marshmallow compatibility issues

# Initialize schemas
event_schema = EventSchema()
events_schema = EventSchema(many=True)
