"""
Product schema module.
This module defines the schema for Product model validation.
"""
from marshmallow import fields, ValidationError, validate
from app.schemas import ma
from app.models.product import Product

def validate_product_name(name):
    """Validate product name field."""
    if not name:
        raise ValidationError('Product name is required')
    if len(name) > 255:
        raise ValidationError('Product name must be less than 255 characters')

class ProductSchema(ma.SQLAlchemySchema):
    """Schema for Product model."""

    class Meta:
        """Meta class for ProductSchema."""
        model = Product
        load_instance = True

    id = ma.auto_field(dump_only=True)
    product_code = fields.String(allow_none=True)
    name = fields.String(required=True, validate=validate_product_name)
    description = fields.String(allow_none=True)
    gross_price = fields.Float(allow_none=True)
    discount_percentage = fields.Float(allow_none=True)
    net_price = fields.Float(allow_none=True)
    category = fields.String(allow_none=True)
    subcategory = fields.String(allow_none=True)
    info = fields.String(allow_none=True)
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)

    # Validation moved to field-level to avoid Marshmallow version conflicts
    # All @validates decorators removed due to Marshmallow compatibility issues


# Create instances of the schema for single and multiple products
product_schema = ProductSchema()
products_schema = ProductSchema(many=True)
