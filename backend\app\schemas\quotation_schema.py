"""
Quotation schema module.
This module defines the schema for Quotation and QuotationItem model validation.
"""
from marshmallow import fields, validate
from app.schemas import ma
from app.models.quotation import Quotation, QuotationItem

class QuotationItemSchema(ma.SQLAlchemySchema):
    """Schema for QuotationItem model."""

    class Meta:
        """Meta class for QuotationItemSchema."""
        model = QuotationItem
        load_instance = True

    id = ma.auto_field(dump_only=True)
    quotation_id = fields.Integer(required=False)
    product_id = fields.Integer(allow_none=True)
    description = fields.String(required=True)
    quantity = fields.Float(required=True)
    unit_price = fields.Float(required=True)
    discount_percentage = fields.Float(allow_none=True)
    sort_order = fields.Integer(allow_none=True)
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)

    # Additional fields for response
    product_name = fields.String(dump_only=True)
    product_code = fields.String(dump_only=True)
    total_price = fields.Float(dump_only=True)

    # Validation removed due to Marshmallow compatibility issues


class QuotationSchema(ma.SQLAlchemySchema):
    """Schema for Quotation model."""

    class Meta:
        """Meta class for QuotationSchema."""
        model = Quotation
        load_instance = True

    id = ma.auto_field(dump_only=True)
    customer_id = fields.Integer(required=True)
    created_by = fields.Integer(required=True)
    quotation_number = fields.String(allow_none=True)
    title = fields.String(required=True)
    introduction = fields.String(allow_none=True)
    conclusion = fields.String(allow_none=True)
    discount_percentage = fields.Float(allow_none=True)
    vat_percentage = fields.Float(allow_none=True)
    status = fields.String(validate=validate.OneOf(["concept", "sent", "accepted", "rejected"]))
    valid_until = fields.DateTime(allow_none=True)
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)
    document_id = fields.Integer(allow_none=True)

    # Nested fields
    items = fields.List(fields.Nested(QuotationItemSchema), dump_only=True)

    # Additional fields for response
    customer_name = fields.String(dump_only=True)
    created_by_name = fields.String(dump_only=True)
    subtotal = fields.Float(dump_only=True)
    discount_amount = fields.Float(dump_only=True)
    total_excl_vat = fields.Float(dump_only=True)
    vat_amount = fields.Float(dump_only=True)
    total_incl_vat = fields.Float(dump_only=True)

    # Validation removed due to Marshmallow compatibility issues


# Create instances of the schema for single and multiple quotations
quotation_schema = QuotationSchema()
quotations_schema = QuotationSchema(many=True)

# Create instances of the schema for single and multiple quotation items
quotation_item_schema = QuotationItemSchema()
quotation_items_schema = QuotationItemSchema(many=True)
