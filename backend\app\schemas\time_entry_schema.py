"""
Time Entry schema module.
This module defines the schema for TimeEntry model validation.
"""
from marshmallow import fields, validates, ValidationError, validate
from app.schemas import ma
from app.models.time_entry import TimeEntry
from datetime import date, time

class TimeEntrySchema(ma.SQLAlchemySchema):
    """Schema for TimeEntry model."""

    class Meta:
        """Meta class for TimeEntrySchema."""
        model = TimeEntry
        load_instance = True

    id = ma.auto_field(dump_only=True)
    user_id = fields.Integer(required=True)
    date = fields.Date(required=True)
    start_time = fields.Time(required=True)
    end_time = fields.Time(required=True)
    break_time = fields.Integer(validate=validate.Range(min=0, max=480))  # Max 8 hours break
    description = fields.String(allow_none=True)
    status = fields.String(dump_only=True)
    approved_by = fields.Integer(allow_none=True, dump_only=True)
    approved_at = fields.DateTime(allow_none=True, dump_only=True)
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)

    # Additional fields for response
    user_name = fields.String(dump_only=True)
    approver_name = fields.String(dump_only=True)

    # Validation removed due to Marshmallow compatibility issues

class TimeEntryCreateSchema(ma.Schema):
    """Schema for creating a time entry."""
    user_id = fields.Integer(required=True)
    date = fields.Date(required=True)
    start_time = fields.Time(required=True)
    end_time = fields.Time(required=True)
    break_time = fields.Integer(validate=validate.Range(min=0, max=480))  # Max 8 hours break
    description = fields.String(allow_none=True)

    # Validation removed due to Marshmallow compatibility issues

class TimeEntryUpdateSchema(ma.Schema):
    """Schema for updating a time entry."""
    date = fields.Date()
    start_time = fields.Time()
    end_time = fields.Time()
    break_time = fields.Integer(validate=validate.Range(min=0, max=480))  # Max 8 hours break
    description = fields.String(allow_none=True)

    # Validation removed due to Marshmallow compatibility issues

class TimeEntryApprovalSchema(ma.Schema):
    """Schema for approving or rejecting a time entry."""
    status = fields.String(required=True, validate=validate.OneOf(["approved", "rejected"]))
