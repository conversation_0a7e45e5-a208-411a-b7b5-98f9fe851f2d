"""
User schema module.
This module defines the schema for User model validation.
"""
from marshmallow import fields, validate, Schema
from app.schemas import ma
from app.models.user import User

class UserSchema(ma.SQLAlchemySchema):
    """Schema for User model."""

    class Meta:
        """Meta class for UserSchema."""
        model = User
        load_instance = True

    id = ma.auto_field(dump_only=True)
    firebase_uid = ma.auto_field(dump_only=True)
    email = fields.Email(required=True)
    name = fields.String(allow_none=True)
    role = fields.String(required=True, validate=validate.OneOf(["administrator", "verkoper", "monteur"]))
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)

    # Validation removed due to Marshmallow compatibility issues

class UserCreateSchema(Schema):
    """Schema for user creation."""
    email = fields.Email(required=True)
    password = fields.String(required=True)
    name = fields.String(allow_none=True)
    role = fields.String(required=True, validate=validate.OneOf(["administrator", "verkoper", "monteur"]))

    # Validation removed due to Marshmallow compatibility issues

# Initialize schemas
user_schema = UserSchema()
users_schema = UserSchema(many=True)
user_create_schema = UserCreateSchema()
