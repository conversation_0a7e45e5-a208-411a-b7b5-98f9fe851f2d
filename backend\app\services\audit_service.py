from app.repositories.audit_log_repository import AuditLogRepository
from app.utils.cache_utils import clear_cache_for_entity
import logging

logger = logging.getLogger(__name__)

class AuditService:
    def __init__(self):
        self.audit_repo = AuditLogRepository()

    def log_action(self, user_id, action, entity_type, entity_id=None, details=None, ip_address=None, user_agent=None):
        """
        Log an action performed by a user.

        Args:
            user_id: The ID of the user who performed the action.
            action: The action performed (e.g., 'create', 'update', 'delete').
            entity_type: The type of entity affected (e.g., 'user', 'customer', 'document').
            entity_id: The ID of the entity affected.
            details: Additional details about the action.
            ip_address: The IP address of the client.
            user_agent: The user agent of the client.

        Returns:
            The created audit log entry.
        """
        try:
            audit_log = self.audit_repo.create(
                user_id=user_id,
                action=action,
                entity_type=entity_type,
                entity_id=entity_id,
                details=details,
                ip_address=ip_address,
                user_agent=user_agent
            )
            log_dict = audit_log.to_dict()

            # Clear relevant caches
            clear_cache_for_entity('audit')
            if user_id:
                clear_cache_for_entity('user_audit', user_id)
            if entity_id and entity_type:
                clear_cache_for_entity('entity_audit')

            return log_dict
        except Exception as e:
            logger.error(f"Failed to log action: {str(e)}")
            # Don't raise the exception - we don't want audit logging to break the application
            return None

    def get_user_activity(self, user_id, page=1, per_page=20):
        """
        Get activity logs for a user.

        Args:
            user_id: The ID of the user.
            page: The page number.
            per_page: The number of items per page.

        Returns:
            A list of audit logs and the total count.
        """
        try:
            logs, total = self.audit_repo.get_by_user_id(user_id, page, per_page)
            return [log.to_dict() for log in logs], total
        except Exception as e:
            logger.error(f"Failed to get user activity: {str(e)}")
            raise

    def get_entity_history(self, entity_type, entity_id, page=1, per_page=20):
        """
        Get history logs for an entity.

        Args:
            entity_type: The type of entity.
            entity_id: The ID of the entity.
            page: The page number.
            per_page: The number of items per page.

        Returns:
            A list of audit logs and the total count.
        """
        try:
            logs, total = self.audit_repo.get_by_entity(entity_type, entity_id, page, per_page)
            return [log.to_dict() for log in logs], total
        except Exception as e:
            logger.error(f"Failed to get entity history: {str(e)}")
            raise

    def get_audit_logs(self, page=1, per_page=20, entity_type=None, action=None, user_id=None, start_date=None, end_date=None):
        """
        Get all audit logs with optional filtering.

        Args:
            page: The page number.
            per_page: The number of items per page.
            entity_type: Filter by entity type.
            action: Filter by action.
            user_id: Filter by user ID.
            start_date: Filter by start date.
            end_date: Filter by end date.

        Returns:
            A list of audit logs and the total count.
        """
        try:
            logs, total = self.audit_repo.get_all(
                page=page,
                per_page=per_page,
                entity_type=entity_type,
                action=action,
                user_id=user_id,
                start_date=start_date,
                end_date=end_date
            )
            return [log.to_dict() for log in logs], total
        except Exception as e:
            logger.error(f"Failed to get audit logs: {str(e)}")
            raise
