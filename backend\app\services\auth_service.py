from app.repositories.user_repository import UserRepository
import firebase_admin
from firebase_admin import auth
import logging
import time
from sqlalchemy.exc import OperationalError, DisconnectionError
from app import db

logger = logging.getLogger(__name__)

class AuthService:
    def __init__(self):
        self.user_repo = UserRepository()

    def verify_token(self, token: str, ip_address=None, user_agent=None) -> dict:
        max_retries = 3
        retry_delay = 1  # seconds

        for attempt in range(max_retries):
            try:
                # Verify the Firebase token
                decoded_token = auth.verify_id_token(token)

                # Try to get user from database with retry logic for connection issues
                user = self._get_user_with_retry(decoded_token["uid"])

                if not user:
                    logger.error(f"User with Firebase UID {decoded_token['uid']} not found in database")
                    raise Exception("User not found in database")

                return user.to_dict()
            except auth.ExpiredIdTokenError:
                logger.error("Token has expired")
                raise Exception("Token has expired")
            except auth.InvalidIdTokenError:
                logger.error("Invalid token")
                raise Exception("Invalid token")
            except (OperationalError, DisconnectionError) as e:
                logger.warning(f"Database connection error on attempt {attempt + 1}: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay * (attempt + 1))  # Exponential backoff
                    continue
                else:
                    logger.error(f"Database connection failed after {max_retries} attempts")
                    raise Exception("Database connection failed")
            except Exception as e:
                logger.error(f"Token verification failed: {str(e)}")
                raise

    def _get_user_with_retry(self, firebase_uid: str, max_retries: int = 3):
        """Get user from database with retry logic for connection issues."""
        for attempt in range(max_retries):
            try:
                return self.user_repo.get_by_firebase_uid(firebase_uid)
            except (OperationalError, DisconnectionError) as e:
                logger.warning(f"Database query failed on attempt {attempt + 1}: {str(e)}")
                if attempt < max_retries - 1:
                    # Try to rollback and close the session to reset the connection
                    try:
                        db.session.rollback()
                        db.session.close()
                    except:
                        pass
                    time.sleep(1 * (attempt + 1))  # Exponential backoff
                    continue
                else:
                    raise