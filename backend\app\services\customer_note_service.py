"""
Customer note service module.
This module provides business logic for customer notes.
"""
from typing import List, Dict, Optional
from app.repositories.customer_note_repository import CustomerNoteRepository
from app.repositories.customer_repository import CustomerRepository
from app.schemas.customer_note_schema import customer_note_schema, customer_notes_schema

class CustomerNoteService:
    """Service for customer notes."""
    
    def __init__(self):
        """Initialize the service."""
        self.note_repo = CustomerNoteRepository()
        self.customer_repo = CustomerRepository()
    
    def get_notes_by_customer_id(self, customer_id: int) -> List[Dict]:
        """Get all notes for a customer."""
        # Check if customer exists
        customer = self.customer_repo.get_by_id(customer_id)
        if not customer:
            raise ValueError(f"Customer with ID {customer_id} not found")
        
        notes = self.note_repo.get_all_by_customer_id(customer_id)
        return customer_notes_schema.dump(notes)
    
    def get_note_by_id(self, note_id: int) -> Optional[Dict]:
        """Get a note by ID."""
        note = self.note_repo.get_by_id(note_id)
        if not note:
            return None
        return customer_note_schema.dump(note)
    
    def create_note(self, note_data: Dict) -> Dict:
        """Create a new note."""
        # Check if customer exists
        customer_id = note_data.get('customer_id')
        customer = self.customer_repo.get_by_id(customer_id)
        if not customer:
            raise ValueError(f"Customer with ID {customer_id} not found")
        
        note = self.note_repo.create(note_data)
        return customer_note_schema.dump(note)
    
    def update_note(self, note_id: int, note_data: Dict) -> Dict:
        """Update a note."""
        note = self.note_repo.get_by_id(note_id)
        if not note:
            raise ValueError(f"Note with ID {note_id} not found")
        
        # Only allow updating the content
        update_data = {'content': note_data.get('content')}
        
        updated_note = self.note_repo.update(note, update_data)
        return customer_note_schema.dump(updated_note)
    
    def delete_note(self, note_id: int) -> None:
        """Delete a note."""
        note = self.note_repo.get_by_id(note_id)
        if not note:
            raise ValueError(f"Note with ID {note_id} not found")
        
        self.note_repo.delete(note)
