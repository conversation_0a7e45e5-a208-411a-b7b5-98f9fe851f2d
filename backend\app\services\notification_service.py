from ..repositories.customer_repository import CustomerRepository
from ..services.document_service import DocumentService
from typing import List

class NotificationService:
    def __init__(self):
        self.customer_repo = CustomerRepository()
        self.document_service = DocumentService()

    def get_customer_status(self, customer_id: int) -> str:
        """
        Determine the status of a customer (green/orange/red) using only the most recent documents.
        """
        customer = self.customer_repo.get_by_id(customer_id)
        if not customer:
            raise Exception("Customer not found")

        if customer.needs_security_certificate and customer.certificate_status == "not_started":
            return "red"

        documents = self.document_service.get_documents_by_customer(customer_id)
        worst_status = "green"
        for doc in documents:
            # Only check active documents (including not_applicable status)
            if doc["active_status"] == "active" or doc["active_status"] == "not_applicable":
                expiry_status = doc["status"]  # This is the expiry status
                if expiry_status == "red":
                    return "red"
                elif expiry_status == "orange" and worst_status != "red":
                    worst_status = "orange"

        return worst_status
