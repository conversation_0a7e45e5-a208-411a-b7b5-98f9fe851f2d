"""
Product service module.
This module provides business logic for products.
"""
from typing import List, Dict, Optional, Tuple, BinaryIO
import pandas as pd
import tempfile
import os
import logging
from app.repositories.product_repository import ProductRepository
from app.schemas.product_schema import product_schema, products_schema

# Configure logging
logger = logging.getLogger(__name__)

class ProductService:
    """Service for product-related operations."""

    def __init__(self):
        """Initialize the service with repositories."""
        self.product_repo = ProductRepository()

    def get_all_products(self, page: int = 1, per_page: int = 20) -> Tuple[List[Dict], int]:
        """
        Get all products with pagination.

        Args:
            page: Page number
            per_page: Number of items per page

        Returns:
            Tuple of products list and total count
        """
        products, total = self.product_repo.get_all(page, per_page)
        return [product.to_dict() for product in products], total

    def get_product_by_id(self, product_id: int) -> Dict:
        """
        Get a product by ID.

        Args:
            product_id: Product ID

        Returns:
            Product data

        Raises:
            Exception: If product not found
        """
        product = self.product_repo.get_by_id(product_id)
        if not product:
            raise Exception("Product not found")
        return product.to_dict()

    def search_products(self, search_term: str, page: int = 1, per_page: int = 20, category: str = None) -> Tuple[List[Dict], int]:
        """
        Search products by name, description, or product code.

        Args:
            search_term: Search term
            page: Page number
            per_page: Number of items per page
            category: Filter by category (optional)

        Returns:
            Tuple of products list and total count
        """
        products, total = self.product_repo.search(search_term, page, per_page, category)
        return [product.to_dict() for product in products], total

    def get_products_by_category(self, category: str, page: int = 1, per_page: int = 20) -> Tuple[List[Dict], int]:
        """
        Get products by category.

        Args:
            category: Category to filter by
            page: Page number
            per_page: Number of items per page

        Returns:
            Tuple of products list and total count
        """
        products, total = self.product_repo.get_by_category(category, page, per_page)
        return [product.to_dict() for product in products], total

    def get_all_categories(self) -> List[str]:
        """
        Get all unique categories.

        Returns:
            List of unique categories
        """
        return self.product_repo.get_all_categories()

    def create_product(self, product_data: Dict) -> Dict:
        """
        Create a new product.

        Args:
            product_data: Product data

        Returns:
            Created product data
        """
        # Validate with schema
        errors = product_schema.validate(product_data)
        if errors:
            raise Exception(f"Validation failed: {errors}")

        # Create product
        product = self.product_repo.create(product_data)
        return product.to_dict()

    def update_product(self, product_id: int, product_data: Dict) -> Dict:
        """
        Update a product.

        Args:
            product_id: Product ID
            product_data: Updated product data

        Returns:
            Updated product data

        Raises:
            Exception: If product not found or validation fails
        """
        # Get product
        product = self.product_repo.get_by_id(product_id)
        if not product:
            raise Exception("Product not found")

        # Validate with schema
        errors = product_schema.validate(product_data)
        if errors:
            raise Exception(f"Validation failed: {errors}")

        # Update product
        updated_product = self.product_repo.update(product, product_data)
        return updated_product.to_dict()

    def delete_product(self, product_id: int) -> bool:
        """
        Delete a product.

        Args:
            product_id: Product ID

        Returns:
            True if deleted, False if not found
        """
        return self.product_repo.delete(product_id)

    def delete_all_products(self) -> int:
        """
        Delete all products.

        Returns:
            Number of deleted products
        """
        return self.product_repo.delete_all()

    def delete_products_by_category(self, category: str) -> int:
        """
        Delete all products in a specific category.

        Args:
            category: Category to delete products from

        Returns:
            Number of deleted products
        """
        return self.product_repo.delete_by_category(category)

    def import_products_from_excel(self, file: BinaryIO) -> Dict:
        """
        Import products from an Excel file.

        Args:
            file: Excel file object

        Returns:
            Import results

        Raises:
            Exception: If import fails
        """
        # Save the file to a temporary location
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        file.save(temp_file.name)
        temp_file.close()

        try:
            # Read the Excel file
            df = pd.read_excel(temp_file.name)

            # Log the column names for debugging
            logger.info(f"Excel columns: {df.columns.tolist()}")

            # Check if required columns exist
            required_columns = ["Artikelnr", "Artikel", "Bruto prijs"]
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise Exception(f"Missing required columns: {', '.join(missing_columns)}")

            # Track import results
            imported_count = 0
            updated_count = 0
            failed_count = 0
            error_messages = []

            # Process each row
            for index, row in df.iterrows():
                try:
                    # Convert row to dict and clean up NaN values
                    row_dict = row.to_dict()
                    row_dict = {k: (v if pd.notna(v) else None) for k, v in row_dict.items()}

                    # Log the row data for debugging
                    logger.info(f"Processing row {index}: {row_dict}")

                    # Extract and convert values with better error handling
                    try:
                        product_code = str(row_dict.get("Artikelnr", "")).strip() if row_dict.get("Artikelnr") else None
                    except:
                        product_code = None

                    try:
                        name = str(row_dict.get("Artikel", "")).strip() if row_dict.get("Artikel") else None
                    except:
                        name = None

                    try:
                        # Handle different formats for gross_price
                        gross_price_val = row_dict.get("Bruto prijs")
                        if isinstance(gross_price_val, str):
                            # Remove any non-numeric characters except decimal point
                            gross_price_val = ''.join(c for c in gross_price_val if c.isdigit() or c == '.')
                            gross_price = float(gross_price_val) if gross_price_val else None
                        else:
                            gross_price = float(gross_price_val) if gross_price_val is not None else None
                    except:
                        gross_price = None

                    try:
                        # Handle different formats for discount_percentage
                        discount_val = row_dict.get("Korting")
                        if isinstance(discount_val, str):
                            # Remove % and convert to float
                            discount_val = discount_val.replace('%', '')
                            discount_percentage = float(discount_val) if discount_val else None
                        else:
                            discount_percentage = float(discount_val) if discount_val is not None else None
                    except:
                        discount_percentage = None

                    try:
                        # Handle different formats for net_price
                        net_price_val = row_dict.get("Netto prijs")
                        if isinstance(net_price_val, str):
                            # Remove any non-numeric characters except decimal point
                            net_price_val = ''.join(c for c in net_price_val if c.isdigit() or c == '.')
                            net_price = float(net_price_val) if net_price_val else None
                        else:
                            net_price = float(net_price_val) if net_price_val is not None else None
                    except:
                        net_price = None

                    try:
                        category = str(row_dict.get("Groep", "")).strip() if row_dict.get("Groep") else None
                    except:
                        category = None

                    try:
                        subcategory = str(row_dict.get("Groepnaam", "")).strip() if row_dict.get("Groepnaam") else None
                    except:
                        subcategory = None

                    try:
                        info = str(row_dict.get("Info", "")).strip() if row_dict.get("Info") else None
                    except:
                        info = None

                    # Create product data dictionary
                    product_data = {
                        "product_code": product_code,
                        "name": name,
                        "description": name,  # Use name as description
                        "gross_price": gross_price,
                        "discount_percentage": discount_percentage,
                        "net_price": net_price,
                        "category": category,
                        "subcategory": subcategory,
                        "info": info
                    }

                    # Skip rows without a name
                    if not product_data["name"]:
                        logger.warning(f"Skipping row {index}: No product name")
                        continue

                    # Skip rows without a price
                    if product_data["gross_price"] is None and product_data["net_price"] is None:
                        logger.warning(f"Skipping row {index}: No price information")
                        continue

                    # Check if product already exists by product code
                    if product_data["product_code"]:
                        existing_product = self.product_repo.get_by_product_code(product_data["product_code"])
                        if existing_product:
                            # Update existing product
                            self.product_repo.update(existing_product, product_data)
                            updated_count += 1
                            continue

                    # Create new product
                    self.product_repo.create(product_data)
                    imported_count += 1

                except Exception as e:
                    error_msg = f"Failed to import product at row {index}: {str(e)}"
                    logger.error(error_msg)
                    error_messages.append(error_msg)
                    failed_count += 1

            # Log a summary of errors if any
            if error_messages:
                logger.error(f"Import errors summary: {error_messages[:10]}")
                if len(error_messages) > 10:
                    logger.error(f"... and {len(error_messages) - 10} more errors")

            return {
                "message": f"Successfully imported {imported_count} products, updated {updated_count}, failed {failed_count}.",
                "imported": imported_count,
                "updated": updated_count,
                "failed": failed_count,
                "errors": error_messages[:10] if error_messages else []
            }

        except Exception as e:
            logger.error(f"Failed to process Excel file: {str(e)}")
            raise Exception(f"Failed to process Excel file: {str(e)}")

        finally:
            # Clean up the temporary file
            try:
                os.unlink(temp_file.name)
            except Exception as e:
                logger.warning(f"Failed to delete temporary file: {str(e)}")

    def export_products_to_excel(self) -> str:
        """
        Export all products to an Excel file.

        Returns:
            Path to the generated Excel file

        Raises:
            Exception: If export fails
        """
        try:
            # Get all products
            products, _ = self.product_repo.get_all(page=1, per_page=10000)

            if not products:
                raise Exception("No products found")

            # Create a pandas DataFrame
            products_data = [product.to_dict() for product in products]
            df = pd.DataFrame(products_data)

            # Rename columns to match the OSEC format
            column_mapping = {
                "product_code": "Artikelnr",
                "name": "Artikel",
                "description": "Artikel",  # Use name as description
                "gross_price": "Bruto prijs",
                "discount_percentage": "Korting",
                "net_price": "Netto prijs",
                "category": "Groep",
                "subcategory": "Groepnaam",
                "info": "Info"
            }

            # Select and rename columns
            df = df[list(column_mapping.keys())]
            df = df.rename(columns=column_mapping)

            # Create a temporary file to save the Excel file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
            temp_file.close()

            # Save the DataFrame to an Excel file
            df.to_excel(temp_file.name, index=False, engine='openpyxl')

            return temp_file.name

        except Exception as e:
            logger.error(f"Failed to export products to Excel: {str(e)}")
            raise Exception(f"Failed to export products to Excel: {str(e)}")
