"""
Service for handling digital signatures
"""
import base64
import io
import uuid
from typing import <PERSON>ple, Optional
from flask import current_app
from app.utils.firebase import upload_bytes_to_storage
import logging

logger = logging.getLogger(__name__)


class SignatureService:
    """Service for handling digital signature operations."""
    
    @staticmethod
    def save_signature(signature_data: str, customer_id: int, document_type: str = 'signature') -> Tuple[str, str]:
        """
        Save a base64 signature image to Firebase Storage.
        
        Args:
            signature_data: Base64 encoded signature image
            customer_id: ID of the customer
            document_type: Type of document (default: 'signature')
            
        Returns:
            Tuple of (file_url, storage_path)
        """
        try:
            # Validate input
            if not signature_data or not signature_data.startswith('data:image/'):
                raise ValueError("Invalid signature data format")
            
            # Extract the base64 data
            header, encoded = signature_data.split(',', 1)
            
            # Determine image format
            if 'png' in header:
                file_extension = '.png'
                content_type = 'image/png'
            elif 'jpeg' in header or 'jpg' in header:
                file_extension = '.jpg'
                content_type = 'image/jpeg'
            else:
                # Default to PNG
                file_extension = '.png'
                content_type = 'image/png'
            
            # Decode base64 to bytes
            image_bytes = base64.b64decode(encoded)
            
            # Generate unique filename
            filename = f"signature_{uuid.uuid4()}{file_extension}"
            storage_path = f"signatures/{customer_id}/{filename}"
            
            # Upload to Firebase Storage
            file_url, final_storage_path = upload_bytes_to_storage(
                image_bytes,
                storage_path,
                content_type=content_type
            )
            
            logger.info(f"Signature saved successfully: {final_storage_path}")
            return file_url, final_storage_path
            
        except Exception as e:
            logger.error(f"Failed to save signature: {str(e)}")
            raise Exception(f"Failed to save signature: {str(e)}")
    
    @staticmethod
    def validate_signature(signature_data: str) -> bool:
        """
        Validate that a signature contains actual drawing data.
        
        Args:
            signature_data: Base64 encoded signature image
            
        Returns:
            True if signature is valid and not empty
        """
        try:
            if not signature_data or not signature_data.startswith('data:image/'):
                return False
            
            # Extract the base64 data
            header, encoded = signature_data.split(',', 1)
            
            # Check if the base64 data is substantial enough
            # Empty signatures are usually very small
            if len(encoded) < 100:  # Arbitrary threshold
                return False
            
            # Try to decode to ensure it's valid base64
            base64.b64decode(encoded)
            return True
            
        except Exception as e:
            logger.warning(f"Signature validation failed: {str(e)}")
            return False
    
    @staticmethod
    def create_signature_placeholder() -> str:
        """
        Create a placeholder image for missing signatures.
        
        Returns:
            Base64 encoded placeholder image
        """
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # Create a simple placeholder image
            width, height = 150, 75
            image = Image.new('RGB', (width, height), color='#f5f5f5')
            draw = ImageDraw.Draw(image)
            
            # Add border
            draw.rectangle([(0, 0), (width-1, height-1)], outline='#d1d5db')
            
            # Add text
            try:
                # Try to use a default font
                font = ImageFont.load_default()
            except:
                font = None
            
            text = "Niet ondertekend"
            if font:
                # Get text size for centering
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                x = (width - text_width) // 2
                y = (height - text_height) // 2
                draw.text((x, y), text, fill='#9ca3af', font=font)
            else:
                # Fallback without font
                draw.text((width//4, height//2), text, fill='#9ca3af')
            
            # Convert to base64
            buffer = io.BytesIO()
            image.save(buffer, format='PNG')
            buffer.seek(0)
            
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            return f"data:image/png;base64,{image_base64}"
            
        except ImportError:
            # If PIL is not available, return a simple data URL
            logger.warning("PIL not available, using simple placeholder")
            # This is a minimal 1x1 transparent PNG
            minimal_png = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            return f"data:image/png;base64,{minimal_png}"
        except Exception as e:
            logger.error(f"Failed to create signature placeholder: {str(e)}")
            # Return minimal placeholder
            minimal_png = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            return f"data:image/png;base64,{minimal_png}"
    
    @staticmethod
    def process_template_signatures(data: dict) -> dict:
        """
        Process signature data in template data for document generation.
        
        Args:
            data: Template data containing signature fields
            
        Returns:
            Processed data with signatures converted for document generation
        """
        processed_data = data.copy()
        
        # List of known signature fields
        signature_fields = ['klant_handtekening', 'monteur_handtekening']
        
        for field in signature_fields:
            if field in processed_data:
                signature_data = processed_data[field]
                
                if SignatureService.validate_signature(signature_data):
                    # Keep the signature as-is for frontend processing
                    # The frontend will handle conversion to image format
                    pass
                else:
                    # Replace empty or invalid signatures with placeholder
                    processed_data[field] = SignatureService.create_signature_placeholder()
        
        return processed_data
