from app.repositories.user_repository import UserRepository
from app.services.audit_service import AuditService
from app.utils.cache_utils import clear_cache_for_entity
from app import db
import firebase_admin
from firebase_admin import auth
from typing import List, Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class UserService:
    def __init__(self):
        self.user_repo = UserRepository()
        self.audit_service = AuditService()

    def get_all_users(self, page: int = 1, per_page: int = 20) -> Tuple[List[Dict], int]:
        users, total = self.user_repo.get_all(page, per_page)
        return [user.to_dict() for user in users], total

    def get_user_by_id(self, user_id: int) -> Dict:
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise Exception("User not found")
        return user.to_dict()

    def create_user(self, email: str, password: str, role: str, name: str = None, current_user_id: int = None, ip_address: str = None, user_agent: str = None) -> Dict:
        role = role.lower()
        try:
            firebase_user = auth.create_user(email=email, password=password)
        except Exception as e:
            raise Exception(f"Failed to create user in Firebase: {str(e)}")
        try:
            auth.set_custom_user_claims(firebase_user.uid, {"role": role})
            user = self.user_repo.create(firebase_user.uid, email, role, name)

            user_dict = user.to_dict()

            # Log the action
            self.audit_service.log_action(
                user_id=current_user_id,
                action='create',
                entity_type='user',
                entity_id=user.id,
                details={
                    'email': email,
                    'role': role,
                    'name': name
                },
                ip_address=ip_address,
                user_agent=user_agent
            )

            # Clear cache
            clear_cache_for_entity('user')

            return user_dict
        except Exception as e:
            auth.delete_user(firebase_user.uid)
            raise Exception(f"Failed to create user: {str(e)}")

    def update_user_role(self, user_id: int, new_role: str, current_user_id: int = None, ip_address: str = None, user_agent: str = None) -> Dict:
        new_role = new_role.lower()
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise Exception("User not found")
        user = self.user_repo.update_role(user, new_role)
        try:
            auth.set_custom_user_claims(user.firebase_uid, {"role": new_role})
        except Exception as e:
            raise Exception(f"Failed to update role in Firebase: {str(e)}")
        user_dict = user.to_dict()

        # Log the action
        self.audit_service.log_action(
            user_id=current_user_id,
            action='update_role',
            entity_type='user',
            entity_id=user_id,
            details={
                'old_role': user.role,
                'new_role': new_role,
                'email': user.email
            },
            ip_address=ip_address,
            user_agent=user_agent
        )

        # Clear cache
        clear_cache_for_entity('user', user_id)

        return user_dict

    def update_user_name(self, user_id: int, new_name: str, current_user_id: int = None, ip_address: str = None, user_agent: str = None) -> Dict:
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise Exception("User not found")
        user = self.user_repo.update_name(user, new_name)
        user_dict = user.to_dict()

        # Log the action
        self.audit_service.log_action(
            user_id=current_user_id,
            action='update_name',
            entity_type='user',
            entity_id=user_id,
            details={
                'old_name': user.name,
                'new_name': new_name,
                'email': user.email
            },
            ip_address=ip_address,
            user_agent=user_agent
        )

        # Clear cache
        clear_cache_for_entity('user', user_id)

        return user_dict

    def update_user_password(self, user_id: int, new_password: str, current_user_id: int = None, ip_address: str = None, user_agent: str = None) -> Dict:
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise Exception("User not found")

        # Only allow users to update their own password or administrators to update any password
        if current_user_id != user_id:
            current_user = self.user_repo.get_by_id(current_user_id)
            if not current_user or current_user.role != "administrator":
                raise Exception("You can only update your own password")

        try:
            # Update password in Firebase
            auth.update_user(user.firebase_uid, password=new_password)

            # Log the action (don't log the password)
            self.audit_service.log_action(
                user_id=current_user_id,
                action='update_password',
                entity_type='user',
                entity_id=user_id,
                details={
                    'email': user.email,
                },
                ip_address=ip_address,
                user_agent=user_agent
            )

            return user.to_dict()
        except Exception as e:
            raise Exception(f"Failed to update password in Firebase: {str(e)}")

    def get_linked_data_count(self, user_id: int) -> Dict:
        """Get count of all data linked to a user."""
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise Exception("User not found")

        try:
            # Import all models that might reference this user
            from app.models.document import Document
            from app.models.event import Event, event_users
            from app.models.customer_note import CustomerNote
            from app.models.quotation import Quotation
            from app.models.time_entry import TimeEntry
            from app.models.mileage_entry import MileageEntry

            # Get counts
            documents_count = Document.query.filter_by(uploaded_by=user_id).count()
            events_assigned_count = db.session.query(event_users).filter_by(user_id=user_id).count()
            events_completed_count = Event.query.filter_by(completed_by=user_id).count()
            notes_count = CustomerNote.query.filter_by(user_id=user_id).count()
            quotations_count = Quotation.query.filter_by(created_by=user_id).count()
            time_entries_count = TimeEntry.query.filter_by(user_id=user_id).count()
            time_approvals_count = TimeEntry.query.filter_by(approved_by=user_id).count()
            mileage_entries_count = MileageEntry.query.filter_by(user_id=user_id).count()
            mileage_approvals_count = MileageEntry.query.filter_by(approved_by=user_id).count()

            # Count sub-documents for documents uploaded by this user
            sub_documents_count = 0
            documents = Document.query.filter_by(uploaded_by=user_id).all()
            for document in documents:
                sub_documents_count += Document.query.filter_by(related_document_id=document.id).count()

            return {
                "user_name": user.name or user.email,
                "user_email": user.email,
                "documents": documents_count,
                "sub_documents": sub_documents_count,
                "events_assigned": events_assigned_count,
                "events_completed": events_completed_count,
                "notes": notes_count,
                "quotations_created": quotations_count,
                "time_entries": time_entries_count,
                "time_approvals": time_approvals_count,
                "mileage_entries": mileage_entries_count,
                "mileage_approvals": mileage_approvals_count,
                "total_items": (documents_count + sub_documents_count + events_assigned_count +
                               events_completed_count + notes_count + quotations_count +
                               time_entries_count + time_approvals_count + mileage_entries_count + mileage_approvals_count)
            }

        except Exception as e:
            logger.error(f"Error getting linked data count for user {user_id}: {str(e)}")
            raise Exception(f"Failed to get linked data count: {str(e)}")

    def delete_user(self, user_id: int, current_user_id: int = None, ip_address: str = None, user_agent: str = None) -> bool:
        user = self.user_repo.get_by_id(user_id)
        if not user:
            raise Exception("User not found")

        try:
            # Import all models that might reference this user
            from app.models.document import Document
            from app.models.event import Event, event_users
            from app.models.customer_note import CustomerNote
            from app.models.quotation import Quotation
            from app.models.time_entry import TimeEntry
            from app.models.mileage_entry import MileageEntry
            from app.models.audit_log import AuditLog

            # Get counts for logging
            documents_count = Document.query.filter_by(uploaded_by=user_id).count()
            events_assigned_count = db.session.query(event_users).filter_by(user_id=user_id).count()
            events_completed_count = Event.query.filter_by(completed_by=user_id).count()
            notes_count = CustomerNote.query.filter_by(user_id=user_id).count()
            quotations_count = Quotation.query.filter_by(created_by=user_id).count()
            time_entries_count = TimeEntry.query.filter_by(user_id=user_id).count()
            time_approvals_count = TimeEntry.query.filter_by(approved_by=user_id).count()
            mileage_entries_count = MileageEntry.query.filter_by(user_id=user_id).count()
            mileage_approvals_count = MileageEntry.query.filter_by(approved_by=user_id).count()
            audit_logs_count = AuditLog.query.filter_by(user_id=user_id).count()

            logger.info(f"Deleting user {user_id} ({user.email}) with {documents_count} documents, {events_assigned_count} assigned events, {events_completed_count} completed events, {notes_count} notes, {quotations_count} quotations, {time_entries_count} time entries, {time_approvals_count} time approvals, {mileage_entries_count} mileage entries, {mileage_approvals_count} mileage approvals, {audit_logs_count} audit logs")

            # Delete from Firebase first
            auth.delete_user(user.firebase_uid)

            # Delete user permissions
            self.permission_repo.delete_by_user_id(user_id)

            # Delete all documents uploaded by this user
            documents = Document.query.filter_by(uploaded_by=user_id).all()
            for document in documents:
                logger.info(f"Deleting document {document.id} uploaded by user {user_id}")
                # Delete any sub-documents first
                sub_documents = Document.query.filter_by(related_document_id=document.id).all()
                for sub_doc in sub_documents:
                    db.session.delete(sub_doc)
                db.session.delete(document)

            # Remove user from all assigned events (many-to-many relationship)
            db.session.execute(event_users.delete().where(event_users.c.user_id == user_id))

            # Update events completed by this user (set completed_by to NULL)
            events_completed = Event.query.filter_by(completed_by=user_id).all()
            for event in events_completed:
                event.completed_by = None
                logger.info(f"Removed user {user_id} as completer from event {event.id}")

            # Delete all customer notes created by this user
            notes = CustomerNote.query.filter_by(user_id=user_id).all()
            for note in notes:
                logger.info(f"Deleting customer note {note.id} created by user {user_id}")
                db.session.delete(note)

            # Delete all quotations created by this user
            quotations = Quotation.query.filter_by(created_by=user_id).all()
            for quotation in quotations:
                logger.info(f"Deleting quotation {quotation.id} created by user {user_id}")
                db.session.delete(quotation)

            # Delete all time entries by this user
            time_entries = TimeEntry.query.filter_by(user_id=user_id).all()
            for entry in time_entries:
                logger.info(f"Deleting time entry {entry.id} by user {user_id}")
                db.session.delete(entry)

            # Update time entries approved by this user (set approved_by to NULL)
            time_approvals = TimeEntry.query.filter_by(approved_by=user_id).all()
            for entry in time_approvals:
                entry.approved_by = None
                entry.status = "pending"  # Reset to pending since approver is gone
                logger.info(f"Removed user {user_id} as approver from time entry {entry.id}")

            # Delete all mileage entries by this user
            mileage_entries = MileageEntry.query.filter_by(user_id=user_id).all()
            for entry in mileage_entries:
                logger.info(f"Deleting mileage entry {entry.id} by user {user_id}")
                db.session.delete(entry)

            # Update mileage entries approved by this user (set approved_by to NULL)
            mileage_approvals = MileageEntry.query.filter_by(approved_by=user_id).all()
            for entry in mileage_approvals:
                entry.approved_by = None
                entry.status = "pending"  # Reset to pending since approver is gone
                logger.info(f"Removed user {user_id} as approver from mileage entry {entry.id}")

            # Keep audit logs but they will reference a deleted user
            # This is intentional for audit trail purposes

            # Finally delete the user
            result = self.user_repo.delete(user_id)

            # Log the action
            self.audit_service.log_action(
                user_id=current_user_id,
                action='delete',
                entity_type='user',
                entity_id=user_id,
                details={
                    'email': user.email,
                    'role': user.role,
                    'name': user.name,
                    'cascade_deleted': {
                        'documents': documents_count,
                        'events_assigned': events_assigned_count,
                        'events_completed': events_completed_count,
                        'notes': notes_count,
                        'quotations': quotations_count,
                        'time_entries': time_entries_count,
                        'time_approvals': time_approvals_count,
                        'mileage_entries': mileage_entries_count,
                        'mileage_approvals': mileage_approvals_count
                    }
                },
                ip_address=ip_address,
                user_agent=user_agent
            )

            logger.info(f"Successfully deleted user {user_id} and all associated data")

        except Exception as e:
            db.session.rollback()
            logger.error(f"Error deleting user {user_id}: {str(e)}")
            raise Exception(f"Failed to delete user and associated data: {str(e)}")

        # Clear cache
        clear_cache_for_entity('user', user_id)

        return result

