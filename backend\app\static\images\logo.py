from PIL import Image, ImageDraw, ImageFont
import os

# Create a blank image with a white background
width, height = 400, 150
image = Image.new('RGB', (width, height), color='white')

# Get a drawing context
draw = ImageDraw.Draw(image)

# Draw a rectangle border
draw.rectangle([(0, 0), (width-1, height-1)], outline='black')

# Draw text in the center
text = "AMSPM Security"
position = (width // 4, height // 3)
draw.text(position, text, fill='black')

# Save the image
current_dir = os.path.dirname(os.path.abspath(__file__))
image.save(os.path.join(current_dir, 'logo.png'))

print(f"Logo created at {os.path.join(current_dir, 'logo.png')}")
