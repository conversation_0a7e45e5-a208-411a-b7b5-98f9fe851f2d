from functools import wraps
from flask import request, current_app
from app import cache
from app.utils.cache_utils import (
    generate_cache_key,
    cache_key_for_list,
    cache_key_for_detail,
    cache_key_for_search,
    clear_cache_for_entity
)
import logging

logger = logging.getLogger(__name__)

def cached_list(entity_type, timeout=None):
    """
    Decorator for caching list endpoints with pagination and filtering.

    Args:
        entity_type (str): The type of entity being cached (e.g., 'user', 'customer')
        timeout (int, optional): Cache timeout in seconds. If None, uses the default timeout.
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Get query parameters
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)

            # Generate cache key based on entity type, pagination, and any other query params
            cache_key = cache_key_for_list(
                entity_type,
                page=page,
                per_page=per_page,
                **{k: v for k, v in request.args.items() if k not in ['page', 'per_page']}
            )

            # Get cache timeout from config if not provided
            cache_timeout = timeout or current_app.config.get('CACHE_TIMEOUT', 3600)

            # Try to get from cache
            cached_response = cache.get(cache_key)
            if cached_response is not None:
                logger.debug(f"Cache hit for {cache_key}")
                return cached_response

            # If not in cache, call the original function
            logger.debug(f"Cache miss for {cache_key}")
            response = f(*args, **kwargs)

            # Cache the response
            cache.set(cache_key, response, timeout=cache_timeout)
            logger.debug(f"Cached response for {cache_key} with timeout {cache_timeout}s")

            return response
        return decorated_function
    return decorator

def cached_detail(entity_type, timeout=None):
    """
    Decorator for caching detail endpoints.

    Args:
        entity_type (str): The type of entity being cached (e.g., 'user', 'customer')
        timeout (int, optional): Cache timeout in seconds. If None, uses the default timeout.
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Get entity ID from kwargs (assuming it's the first parameter)
            entity_id = next(iter(kwargs.values()), None)
            if entity_id is None:
                # If no entity ID found, just call the original function
                return f(*args, **kwargs)

            # Generate cache key
            cache_key = cache_key_for_detail(entity_type, entity_id)

            # Get cache timeout from config if not provided
            cache_timeout = timeout or current_app.config.get('CACHE_TIMEOUT', 3600)

            # Try to get from cache
            cached_response = cache.get(cache_key)
            if cached_response is not None:
                logger.debug(f"Cache hit for {cache_key}")
                return cached_response

            # If not in cache, call the original function
            logger.debug(f"Cache miss for {cache_key}")
            response = f(*args, **kwargs)

            # Cache the response
            cache.set(cache_key, response, timeout=cache_timeout)
            logger.debug(f"Cached response for {cache_key} with timeout {cache_timeout}s")

            return response
        return decorated_function
    return decorator

def cached_search(entity_type, timeout=None):
    """
    Decorator for caching search endpoints.

    Args:
        entity_type (str): The type of entity being searched (e.g., 'user', 'customer')
        timeout (int, optional): Cache timeout in seconds. If None, uses the default timeout.
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Get search term from query parameters
            search_term = request.args.get('q', '')
            if not search_term:
                # If no search term, just call the original function
                return f(*args, **kwargs)

            # Generate cache key
            cache_key = cache_key_for_search(
                entity_type,
                search_term=search_term,
                **{k: v for k, v in request.args.items() if k != 'q'}
            )

            # Get cache timeout from config if not provided
            cache_timeout = timeout or current_app.config.get('CACHE_TIMEOUT', 1800)  # Default 30 minutes for search

            # Try to get from cache
            cached_response = cache.get(cache_key)
            if cached_response is not None:
                logger.debug(f"Cache hit for {cache_key}")
                return cached_response

            # If not in cache, call the original function
            logger.debug(f"Cache miss for {cache_key}")
            response = f(*args, **kwargs)

            # Cache the response
            cache.set(cache_key, response, timeout=cache_timeout)
            logger.debug(f"Cached response for {cache_key} with timeout {cache_timeout}s")

            return response
        return decorated_function
    return decorator
