from app import cache
from flask import current_app
import logging
import hashlib
import json

logger = logging.getLogger(__name__)

def generate_cache_key(entity_type, entity_id=None, action=None, **kwargs):
    """
    Generate a standardized cache key with versioning.

    Args:
        entity_type (str): The type of entity (e.g., 'user', 'customer', 'event', 'document', 'template')
        entity_id (int, optional): The ID of the specific entity.
        action (str, optional): The action or view (e.g., 'list', 'detail', 'search')
        **kwargs: Additional parameters to include in the cache key (e.g., page, per_page, search_term)

    Returns:
        str: A cache key string
    """
    try:
        # Get cache version from config
        cache_version = current_app.config.get('CACHE_VERSION', '1.0')
        prefix = current_app.config.get('CACHE_KEY_PREFIX', 'customer_mgmt_')

        # Build the base key
        key_parts = [prefix, entity_type]

        if entity_id is not None:
            key_parts.append(str(entity_id))

        if action is not None:
            key_parts.append(action)

        # Add version
        key_parts.append(f"v{cache_version}")

        # Create base key
        base_key = ':'.join(key_parts)

        # If we have additional parameters, hash them and append to the key
        if kwargs:
            # Sort the kwargs to ensure consistent ordering
            kwargs_str = json.dumps(kwargs, sort_keys=True)
            kwargs_hash = hashlib.md5(kwargs_str.encode()).hexdigest()[:8]
            return f"{base_key}:{kwargs_hash}"

        return base_key
    except Exception as e:
        logger.error(f"Error generating cache key: {str(e)}")
        # Fallback to a simple key if there's an error
        return f"{entity_type}_{entity_id or 'all'}"

def clear_cache_for_entity(entity_type, entity_id=None):
    """
    Clear cache entries related to a specific entity type and optionally a specific entity ID.

    Args:
        entity_type (str): The type of entity (e.g., 'user', 'customer', 'event', 'document', 'template')
        entity_id (int, optional): The ID of the specific entity. If None, clears all caches for the entity type.
    """
    try:
        prefix = current_app.config.get('CACHE_KEY_PREFIX', 'customer_mgmt_')

        # If entity_id is provided, clear specific entity cache
        if entity_id is not None:
            # Clear detail view cache
            detail_key = generate_cache_key(entity_type, entity_id, 'detail')
            cache.delete(detail_key)

            # Also clear any related list caches that might contain this entity
            list_key_pattern = f"{prefix}{entity_type}:list:*"
            # Note: This pattern matching requires Redis cache backend
            # For SimpleCache, we'll need to clear all cache
            if current_app.config.get('CACHE_TYPE') == 'redis':
                # This is a Redis-specific operation
                redis_client = cache.cache._client
                for key in redis_client.scan_iter(list_key_pattern):
                    redis_client.delete(key)
            else:
                # For SimpleCache, we need to clear everything related to this entity type
                cache.clear()

            logger.info(f"Cleared cache for {entity_type} with ID {entity_id}")
        else:
            # Clear all caches for this entity type
            if current_app.config.get('CACHE_TYPE') == 'redis':
                redis_client = cache.cache._client
                entity_key_pattern = f"{prefix}{entity_type}:*"
                for key in redis_client.scan_iter(entity_key_pattern):
                    redis_client.delete(key)
            else:
                # For SimpleCache, we need to clear everything
                cache.clear()

            logger.info(f"Cleared all cache entries for {entity_type}")
    except Exception as e:
        logger.error(f"Failed to clear cache for {entity_type}: {str(e)}")
        # Fallback to clearing all cache if there's an error
        try:
            cache.clear()
        except Exception as clear_error:
            logger.error(f"Failed to clear all cache: {str(clear_error)}")

def clear_all_cache():
    """
    Clear the entire cache.
    """
    try:
        cache.clear()
        logger.info("Cleared all cache entries")
    except Exception as e:
        logger.error(f"Failed to clear all cache: {str(e)}")

def cache_key_for_list(entity_type, **kwargs):
    """
    Generate a cache key for list views with pagination and filtering.

    Args:
        entity_type (str): The type of entity (e.g., 'user', 'customer', 'event', 'document')
        **kwargs: Query parameters (page, per_page, filters, etc.)

    Returns:
        str: A cache key string
    """
    return generate_cache_key(entity_type, action='list', **kwargs)

def cache_key_for_detail(entity_type, entity_id):
    """
    Generate a cache key for detail views.

    Args:
        entity_type (str): The type of entity (e.g., 'user', 'customer', 'event', 'document')
        entity_id (int): The ID of the entity

    Returns:
        str: A cache key string
    """
    return generate_cache_key(entity_type, entity_id, action='detail')

def cache_key_for_search(entity_type, search_term, **kwargs):
    """
    Generate a cache key for search results.

    Args:
        entity_type (str): The type of entity (e.g., 'user', 'customer', 'event', 'document')
        search_term (str): The search term
        **kwargs: Additional query parameters

    Returns:
        str: A cache key string
    """
    return generate_cache_key(entity_type, action='search', search_term=search_term, **kwargs)
