from functools import wraps
from app import csrf
import logging

logger = logging.getLogger(__name__)

def csrf_exempt(view):
    """
    Decorator to exempt a view from CSRF protection.
    Use this for API endpoints that need to be accessed by external services.
    """
    @wraps(view)
    def decorated(*args, **kwargs):
        return view(*args, **kwargs)
    
    decorated._csrf_exempt = True
    logger.info(f"CSRF protection exempted for view: {view.__name__}")
    return csrf.exempt(decorated)
