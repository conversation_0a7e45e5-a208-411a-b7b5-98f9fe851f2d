"""
Rate limiting utilities using custom rate limiter.

This module provides a rate limiting decorator that uses our custom
sliding window rate limiter instead of Flask-Limiter to avoid conflicts
between global and endpoint-specific rate limits.
"""

from app.utils.custom_rate_limiter import rate_limit
import logging

logger = logging.getLogger(__name__)

# Export the rate_limit decorator from our custom implementation
__all__ = ['rate_limit']

# Log that we're using the custom rate limiter
logger.info("Using custom rate limiter implementation")
