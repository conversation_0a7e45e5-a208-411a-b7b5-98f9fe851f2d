"""
Response sanitizer utility.

This module provides functions to sanitize API responses by removing sensitive information
before sending them to the client.
"""
import logging
from flask import jsonify, Response
import json

logger = logging.getLogger(__name__)

# List of sensitive fields that should be completely redacted from responses
SENSITIVE_FIELDS = [
    'password',
    'token',
    'secret',
    'key',
    'firebase_uid',
    'permissions',
    # 'document_types' is removed from the list because administrators need to see it
    'can_upload',
    'can_view'
]

def is_sensitive_field(field_name: str) -> bool:
    """
    Check if a field name is considered sensitive.

    Args:
        field_name: The name of the field to check

    Returns:
        bool: True if the field is sensitive, False otherwise
    """
    field_name_lower = field_name.lower()
    return any(sensitive in field_name_lower for sensitive in SENSITIVE_FIELDS)

# We no longer need the is_structure_field function since we're treating all sensitive fields the same

def sanitize_dict(data: dict) -> dict:
    """
    Sanitize a dictionary by removing or redacting sensitive fields.

    Args:
        data: The dictionary to sanitize

    Returns:
        dict: The sanitized dictionary
    """
    sanitized = {}

    for key, value in data.items():
        if is_sensitive_field(key):
            # For sensitive fields, completely redact the value
            sanitized[key] = "***"
        elif isinstance(value, dict):
            # Recursively sanitize nested dictionaries
            sanitized[key] = sanitize_dict(value)
        elif isinstance(value, list):
            # Recursively sanitize lists of dictionaries
            sanitized[key] = [
                sanitize_dict(item) if isinstance(item, dict) else item
                for item in value
            ]
        else:
            # Keep non-sensitive values as is
            sanitized[key] = value

    return sanitized

def sanitize_response(response: Response) -> Response:
    """
    Sanitize a Flask response by removing sensitive information.

    Args:
        response: The Flask response to sanitize

    Returns:
        Response: The sanitized response
    """
    # Only process JSON responses
    if response.content_type != 'application/json':
        return response

    try:
        # Parse the response data
        data = json.loads(response.get_data(as_text=True))

        # Sanitize the data
        if isinstance(data, dict):
            sanitized_data = sanitize_dict(data)
        elif isinstance(data, list):
            sanitized_data = [
                sanitize_dict(item) if isinstance(item, dict) else item
                for item in data
            ]
        else:
            # If it's not a dict or list, just return the original response
            return response

        # Create a new response with the sanitized data
        response.set_data(json.dumps(sanitized_data))

    except Exception as e:
        logger.error(f"Error sanitizing response: {str(e)}")

    return response
