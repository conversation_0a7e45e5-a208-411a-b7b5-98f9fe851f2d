from functools import wraps
from flask import request, jsonify
import firebase_admin
from firebase_admin import auth
from app.models.user import User
import logging

logger = logging.getLogger(__name__)

def token_required(f):
    """
    Decorator to verify Firebase authentication token from cookies, request header, or query parameter.
    Prioritizes the HttpOnly cookie for better security.
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = None

        # Debug logging for request details
        logger.debug(f"Request URL: {request.url}")
        logger.debug(f"Request headers: {dict(request.headers)}")
        logger.debug(f"Request cookies: {dict(request.cookies)}")

        # First check for token in cookie (both HttpOnly and regular)
        if request.cookies.get('auth_token'):
            token = request.cookies.get('auth_token')
            logger.info("Using token from auth_token cookie")
        # Check for token in Authorization header (Bearer token)
        elif request.headers.get('Authorization'):
            auth_header = request.headers.get('Authorization')
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
                logger.info("Using token from Authorization header")
            else:
                logger.warning(f"Invalid Authorization header format: {auth_header}")
        # Check for token in request headers (X-Auth-Token)
        elif request.headers.get('X-Auth-Token'):
            token = request.headers.get('X-Auth-Token')
            logger.info("Using token from X-Auth-Token header")
        # Last fallback to query parameter (for file downloads)
        elif request.args.get('token'):
            token = request.args.get('token')
            logger.info("Using token from query parameter")

        if not token:
            logger.warning(f"No token provided in request to {request.url}")
            logger.warning(f"Available cookies: {list(request.cookies.keys())}")
            logger.warning(f"Available headers: {list(request.headers.keys())}")
            return jsonify({"error": "Token is missing"}), 401

        try:
            decoded_token = auth.verify_id_token(token)
            logger.info(f"Token verified for user: {decoded_token.get('email', 'unknown')}")
            request.user = decoded_token

            # Also set current_user for convenience
            user = User.query.filter_by(firebase_uid=decoded_token["uid"]).first()
            if user:
                request.current_user = user
        except auth.ExpiredIdTokenError:
            logger.error("Token verification failed: Token has expired")
            return jsonify({"error": "Token has expired"}), 401
        except auth.InvalidIdTokenError:
            logger.error("Token verification failed: Invalid token")
            return jsonify({"error": "Invalid token"}), 401
        except Exception as e:
            logger.error(f"Token verification failed: {str(e)}")
            return jsonify({"error": f"Token verification failed: {str(e)}"}), 401

        return f(*args, **kwargs)
    return decorated_function

def role_required(role):
    """
    Decorator to restrict access to a specific role.
    """
    def decorator(f):
        @wraps(f)
        @token_required
        def decorated_function(*args, **kwargs):
            user_data = request.user
            user = User.query.filter_by(firebase_uid=user_data["uid"]).first()
            if not user:
                logger.error(
                    f"User not found in database for Firebase UID: {user_data['uid']}. "
                    "Ensure the user was created via the application (admin-only)."
                )
                return jsonify({"error": "User not found in database. Contact an administrator to create your account."}), 403
            if user.role != role:
                logger.warning(
                    f"Access denied for user {user_data.get('email', 'unknown')} "
                    f"with role {user.role} (required role: {role})"
                )
                return jsonify({"error": "Access denied"}), 403

            logger.info(f"Access granted for user {user.email} with role {user.role}")
            request.current_user = user
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def roles_required(*allowed_roles):
    """
    Decorator to restrict access to a list of allowed roles.
    Args:
        allowed_roles: Variable number of roles that are allowed to access the endpoint.
    """
    def decorator(f):
        @wraps(f)
        @token_required
        def decorated_function(*args, **kwargs):
            user_data = request.user
            user = User.query.filter_by(firebase_uid=user_data["uid"]).first()
            if not user:
                logger.error(
                    f"User not found in database for Firebase UID: {user_data['uid']}. "
                    "Ensure the user was created via the application (admin-only)."
                )
                return jsonify({"error": "User not found in database. Contact an administrator to create your account."}), 403
            if user.role not in allowed_roles:
                logger.warning(
                    f"Access denied for user {user_data.get('email', 'unknown')} "
                    f"with role {user.role} (allowed roles: {allowed_roles})"
                )
                return jsonify({"error": "Access denied"}), 403

            logger.info(f"Access granted for user {user.email} with role {user.role}")
            request.current_user = user
            return f(*args, **kwargs)
        return decorated_function
    return decorator