#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create an admin user for the application.
Run this after fixing the schema validation issue.
"""

import os
import sys
from app import create_app, db
from app.models.user import User
from firebase_admin import auth
import logging

def create_admin_user():
    """Create an admin user in both Firebase and the database."""
    
    # Create the Flask app
    app, _ = create_app()
    
    with app.app_context():
        try:
            # Check if Firebase is initialized
            if not app.config.get('FIREBASE_INITIALIZED', False):
                print("❌ Firebase is not initialized. Cannot create admin user.")
                print("Please ensure Firebase credentials are properly configured.")
                return False
            
            # Default admin credentials
            admin_email = os.getenv('ADMIN_EMAIL', '<EMAIL>')
            admin_password = os.getenv('ADMIN_PASSWORD', 'TempPassword123!')
            
            print(f"Creating admin user: {admin_email}")
            
            # Check if admin user already exists in database
            existing_admin = User.query.filter_by(email=admin_email).first()
            if existing_admin:
                print(f"✅ Admin user {admin_email} already exists in database")
                return True
            
            # Check if user exists in Firebase
            firebase_user = None
            try:
                firebase_user = auth.get_user_by_email(admin_email)
                print(f"✅ Admin user {admin_email} already exists in Firebase")
            except auth.UserNotFoundError:
                print(f"Creating admin user in Firebase: {admin_email}")
                try:
                    firebase_user = auth.create_user(
                        email=admin_email,
                        password=admin_password,
                        email_verified=True
                    )
                    print(f"✅ Admin user created in Firebase")
                except Exception as e:
                    print(f"❌ Failed to create admin user in Firebase: {str(e)}")
                    return False
            
            # Set admin role in Firebase custom claims
            try:
                auth.set_custom_user_claims(firebase_user.uid, {"role": "administrator"})
                print(f"✅ Admin role set in Firebase")
            except Exception as e:
                print(f"⚠️  Warning: Failed to set custom claims: {str(e)}")
            
            # Create admin user in database
            try:
                admin_user = User(
                    firebase_uid=firebase_user.uid,
                    email=admin_email,
                    name="Administrator",
                    role="administrator"
                )
                
                db.session.add(admin_user)
                db.session.commit()
                
                print(f"✅ Admin user created in database")
                print(f"📧 Email: {admin_email}")
                print(f"🔑 Password: {admin_password}")
                print(f"⚠️  IMPORTANT: Change the password immediately after first login!")
                
                return True
                
            except Exception as e:
                print(f"❌ Failed to create admin user in database: {str(e)}")
                db.session.rollback()
                return False
                
        except Exception as e:
            print(f"❌ Failed to create admin user: {str(e)}")
            return False

def main():
    """Main function."""
    print("🚀 Creating admin user...")
    
    success = create_admin_user()
    
    if success:
        print("\n🎉 Admin user creation completed successfully!")
        print("\nNext steps:")
        print("1. Try logging in with the admin credentials")
        print("2. Change the admin password immediately")
        print("3. Create additional users as needed")
    else:
        print("\n❌ Admin user creation failed!")
        print("Please check the error messages above and try again.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
