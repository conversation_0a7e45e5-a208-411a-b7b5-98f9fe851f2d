#!/usr/bin/env python3
"""
Production database initialization script.
This script should be run once after deploying to create the database schema
and initial admin user.
"""

import os
import sys
from app import create_app, db
from app.models import User
from firebase_admin import auth
import logging

def init_database():
    """Initialize the production database."""
    try:
        app, _ = create_app()
        from app import db
    except Exception as e:
        print(f"Failed to create app during database initialization: {e}")
        print("This might be due to missing Firebase credentials - continuing with database setup only...")
        # Try to create a minimal app just for database operations
        from flask import Flask
        from flask_sqlalchemy import SQLAlchemy
        from app.config import Config
        app = Flask(__name__)
        app.config.from_object(Config)
        db = SQLAlchemy()
        db.init_app(app)
    
    with app.app_context():
        try:
            # Create all database tables
            print("Creating database tables...")
            db.create_all()
            print("✓ Database tables created successfully")
            
            # Check if admin user exists
            admin_email = os.getenv('ADMIN_EMAIL')
            if not admin_email:
                print("⚠ Warning: ADMIN_EMAIL environment variable not set")
                print("Using default admin email: <EMAIL>")
                admin_email = "<EMAIL>"
            
            existing_admin = User.query.filter_by(email=admin_email).first()
            if existing_admin:
                print(f"✓ Admin user {admin_email} already exists")
                return
            
            # Create admin user in Firebase (if Firebase is available)
            if app.config.get('FIREBASE_INITIALIZED', False):
                print(f"Creating admin user in Firebase: {admin_email}")
                try:
                    firebase_user = auth.create_user(
                        email=admin_email,
                        password="TempPassword123!",  # User should change this immediately
                        email_verified=True
                    )

                    # Set admin role in Firebase custom claims
                    auth.set_custom_user_claims(firebase_user.uid, {"role": "administrator"})

                    # Create admin user in database
                    admin_user = User(
                        firebase_uid=firebase_user.uid,
                        email=admin_email,
                        name="Administrator",
                        role="administrator"
                    )

                    db.session.add(admin_user)
                    db.session.commit()

                    print(f"✓ Admin user created successfully")
                    print(f"⚠ IMPORTANT: Admin password is 'TempPassword123!' - CHANGE IT IMMEDIATELY!")

                except Exception as e:
                    print(f"✗ Failed to create admin user: {str(e)}")
            else:
                print("⚠ Firebase not initialized - skipping admin user creation")
                print("You'll need to set up Firebase credentials and run this script again to create the admin user")
                
        except Exception as e:
            print(f"✗ Database initialization failed: {str(e)}")
            sys.exit(1)

def run_migrations():
    """Run any pending database migrations."""
    app, _ = create_app()
    
    with app.app_context():
        try:
            # Import and run migration scripts
            from migrations.create_document_templates_table import run_migration as create_doc_templates
            from migrations.increase_file_url_column_size import run_migration as increase_file_url
            
            print("Running database migrations...")
            
            # Run migrations
            create_doc_templates()
            increase_file_url()
            
            print("✓ Database migrations completed successfully")
            
        except Exception as e:
            print(f"⚠ Migration warning: {str(e)}")
            # Don't fail the entire process for migration issues

if __name__ == "__main__":
    print("Initializing production database...")
    
    # Check required environment variables
    required_vars = ['DATABASE_URL', 'SECRET_KEY', 'FIREBASE_CREDENTIALS_JSON', 'FIREBASE_STORAGE_BUCKET']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"✗ Missing required environment variables: {', '.join(missing_vars)}")
        sys.exit(1)
    
    # Run migrations first
    run_migrations()
    
    # Initialize database
    init_database()
    
    print("✓ Production database initialization completed")
