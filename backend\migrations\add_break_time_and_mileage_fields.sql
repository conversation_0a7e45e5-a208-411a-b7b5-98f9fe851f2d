-- Migration to add break_time to time_entries and new fields to mileage_entries
-- Run this migration to update the database schema

-- Add break_time column to time_entries table
ALTER TABLE time_entries 
ADD COLUMN break_time INTEGER DEFAULT 0;

-- Add new columns to mileage_entries table
ALTER TABLE mileage_entries 
ADD COLUMN license_plate VARCHAR(20) NOT NULL DEFAULT '',
ADD COLUMN start_odometer INTEGER NOT NULL DEFAULT 0,
ADD COLUMN end_odometer INTEGER NOT NULL DEFAULT 0,
ADD COLUMN reason VARCHAR(255) NOT NULL DEFAULT '';

-- Update existing mileage entries to have valid default values
-- Note: You may need to manually update these based on your existing data
UPDATE mileage_entries 
SET 
    license_plate = 'UNKNOWN',
    start_odometer = 0,
    end_odometer = CAST(kilometers AS INTEGER),
    reason = 'Legacy entry'
WHERE license_plate = '' OR license_plate IS NULL;

-- Add comments to document the new fields
COMMENT ON COLUMN time_entries.break_time IS 'Break time in minutes, automatically subtracted from total work time';
COMMENT ON COLUMN mileage_entries.license_plate IS 'License plate number of the vehicle used';
COMMENT ON COLUMN mileage_entries.start_odometer IS 'Starting odometer reading in kilometers';
COMMENT ON COLUMN mileage_entries.end_odometer IS 'Ending odometer reading in kilometers';
COMMENT ON COLUMN mileage_entries.reason IS 'Reason for the travel/mileage';
