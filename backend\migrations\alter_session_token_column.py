"""
Migration script to alter the token column in the sessions table
from VARCHAR(255) to TEXT to accommodate longer tokens.
"""

import sys
import os

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import db
from sqlalchemy import text

def upgrade():
    """
    Upgrade the database schema by altering the token column in the sessions table.
    """
    # Execute raw SQL to alter the column type
    db.session.execute(text("ALTER TABLE sessions ALTER COLUMN token TYPE TEXT"))
    db.session.commit()
    print("Successfully altered sessions.token column from VARCHAR(255) to TEXT")

def downgrade():
    """
    Downgrade the database schema by reverting the token column back to VARCHAR(255).
    Note: This may cause data loss if tokens are longer than 255 characters.
    """
    # Execute raw SQL to revert the column type
    db.session.execute(text("ALTER TABLE sessions ALTER COLUMN token TYPE VARCHAR(255)"))
    db.session.commit()
    print("Reverted sessions.token column from TEXT to VARCHAR(255)")

if __name__ == "__main__":
    # When run directly, perform the upgrade
    from app import create_app
    app = create_app()
    with app.app_context():
        upgrade()
