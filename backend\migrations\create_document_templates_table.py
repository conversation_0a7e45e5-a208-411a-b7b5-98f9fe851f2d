"""
Migration script to create the document_templates table.
"""
from app import create_app, db
from datetime import datetime
from sqlalchemy import text, inspect

def run_migration():
    """
    Create the document_templates table if it doesn't exist.
    """
    app, _ = create_app()

    with app.app_context():
        inspector = inspect(db.engine)
        # Check if the table already exists
        if not inspector.has_table('document_templates'):
            # Create the table
            db.session.execute(text('''
                CREATE TABLE document_templates (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    document_type VARCHAR(50) NOT NULL,
                    description TEXT,
                    file_path VARCHAR(255) NOT NULL,
                    file_type VARCHAR(10) NOT NULL DEFAULT 'pdf',
                    created_by INTEGER NOT NULL REFERENCES users(id),
                    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
                )
            '''))
            db.session.commit()
            print("Created document_templates table")
        else:
            # Check if file_type column exists
            columns = [column['name'] for column in inspector.get_columns('document_templates')]
            if 'file_type' not in columns:
                # Add the file_type column if it doesn't exist
                db.session.execute(text("""
                    ALTER TABLE document_templates
                    ADD COLUMN file_type VARCHAR(10) NOT NULL DEFAULT 'pdf'
                """))
                db.session.commit()
                print("Added file_type column to document_templates table")
            else:
                print("document_templates table already exists with file_type column")

if __name__ == "__main__":
    run_migration()
