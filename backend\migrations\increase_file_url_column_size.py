"""
Migration script to increase the file_url column size in the documents table
to accommodate Firebase Storage signed URLs which can be very long.
"""

import sys
import os

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from sqlalchemy import text, inspect

def run_migration():
    """
    Increase the file_url column size in the documents table.
    """
    app, _ = create_app()

    with app.app_context():
        inspector = inspect(db.engine)
        
        # Check if the documents table exists
        if inspector.has_table('documents'):
            # Get current column info
            columns = inspector.get_columns('documents')
            file_url_column = next((col for col in columns if col['name'] == 'file_url'), None)
            
            if file_url_column:
                print(f"Current file_url column type: {file_url_column['type']}")
                
                # Increase the file_url column size to accommodate Firebase Storage signed URLs
                # Firebase signed URLs can be very long (1000+ characters)
                db.session.execute(text("""
                    ALTER TABLE documents 
                    ALTER COLUMN file_url TYPE VARCHAR(2000)
                """))
                
                # Also increase file_path column size for consistency
                db.session.execute(text("""
                    ALTER TABLE documents 
                    ALTER COLUMN file_path TYPE VARCHAR(2000)
                """))
                
                db.session.commit()
                print("Successfully increased file_url and file_path column sizes to VARCHAR(2000)")
            else:
                print("file_url column not found in documents table")
        else:
            print("documents table does not exist")

if __name__ == "__main__":
    run_migration()
