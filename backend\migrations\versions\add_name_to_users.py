"""add name to users

Revision ID: add_name_to_users
Revises: change_uploaded_by_to_string
Create Date: 2024-05-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

def upgrade():
    # Add name column to users table
    op.add_column('users', sa.Column('name', sa.String(100), nullable=True))

def downgrade():
    # Remove name column from users table
    op.drop_column('users', 'name')
