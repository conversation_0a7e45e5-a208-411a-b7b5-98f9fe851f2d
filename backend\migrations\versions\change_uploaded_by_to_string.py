"""change uploaded_by to string

Revision ID: change_uploaded_by_to_string
Revises: previous_revision_id
Create Date: 2024-01-13 17:20:00.000000

"""
from alembic import op
import sqlalchemy as sa

def upgrade():
    # Convert uploaded_by column from integer to string
    op.alter_column('documents', 'uploaded_by',
                    existing_type=sa.Integer(),
                    type_=sa.String(128),
                    existing_nullable=False)

def downgrade():
    # Convert back to integer if needed
    op.alter_column('documents', 'uploaded_by',
                    existing_type=sa.String(128),
                    type_=sa.Integer(),
                    existing_nullable=False)