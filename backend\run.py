from app import create_app
import os

app, _ = create_app()  # socketio removed

if __name__ == "__main__":
    # Get configuration from environment
    debug_mode = os.getenv("FLASK_DEBUG", "False").lower() in ("true", "1", "t")
    port = int(os.getenv("PORT", 5000))

    # For production deployment (like Render), we don't need SSL context
    # as the platform handles HTTPS termination
    if os.getenv("FLASK_ENV") == "production":
        # Production configuration - let the platform handle SSL
        app.run(host="0.0.0.0", port=port, debug=False, threaded=True)
    else:
        # Development configuration with SSL
        try:
            import ssl
            ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
            ssl_context.load_cert_chain(
                certfile=os.path.abspath("./certs/cert.pem"),
                keyfile=os.path.abspath("./certs/key.pem")
            )
            host = "localhost" if not debug_mode else "0.0.0.0"
            app.run(host=host, port=port, ssl_context=ssl_context, debug=debug_mode, threaded=True)
        except FileNotFoundError:
            # Fallback if SSL certificates are not available
            print("SSL certificates not found, running without HTTPS")
            app.run(host="0.0.0.0", port=port, debug=debug_mode, threaded=True)