#!/usr/bin/env python3
"""
Security and configuration validation script for production deployment.
Run this script to validate your production configuration before deployment.
"""

import os
import json
import sys
import re
from urllib.parse import urlparse

def check_environment_variables():
    """Check if all required environment variables are set."""
    print("🔍 Checking environment variables...")
    
    required_vars = {
        'SECRET_KEY': 'Flask secret key for session security',
        'DATABASE_URL': 'PostgreSQL database connection string',
        'FIREBASE_CREDENTIALS_JSON': 'Firebase service account credentials',
        'FIREBASE_STORAGE_BUCKET': 'Firebase storage bucket name',
        'ADMIN_EMAIL': 'Initial admin user email address'
    }
    
    optional_vars = {
        'FRONTEND_URL': 'Frontend application URL for CORS',
        'FIREBASE_HOSTING_DOMAIN': 'Firebase hosting domain',
        'REDIS_URL': 'Redis cache connection string',
        'RATE_LIMIT_ENABLED': 'Enable/disable rate limiting',
        'SANITIZE_RESPONSES': 'Enable/disable response sanitization'
    }
    
    missing_required = []
    missing_optional = []
    
    for var, description in required_vars.items():
        if not os.getenv(var):
            missing_required.append(f"  ❌ {var}: {description}")
        else:
            print(f"  ✅ {var}: Set")
    
    for var, description in optional_vars.items():
        if not os.getenv(var):
            missing_optional.append(f"  ⚠️  {var}: {description}")
        else:
            print(f"  ✅ {var}: Set")
    
    if missing_required:
        print("\n❌ Missing required environment variables:")
        for var in missing_required:
            print(var)
        return False
    
    if missing_optional:
        print("\n⚠️  Missing optional environment variables:")
        for var in missing_optional:
            print(var)
    
    return True

def validate_secret_key():
    """Validate SECRET_KEY strength."""
    print("\n🔐 Validating SECRET_KEY...")
    
    secret_key = os.getenv('SECRET_KEY')
    if not secret_key:
        print("  ❌ SECRET_KEY not set")
        return False
    
    if len(secret_key) < 32:
        print("  ❌ SECRET_KEY too short (minimum 32 characters)")
        return False
    
    if secret_key.isalnum():
        print("  ⚠️  SECRET_KEY should contain special characters")
    
    print("  ✅ SECRET_KEY appears secure")
    return True

def validate_database_url():
    """Validate DATABASE_URL format."""
    print("\n🗄️  Validating DATABASE_URL...")
    
    db_url = os.getenv('DATABASE_URL')
    if not db_url:
        print("  ❌ DATABASE_URL not set")
        return False
    
    try:
        parsed = urlparse(db_url)
        if parsed.scheme not in ['postgresql', 'postgres']:
            print("  ❌ DATABASE_URL must use postgresql:// scheme")
            return False
        
        if not parsed.hostname:
            print("  ❌ DATABASE_URL missing hostname")
            return False
        
        if not parsed.username:
            print("  ❌ DATABASE_URL missing username")
            return False
        
        print("  ✅ DATABASE_URL format is valid")
        return True
        
    except Exception as e:
        print(f"  ❌ Invalid DATABASE_URL format: {e}")
        return False

def validate_firebase_config():
    """Validate Firebase configuration."""
    print("\n🔥 Validating Firebase configuration...")
    
    creds_json = os.getenv('FIREBASE_CREDENTIALS_JSON')
    if not creds_json:
        print("  ❌ FIREBASE_CREDENTIALS_JSON not set")
        return False
    
    try:
        creds = json.loads(creds_json)
        required_fields = ['type', 'project_id', 'private_key', 'client_email']
        
        for field in required_fields:
            if field not in creds:
                print(f"  ❌ Missing field in Firebase credentials: {field}")
                return False
        
        if creds['type'] != 'service_account':
            print("  ❌ Firebase credentials must be for a service account")
            return False
        
        print("  ✅ Firebase credentials format is valid")
        
    except json.JSONDecodeError:
        print("  ❌ FIREBASE_CREDENTIALS_JSON is not valid JSON")
        return False
    
    # Validate storage bucket
    bucket = os.getenv('FIREBASE_STORAGE_BUCKET')
    if not bucket:
        print("  ❌ FIREBASE_STORAGE_BUCKET not set")
        return False
    
    if not bucket.endswith('.firebasestorage.app') and not bucket.endswith('.appspot.com'):
        print("  ⚠️  FIREBASE_STORAGE_BUCKET format may be incorrect")
    
    print("  ✅ Firebase storage bucket configured")
    return True

def validate_cors_config():
    """Validate CORS configuration."""
    print("\n🌐 Validating CORS configuration...")
    
    frontend_url = os.getenv('FRONTEND_URL')
    firebase_domain = os.getenv('FIREBASE_HOSTING_DOMAIN')
    
    if not frontend_url and not firebase_domain:
        print("  ⚠️  No production frontend URLs configured")
        print("     Set FRONTEND_URL and/or FIREBASE_HOSTING_DOMAIN")
        return False
    
    urls_to_check = []
    if frontend_url:
        urls_to_check.append(('FRONTEND_URL', frontend_url))
    if firebase_domain:
        urls_to_check.append(('FIREBASE_HOSTING_DOMAIN', firebase_domain))
    
    for name, url in urls_to_check:
        try:
            parsed = urlparse(url)
            if parsed.scheme != 'https':
                print(f"  ❌ {name} must use HTTPS in production")
                return False
            
            if not parsed.hostname:
                print(f"  ❌ {name} missing hostname")
                return False
            
            print(f"  ✅ {name}: {url}")
            
        except Exception as e:
            print(f"  ❌ Invalid {name} format: {e}")
            return False
    
    return True

def check_security_settings():
    """Check security-related settings."""
    print("\n🛡️  Checking security settings...")
    
    flask_debug = os.getenv('FLASK_DEBUG', 'False').lower()
    if flask_debug in ('true', '1', 't'):
        print("  ❌ FLASK_DEBUG is enabled - DISABLE for production!")
        return False
    
    flask_env = os.getenv('FLASK_ENV', 'development')
    if flask_env != 'production':
        print("  ⚠️  FLASK_ENV should be 'production'")
    
    rate_limit = os.getenv('RATE_LIMIT_ENABLED', 'True').lower()
    if rate_limit not in ('true', '1', 't'):
        print("  ⚠️  Rate limiting is disabled")
    
    sanitize = os.getenv('SANITIZE_RESPONSES', 'True').lower()
    if sanitize not in ('true', '1', 't'):
        print("  ⚠️  Response sanitization is disabled")
    
    print("  ✅ Security settings validated")
    return True

def validate_admin_email():
    """Validate admin email format."""
    print("\n👤 Validating admin email...")
    
    admin_email = os.getenv('ADMIN_EMAIL')
    if not admin_email:
        print("  ❌ ADMIN_EMAIL not set")
        return False
    
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, admin_email):
        print("  ❌ ADMIN_EMAIL format is invalid")
        return False
    
    print(f"  ✅ Admin email: {admin_email}")
    return True

def main():
    """Run all security checks."""
    print("🚀 Production Security Validation")
    print("=" * 50)
    
    checks = [
        check_environment_variables,
        validate_secret_key,
        validate_database_url,
        validate_firebase_config,
        validate_cors_config,
        check_security_settings,
        validate_admin_email
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        if check():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Security Check Results: {passed}/{total} passed")
    
    if passed == total:
        print("✅ All security checks passed! Ready for production deployment.")
        return 0
    else:
        print("❌ Some security checks failed. Please fix the issues before deploying.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
