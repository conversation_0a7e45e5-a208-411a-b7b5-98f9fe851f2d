#!/bin/bash
set -e

echo "=== Customer Management API Deployment ==="

# Set environment variables
export FLASK_ENV=production
export FLASK_DEBUG=False

echo "Current directory: $(pwd)"
echo "Python version: $(python --version)"

# Try to start with the main app
echo "Attempting to start main application..."
if python -c "import app; print('Main app import successful')" 2>/dev/null; then
    echo "✓ Main app imports successfully"
    
    # Initialize database if possible
    if [ -n "$DATABASE_URL" ] && [ -n "$SECRET_KEY" ]; then
        echo "Initializing database..."
        python init_production_db.py || echo "Database init failed, continuing..."
    fi
    
    echo "Starting main application with Gunicorn..."
    exec gunicorn --bind 0.0.0.0:$PORT --workers 2 --timeout 120 --access-logfile - --error-logfile - app:app
else
    echo "✗ Main app import failed, falling back to simple app"
    echo "Starting simple application with Gun<PERSON>..."
    exec gunicorn --bind 0.0.0.0:$PORT --workers 2 --timeout 120 --access-logfile - --error-logfile - simple_app:app
fi
