#!/usr/bin/env python3
"""
Test script for the custom rate limiter.

This script tests the new custom rate limiting system to ensure it works correctly
and doesn't have the issues that Flask-Limiter had with global vs endpoint-specific limits.
"""

import sys
import os
import time
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

def test_rate_limiter():
    """Test the rate limiter with actual HTTP requests."""
    
    # Base URL for the API
    base_url = "https://localhost:5000"
    
    # Test endpoint that should have rate limiting
    test_endpoint = f"{base_url}/api/auth/csrf-token"
    
    print("Testing Custom Rate Limiter")
    print("=" * 50)
    
    # Test 1: Normal requests within limit
    print("\nTest 1: Normal requests within limit")
    success_count = 0
    for i in range(5):
        try:
            response = requests.get(test_endpoint, verify=False, timeout=5)
            if response.status_code == 200:
                success_count += 1
                print(f"Request {i+1}: SUCCESS (200)")
            else:
                print(f"Request {i+1}: FAILED ({response.status_code})")
        except Exception as e:
            print(f"Request {i+1}: ERROR - {str(e)}")
        time.sleep(0.5)  # Small delay between requests
    
    print(f"Successful requests: {success_count}/5")
    
    # Test 2: Rapid requests to trigger rate limit
    print("\nTest 2: Rapid requests to trigger rate limit")
    print("Sending 70 requests rapidly (limit should be 60/minute)...")
    
    def make_request(request_num):
        try:
            response = requests.get(test_endpoint, verify=False, timeout=5)
            return {
                'request_num': request_num,
                'status_code': response.status_code,
                'headers': dict(response.headers),
                'success': response.status_code == 200
            }
        except Exception as e:
            return {
                'request_num': request_num,
                'status_code': None,
                'error': str(e),
                'success': False
            }
    
    # Use ThreadPoolExecutor to make concurrent requests
    results = []
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(make_request, i) for i in range(1, 71)]
        for future in as_completed(futures):
            results.append(future.result())
    
    # Sort results by request number
    results.sort(key=lambda x: x['request_num'])
    
    # Analyze results
    successful_requests = [r for r in results if r['success']]
    rate_limited_requests = [r for r in results if r['status_code'] == 429]
    error_requests = [r for r in results if not r['success'] and r['status_code'] != 429]
    
    print(f"Total requests: {len(results)}")
    print(f"Successful (200): {len(successful_requests)}")
    print(f"Rate limited (429): {len(rate_limited_requests)}")
    print(f"Errors: {len(error_requests)}")
    
    # Show first few rate limited responses
    if rate_limited_requests:
        print("\nFirst rate limited response details:")
        first_limited = rate_limited_requests[0]
        print(f"Request #{first_limited['request_num']}")
        if 'headers' in first_limited and 'Retry-After' in first_limited['headers']:
            print(f"Retry-After header: {first_limited['headers']['Retry-After']}")
    
    # Test 3: Verify different endpoints have separate limits
    print("\nTest 3: Testing endpoint separation")
    print("This would require authentication, skipping for now...")
    
    # Summary
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    if len(successful_requests) > 0 and len(rate_limited_requests) > 0:
        print("✅ Rate limiter is working correctly!")
        print(f"   - Allowed {len(successful_requests)} requests")
        print(f"   - Rate limited {len(rate_limited_requests)} requests")
        print("   - No conflicts between global and endpoint limits")
    elif len(successful_requests) == len(results):
        print("⚠️  Rate limiter might not be working - all requests succeeded")
        print("   - This could mean rate limiting is disabled")
        print("   - Or the limit is higher than the number of test requests")
    else:
        print("❌ Unexpected results")
        print(f"   - Successful: {len(successful_requests)}")
        print(f"   - Rate limited: {len(rate_limited_requests)}")
        print(f"   - Errors: {len(error_requests)}")

def test_rate_limiter_unit():
    """Unit test the rate limiter logic without HTTP requests."""
    
    print("\nUnit Testing Rate Limiter Logic")
    print("=" * 50)
    
    try:
        from app.utils.custom_rate_limiter import SlidingWindowRateLimiter
        
        # Create a test rate limiter
        limiter = SlidingWindowRateLimiter()
        
        # Test parsing rate limit strings
        print("\nTesting rate limit string parsing:")
        test_limits = ["60/minute", "100/hour", "5/second", "1000/day"]
        
        for limit_str in test_limits:
            try:
                count, seconds = limiter._parse_limit(limit_str)
                print(f"  {limit_str} -> {count} requests per {seconds} seconds ✅")
            except Exception as e:
                print(f"  {limit_str} -> ERROR: {e} ❌")
        
        # Test invalid formats
        print("\nTesting invalid rate limit formats:")
        invalid_limits = ["invalid", "60/minutes", "abc/hour", "60"]
        
        for limit_str in invalid_limits:
            try:
                count, seconds = limiter._parse_limit(limit_str)
                print(f"  {limit_str} -> Should have failed but got {count}/{seconds} ❌")
            except Exception as e:
                print(f"  {limit_str} -> Correctly rejected: {e} ✅")
        
        print("\n✅ Unit tests completed successfully!")
        
    except ImportError as e:
        print(f"❌ Could not import rate limiter: {e}")
        print("Make sure you're running this from the backend directory")

if __name__ == "__main__":
    print("Custom Rate Limiter Test Suite")
    print("=" * 50)
    
    # Run unit tests first
    test_rate_limiter_unit()
    
    # Ask user if they want to run HTTP tests
    print("\n" + "=" * 50)
    response = input("Do you want to run HTTP tests? (requires running server) [y/N]: ")
    
    if response.lower() in ['y', 'yes']:
        test_rate_limiter()
    else:
        print("Skipping HTTP tests. Start your Flask server and run again to test HTTP endpoints.")
    
    print("\nTest suite completed!")
