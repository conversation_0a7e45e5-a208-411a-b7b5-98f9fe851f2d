"""
WSGI entry point for production deployment.
This file is used by WSGI servers like Gunicorn.
"""

import os
import sys

# Set default environment variables for production
os.environ.setdefault('FLASK_ENV', 'production')
os.environ.setdefault('FLASK_DEBUG', 'False')

# Initialize app variable
application = None

try:
    from app import create_app
    # Create the Flask application
    application, _ = create_app()
    print("✓ Flask app created successfully for WSGI")
except Exception as e:
    print(f"✗ Failed to create Flask app: {e}")
    import traceback
    traceback.print_exc()
    
    # Try to create a minimal app for debugging
    try:
        from flask import Flask
        application = Flask(__name__)
        
        @application.route('/api/health')
        def health_check():
            return {'status': 'minimal', 'message': 'Minimal app running - check logs for errors'}, 200
            
        @application.route('/')
        def root():
            return {'error': 'Application failed to initialize properly', 'message': str(e)}, 500
            
        print("✓ Created minimal Flask app for debugging")
    except Exception as fallback_error:
        print(f"✗ Failed to create even minimal app: {fallback_error}")
        # Create an absolute minimal app that will at least start
        from flask import Flask
        application = Flask(__name__)
        
        @application.route('/')
        def error_page():
            return f'Application failed to start: {fallback_error}', 500

# Health check endpoint is already defined in the main app
# No need to add it here to avoid conflicts

# For compatibility with different WSGI server naming conventions
app = application

if __name__ == "__main__":
    # This will only run if the file is executed directly
    # Most deployment platforms will use a WSGI server instead
    port = int(os.getenv("PORT", 5000))
    debug = os.getenv("FLASK_DEBUG", "False").lower() in ("true", "1", "t")
    
    application.run(host="0.0.0.0", port=port, debug=debug)
