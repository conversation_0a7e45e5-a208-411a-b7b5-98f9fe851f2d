#!/usr/bin/env python3
"""
Automated deployment script for Customer Management System.
This script helps automate the deployment process to Render and Firebase.
"""

import os
import sys
import subprocess
import json
import argparse
from pathlib import Path

def run_command(command, cwd=None, check=True):
    """Run a shell command and return the result."""
    print(f"Running: {command}")
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=check
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        if check:
            sys.exit(1)
        return e

def check_prerequisites():
    """Check if all required tools are installed."""
    print("🔍 Checking prerequisites...")
    
    tools = {
        'git': 'git --version',
        'node': 'node --version',
        'npm': 'npm --version',
        'python': 'python --version',
        'pip': 'pip --version'
    }
    
    missing_tools = []
    
    for tool, command in tools.items():
        result = run_command(command, check=False)
        if result.returncode != 0:
            missing_tools.append(tool)
        else:
            print(f"  ✅ {tool}: Available")
    
    if missing_tools:
        print(f"❌ Missing required tools: {', '.join(missing_tools)}")
        return False
    
    return True

def validate_environment():
    """Validate the current environment and configuration."""
    print("\n🔧 Validating environment...")
    
    # Check if we're in the right directory
    if not os.path.exists('backend/app.py') or not os.path.exists('frontend/package.json'):
        print("❌ Please run this script from the project root directory")
        return False
    
    # Run security check
    print("Running security validation...")
    result = run_command('python backend/security_check.py', check=False)
    if result.returncode != 0:
        print("❌ Security validation failed")
        return False
    
    return True

def build_frontend():
    """Build the frontend application."""
    print("\n🏗️  Building frontend...")
    
    # Install dependencies
    print("Installing frontend dependencies...")
    run_command('npm ci', cwd='frontend')
    
    # Run linting
    print("Running frontend linting...")
    run_command('npm run lint', cwd='frontend', check=False)
    
    # Build the application
    print("Building frontend application...")
    run_command('npm run build', cwd='frontend')
    
    # Check if build was successful
    if not os.path.exists('frontend/dist/index.html'):
        print("❌ Frontend build failed - dist/index.html not found")
        return False
    
    print("✅ Frontend build completed successfully")
    return True

def deploy_to_firebase():
    """Deploy frontend to Firebase Hosting."""
    print("\n🔥 Deploying to Firebase Hosting...")
    
    # Check if Firebase CLI is installed
    result = run_command('firebase --version', check=False)
    if result.returncode != 0:
        print("❌ Firebase CLI not installed. Install with: npm install -g firebase-tools")
        return False
    
    # Check if user is logged in
    result = run_command('firebase projects:list', cwd='frontend', check=False)
    if result.returncode != 0:
        print("❌ Not logged in to Firebase. Run: firebase login")
        return False
    
    # Deploy to Firebase
    print("Deploying to Firebase Hosting...")
    run_command('firebase deploy --only hosting', cwd='frontend')
    
    print("✅ Frontend deployed to Firebase Hosting successfully")
    return True

def prepare_backend():
    """Prepare backend for deployment."""
    print("\n⚙️  Preparing backend for deployment...")
    
    # Check if requirements.txt exists
    if not os.path.exists('backend/requirements.txt'):
        print("❌ backend/requirements.txt not found")
        return False
    
    # Validate Python dependencies
    print("Validating Python dependencies...")
    run_command('pip check', cwd='backend', check=False)
    
    # Check if all necessary files exist
    required_files = [
        'backend/app.py',
        'backend/Procfile',
        'backend/runtime.txt',
        'backend/requirements.txt'
    ]
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"❌ Required file missing: {file_path}")
            return False
        print(f"  ✅ {file_path}: Found")
    
    print("✅ Backend preparation completed")
    return True

def create_render_config():
    """Create or update Render configuration."""
    print("\n📝 Creating Render configuration...")
    
    if os.path.exists('render.yaml'):
        print("  ✅ render.yaml already exists")
    else:
        print("❌ render.yaml not found. Please create it using the provided template.")
        return False
    
    return True

def run_tests():
    """Run tests if available."""
    print("\n🧪 Running tests...")
    
    # Backend tests
    if os.path.exists('backend/tests'):
        print("Running backend tests...")
        run_command('python -m pytest backend/tests/', check=False)
    
    # Frontend tests
    if os.path.exists('frontend/src/__tests__'):
        print("Running frontend tests...")
        run_command('npm test -- --watchAll=false', cwd='frontend', check=False)
    
    return True

def main():
    """Main deployment function."""
    parser = argparse.ArgumentParser(description='Deploy Customer Management System')
    parser.add_argument('--frontend-only', action='store_true', help='Deploy only frontend')
    parser.add_argument('--backend-only', action='store_true', help='Prepare only backend')
    parser.add_argument('--skip-tests', action='store_true', help='Skip running tests')
    parser.add_argument('--skip-build', action='store_true', help='Skip building frontend')
    
    args = parser.parse_args()
    
    print("🚀 Customer Management System Deployment")
    print("=" * 50)
    
    # Check prerequisites
    if not check_prerequisites():
        sys.exit(1)
    
    # Validate environment
    if not validate_environment():
        sys.exit(1)
    
    # Run tests
    if not args.skip_tests:
        run_tests()
    
    success = True
    
    # Frontend deployment
    if not args.backend_only:
        if not args.skip_build:
            if not build_frontend():
                success = False
        
        if success:
            deploy_choice = input("\n🔥 Deploy to Firebase Hosting? (y/N): ").lower()
            if deploy_choice in ['y', 'yes']:
                if not deploy_to_firebase():
                    success = False
    
    # Backend preparation
    if not args.frontend_only:
        if not prepare_backend():
            success = False
        
        if success and not create_render_config():
            success = False
    
    print("\n" + "=" * 50)
    
    if success:
        print("✅ Deployment preparation completed successfully!")
        
        if not args.frontend_only:
            print("\n📋 Next steps for backend deployment:")
            print("1. Push your code to GitHub")
            print("2. Connect your GitHub repository to Render")
            print("3. Create services using the render.yaml configuration")
            print("4. Set environment variables in Render dashboard")
            print("5. Deploy and run: python init_production_db.py")
        
        if not args.backend_only:
            print("\n🔥 Frontend deployed to Firebase Hosting")
            print("Update your backend FRONTEND_URL environment variable with the Firebase URL")
    
    else:
        print("❌ Deployment preparation failed. Please fix the issues and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
