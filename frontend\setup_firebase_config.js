/**
 * <PERSON><PERSON><PERSON> to help set up Firebase configuration for the frontend
 * 
 * Usage:
 * 1. Replace the firebaseConfig object with your own Firebase configuration
 * 2. Run the script with Node.js: node setup_firebase_config.js
 * 3. Copy the generated environment variables to your .env file
 */

// Replace this with your Firebase configuration
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "your-messaging-sender-id",
  appId: "your-app-id",
  measurementId: "your-measurement-id"
};

// Generate environment variables
function generateEnvVars(config) {
  const envVars = [];
  
  envVars.push(`VITE_FIREBASE_API_KEY=${config.apiKey}`);
  envVars.push(`VITE_FIREBASE_AUTH_DOMAIN=${config.authDomain}`);
  envVars.push(`VITE_FIREBASE_PROJECT_ID=${config.projectId}`);
  envVars.push(`VITE_FIREBASE_STORAGE_BUCKET=${config.storageBucket}`);
  envVars.push(`VITE_FIREBASE_MESSAGING_SENDER_ID=${config.messagingSenderId}`);
  envVars.push(`VITE_FIREBASE_APP_ID=${config.appId}`);
  
  if (config.measurementId) {
    envVars.push(`VITE_FIREBASE_MEASUREMENT_ID=${config.measurementId}`);
  }
  
  return envVars.join('\n');
}

// Print the environment variables
console.log('\nFirebase Configuration Environment Variables:');
console.log('==========================================');
console.log(generateEnvVars(firebaseConfig));
console.log('==========================================');
console.log('\nCopy these variables to your .env file in the frontend directory.');
console.log('Make sure to replace the placeholder values with your actual Firebase configuration.');
