import axios from "axios";
import { auth } from "./firebase";

// Function to get CSRF token from cookies
const getCSRFToken = (): string | null => {
  // Try different possible CSRF cookie names
  const possibleNames = ["_csrf_token", "csrf_token", "X-CSRFToken"];
  const decodedCookie = decodeURIComponent(document.cookie);
  const cookieArray = decodedCookie.split(";");

  for (const cookieName of possibleNames) {
    const name = `${cookieName}=`;
    for (let i = 0; i < cookieArray.length; i++) {
      let cookie = cookieArray[i].trim();
      if (cookie.indexOf(name) === 0) {
        const token = cookie.substring(name.length, cookie.length);
        console.log(`Found CSRF token in cookie: ${cookieName}`);
        return token;
      }
    }
  }

  console.warn("No CSRF token found in cookies");
  console.log("Available cookies:", document.cookie);
  return null;
};

// Function to get auth token from cookies
const getAuthTokenFromCookie = (): string | null => {
  const name = "auth_token=";
  const decodedCookie = decodeURIComponent(document.cookie);
  const cookieArray = decodedCookie.split(";");

  for (let i = 0; i < cookieArray.length; i++) {
    let cookie = cookieArray[i].trim();
    if (cookie.indexOf(name) === 0) {
      return cookie.substring(name.length, cookie.length);
    }
  }
  return null;
};

const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  withCredentials: true, // Enable credentials to support CSRF tokens
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: 15000, // Default timeout of 15 seconds
});

api.interceptors.request.use(async (config) => {
  try {
    let token = null;

    // First try to get token from cookie (for production)
    const cookieToken = getAuthTokenFromCookie();
    if (cookieToken) {
      token = cookieToken;
    }

    // If no cookie token, try Firebase user (for development or initial auth)
    if (!token) {
      const user = auth.currentUser;
      if (user) {
        token = await user.getIdToken();
      }
    }

    // Add authentication token for protected routes
    if (token && (
        config.url?.includes('/auth/verify') ||
        config.url?.includes('/documents/') ||
        config.url?.includes('/customers/') ||
        config.url?.includes('/events/') ||
        config.url?.includes('/users/') ||
        config.url?.includes('/quotations/') ||
        config.url?.includes('/time-entries/') ||
        config.url?.includes('/mileage-entries/'))) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add CSRF token for non-GET requests
    if (config.method !== 'get') {
      const csrfToken = getCSRFToken();
      if (csrfToken) {
        // Flask-SeaSurf uses 'X-CSRFToken' as the default header name
        config.headers['X-CSRFToken'] = csrfToken;
        console.log(`Added CSRF token to ${config.method?.toUpperCase()} request to ${config.url}`);
      } else {
        console.warn(`No CSRF token available for ${config.method?.toUpperCase()} request to ${config.url}`);
        // Try to fetch a new CSRF token if we don't have one
        if (!config.url?.includes('/auth/csrf-token')) {
          console.log('Attempting to fetch new CSRF token...');
        }
      }
    }

    return config;
  } catch (error) {
    console.error('Error in request interceptor:', error);
    return config;
  }
});

api.interceptors.response.use(
  (response) => {
    // Check if any sensitive fields have been sanitized and handle them appropriately
    if (response.data && typeof response.data === 'object') {
      // If we detect sanitized fields (marked with ***), log a message
      const hasSanitizedFields = JSON.stringify(response.data).includes('***');
      if (hasSanitizedFields && process.env.NODE_ENV === 'development') {
        console.warn('Response contains sanitized fields for security reasons.');
      }
    }
    return response;
  },
  async (error) => {
    // Handle network errors and timeouts
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      console.error('Request timeout - server may be slow');
    } else if (error.code === 'ERR_NETWORK') {
      console.error('Network error - check connection');
    }

    // For 401 unauthorized errors, try to refresh token first
    if (error.response?.status === 401) {
      const user = auth.currentUser;
      if (user && !error.config._retry) {
        try {
          // Mark this request as a retry to prevent infinite loops
          error.config._retry = true;

          // Try to get a fresh token
          const newToken = await user.getIdToken(true);
          error.config.headers.Authorization = `Bearer ${newToken}`;

          // Retry the original request
          return api.request(error.config);
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError);
          // If refresh fails, sign out and redirect
          auth.signOut();
          window.location.href = '/login';
        }
      } else {
        // No user or already retried, sign out
        auth.signOut();
        window.location.href = '/login';
      }
    }
    // For 403 forbidden errors, redirect to dashboard instead of logging out
    else if (error.response?.status === 403) {
      // Check if we're already on the dashboard to prevent redirect loops
      if (!window.location.pathname.includes('/user-dashboard')) {
        window.location.href = '/user-dashboard';
      }
    }
    return Promise.reject(error);
  }
);

export default api;