import React, { useState, useEffect } from 'react';
import { getAuditLogs, AuditLog } from '../services/auditService';
import Pagination from './Pagination';
import { FaUser, FaBuilding, FaCalendarAlt, FaFileAlt, FaSearch, FaFilter, FaTimes } from 'react-icons/fa';

interface AuditLogViewerProps {
  entityType?: string;
  entityId?: number;
  userId?: number;
  title?: string;
}

const AuditLogViewer: React.FC<AuditLogViewerProps> = ({
  entityType,
  entityId,
  userId,
  title = 'Audit Logs'
}) => {
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Filters
  const [filterEntityType, setFilterEntityType] = useState<string | undefined>(entityType);
  const [filterAction, setFilterAction] = useState<string | undefined>();
  const [filterUserId, setFilterUserId] = useState<number | undefined>(userId);
  const [filterStartDate, setFilterStartDate] = useState<string | undefined>();
  const [filterEndDate, setFilterEndDate] = useState<string | undefined>();
  const [showFilters, setShowFilters] = useState(false);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      const response = await getAuditLogs(
        currentPage,
        itemsPerPage,
        filterEntityType,
        filterAction,
        filterUserId,
        filterStartDate,
        filterEndDate
      );

      setLogs(response.logs);
      setTotalItems(response.total);
      setError(null);
    } catch (err) {
      setError('Failed to load audit logs');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLogs();
  }, [currentPage, itemsPerPage, filterEntityType, filterAction, filterUserId, filterStartDate, filterEndDate]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (perPage: number) => {
    setItemsPerPage(perPage);
    setCurrentPage(1);
  };

  const resetFilters = () => {
    setFilterEntityType(entityType);
    setFilterAction(undefined);
    setFilterUserId(userId);
    setFilterStartDate(undefined);
    setFilterEndDate(undefined);
  };

  const getEntityIcon = (type: string) => {
    switch (type) {
      case 'user':
        return <FaUser className="text-blue-500" />;
      case 'customer':
        return <FaBuilding className="text-green-500" />;
      case 'event':
        return <FaCalendarAlt className="text-purple-500" />;
      case 'document':
        return <FaFileAlt className="text-orange-500" />;
      default:
        return <FaFileAlt className="text-gray-500" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'create':
        return 'bg-green-100 text-green-800';
      case 'update':
      case 'update_role':
      case 'update_name':
        return 'bg-blue-100 text-blue-800';
      case 'delete':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatAction = (action: string) => {
    return action.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <div className="bg-white dark:bg-dark-secondary rounded-lg shadow-sm p-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text">{title}</h2>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="btn btn-outline text-xs py-1 px-2 flex items-center"
          >
            {showFilters ? <FaTimes className="mr-1" /> : <FaFilter className="mr-1" />}
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </button>
          <button
            onClick={fetchLogs}
            className="btn btn-outline text-xs py-1 px-2"
          >
            Refresh
          </button>
        </div>
      </div>

      {showFilters && (
        <div className="bg-gray-50 dark:bg-dark-tertiary p-4 rounded-lg mb-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-light mb-1">Entity Type</label>
              <select
                value={filterEntityType || ''}
                onChange={(e) => setFilterEntityType(e.target.value || undefined)}
                className="w-full border border-gray-300 dark:border-dark-border rounded p-2 text-sm dark:bg-dark-input dark:text-dark-text"
              >
                <option value="">All Types</option>
                <option value="user">User</option>
                <option value="customer">Customer</option>
                <option value="document">Document</option>
                <option value="event">Event</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-light mb-1">Action</label>
              <select
                value={filterAction || ''}
                onChange={(e) => setFilterAction(e.target.value || undefined)}
                className="w-full border border-gray-300 dark:border-dark-border rounded p-2 text-sm dark:bg-dark-input dark:text-dark-text"
              >
                <option value="">All Actions</option>
                <option value="create">Create</option>
                <option value="update">Update</option>
                <option value="delete">Delete</option>
                <option value="update_role">Update Role</option>
                <option value="update_name">Update Name</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-dark-text-light mb-1">Date Range</label>
              <div className="flex space-x-2">
                <input
                  type="date"
                  value={filterStartDate || ''}
                  onChange={(e) => setFilterStartDate(e.target.value || undefined)}
                  className="w-full border border-gray-300 dark:border-dark-border rounded p-2 text-sm dark:bg-dark-input dark:text-dark-text"
                  placeholder="Start Date"
                />
                <input
                  type="date"
                  value={filterEndDate || ''}
                  onChange={(e) => setFilterEndDate(e.target.value || undefined)}
                  className="w-full border border-gray-300 dark:border-dark-border rounded p-2 text-sm dark:bg-dark-input dark:text-dark-text"
                  placeholder="End Date"
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end mt-4">
            <button
              onClick={resetFilters}
              className="btn btn-outline text-xs py-1 px-2 mr-2"
            >
              Reset Filters
            </button>
            <button
              onClick={fetchLogs}
              className="btn btn-primary text-xs py-1 px-2"
            >
              Apply Filters
            </button>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {loading ? (
        <div className="text-center py-4">
          <div className="spinner"></div>
          <p className="mt-2 text-gray-500">Loading audit logs...</p>
        </div>
      ) : (
        <>
          {logs.length === 0 ? (
            <p className="text-gray-500 text-center py-4">No audit logs found.</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Action
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Entity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Details
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {logs.map((log) => (
                    <tr key={log.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 text-xs rounded-full ${getActionColor(log.action)}`}>
                          {formatAction(log.action)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getEntityIcon(log.entity_type)}
                          <div className="ml-2">
                            <div className="text-sm font-medium text-gray-900 capitalize">
                              {log.entity_type}
                            </div>
                            <div className="text-xs text-gray-500">
                              ID: {log.entity_id}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {log.user ? (log.user.name || log.user.email) : 'System'}
                        </div>
                        {log.user && (
                          <div className="text-xs text-gray-500 capitalize">
                            {log.user.role}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(log.created_at)}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                        {log.details ? (
                          <div className="text-xs">
                            <pre className="whitespace-pre-wrap font-sans">
                              {JSON.stringify(log.details, null, 2)}
                            </pre>
                          </div>
                        ) : (
                          <span className="text-gray-400">No details</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          <div className="mt-4">
            <Pagination
              currentPage={currentPage}
              totalPages={Math.ceil(totalItems / itemsPerPage)}
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              onPageChange={handlePageChange}
              onItemsPerPageChange={handleItemsPerPageChange}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default AuditLogViewer;
