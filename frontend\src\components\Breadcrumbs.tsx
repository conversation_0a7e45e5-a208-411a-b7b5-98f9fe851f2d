import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FaHome, FaChevronRight } from 'react-icons/fa';

interface BreadcrumbsProps {
  customTitle?: string;
  customerId?: string | number;
  customerName?: string;
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ 
  customTitle, 
  customerId,
  customerName
}) => {
  const location = useLocation();
  const pathnames = location.pathname.split('/').filter(x => x);

  // Create breadcrumb items based on the current path
  const getBreadcrumbItems = () => {
    const items = [];
    let currentPath = '';

    // Add home
    items.push({
      name: 'Home',
      path: '/',
      icon: <FaHome className="mr-1" />
    });

    // Special case for customers
    if (pathnames[0] === 'customers' && customerId) {
      items.push({
        name: 'Customers',
        path: '/customers',
        icon: null
      });

      if (customerName) {
        items.push({
          name: customerName,
          path: `/customers/${customerId}`,
          icon: null
        });
      } else {
        items.push({
          name: `Customer ${customerId}`,
          path: `/customers/${customerId}`,
          icon: null
        });
      }

      // Add documents if applicable
      if (pathnames[2] === 'documents') {
        items.push({
          name: 'Documents',
          path: `/customers/${customerId}/documents`,
          icon: null
        });
      }
    } 
    // Handle document templates
    else if (pathnames[0] === 'document-templates') {
      items.push({
        name: 'Document Templates',
        path: '/document-templates',
        icon: null
      });
    }
    // Handle document editor
    else if (pathnames[0] === 'edit-document' && pathnames.length >= 3) {
      items.push({
        name: 'Customers',
        path: '/customers',
        icon: null
      });

      items.push({
        name: `Customer ${pathnames[2]}`,
        path: `/customers/${pathnames[2]}`,
        icon: null
      });

      items.push({
        name: 'Documents',
        path: `/customers/${pathnames[2]}/documents`,
        icon: null
      });

      items.push({
        name: 'Edit Document',
        path: location.pathname,
        icon: null
      });
    }
    // Default case - build from path segments
    else {
      pathnames.forEach((name, index) => {
        currentPath += `/${name}`;
        
        // Format the name for display
        const displayName = name
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
        
        items.push({
          name: displayName,
          path: currentPath,
          icon: null
        });
      });
    }

    // Override the last item's name if a custom title is provided
    if (customTitle && items.length > 0) {
      items[items.length - 1].name = customTitle;
    }

    return items;
  };

  const breadcrumbItems = getBreadcrumbItems();

  return (
    <nav className="flex mb-4 text-sm" aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-3 flex-wrap">
        {breadcrumbItems.map((item, index) => (
          <li key={index} className="inline-flex items-center">
            {index > 0 && (
              <FaChevronRight className="mx-2 text-gray-400" size={12} />
            )}
            
            {index === breadcrumbItems.length - 1 ? (
              <span className="text-amspm-text font-medium" aria-current="page">
                {item.icon}{item.name}
              </span>
            ) : (
              <Link 
                to={item.path} 
                className="text-amspm-primary hover:text-amspm-primary-dark flex items-center"
              >
                {item.icon}{item.name}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumbs;
