import React from 'react';
import { useConfirmation } from '../context/ConfirmationContext';
import { FaExclamationTriangle } from 'react-icons/fa';

const ConfirmationDialog: React.FC = () => {
  const { confirmationState, hideConfirmation } = useConfirmation();
  const { isOpen, title, message, confirmText, cancelText, confirmButtonClass, showCancel = true, onConfirm, onCancel } = confirmationState;

  if (!isOpen) return null;

  const handleConfirm = () => {
    onConfirm();
    hideConfirmation();
  };

  const handleCancel = () => {
    if (onCancel) onCancel();
    hideConfirmation();
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 p-4 flex items-center justify-center z-[100]"
      style={{ pointerEvents: 'auto' }}
      onClick={handleCancel}
    >
      <div
        className="modal-content max-w-md w-full bg-white dark:bg-dark-secondary rounded-lg shadow-lg overflow-hidden"
        style={{ pointerEvents: 'auto', zIndex: 101 }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-6">
          <div className="flex items-center mb-4">
            <div className="flex-shrink-0 mr-4">
              <FaExclamationTriangle className="h-6 w-6 text-yellow-500" />
            </div>
            <h3 className="text-lg font-medium text-amspm-text dark:text-dark-text">{title}</h3>
          </div>
          <div className="mt-2">
            <p className="text-sm text-gray-600 dark:text-gray-300">{message}</p>
          </div>
          <div className="mt-6 flex justify-end space-x-3">
            {showCancel && (
              <button
                type="button"
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amspm-primary"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleCancel();
                }}
                style={{ pointerEvents: 'auto', cursor: 'pointer' }}
              >
                {cancelText}
              </button>
            )}
            <button
              type="button"
              className={`px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amspm-primary ${confirmButtonClass || 'bg-blue-600 hover:bg-blue-700'}`}
              onClick={handleConfirm}
            >
              {confirmText}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationDialog;
