// frontend/src/components/CreateUpcomingEventModal.tsx
import React, { useState, useEffect } from "react";
import { createEvent } from "../services/eventService";
import { getAllUsers, getUserPermissions } from "../services/userService";
import { Document } from "../types/document";
import { Quotation } from "../types/quotation";
import { User, UserPermissions } from "../types/user";
import { useConfirmation } from '../context/ConfirmationContext';
import LoadingSpinner from './LoadingSpinner';
import { FaExclamationTriangle } from "react-icons/fa";

interface CreateUpcomingEventModalProps {
  document: Document;
  onClose: () => void;
  onEventCreated: () => void;
}

const CreateUpcomingEventModal: React.FC<CreateUpcomingEventModalProps> = ({ document, onClose, onEventCreated }) => {
  const { showConfirmation } = useConfirmation();
  const [users, setUsers] = useState<User[]>([]);

  // Bepaal het document type
  const isQuotation = document?.document_type === 'offerte';

  // Bepaal de standaard beschrijving op basis van document
  let defaultDescription = '';
  if (document) {
    if (isQuotation) {
      defaultDescription = `Installatie voor offerte (document ID: ${document.id})`;
    } else {
      defaultDescription = `Renew document: ${document.document_type} for customer ID: ${document.customer_id}`;
    }
  }

  const [eventData, setEventData] = useState({
    user_id: 0,
    scheduled_date: "",
    description: defaultDescription,
  });
  const [error, setError] = useState<string | null>(null);
  const [permissionError, setPermissionError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [formModified, setFormModified] = useState(false);
  const [userPermissions, setUserPermissions] = useState<Record<number, UserPermissions>>({});
  const [checkingPermissions, setCheckingPermissions] = useState(false);

  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      try {
        const usersResponse = await getAllUsers();
        setUsers(usersResponse?.users || []);
      } catch (err) {
        setError("Failed to fetch users");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };
    fetchUsers();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous errors
    setError(null);

    // Validate user selection
    if (eventData.user_id === 0) {
      setError("Selecteer een gebruiker");
      return;
    }

    // Als er geen klant-ID is, kunnen we geen gebeurtenis aanmaken
    if (!document.customer_id) {
      setError("Geen klant-ID gevonden");
      return;
    }

    // Check if the selected user has permission for this document type
    if (!isQuotation) {
      const hasPermission = checkUserPermission(eventData.user_id);
      if (!hasPermission) {
        const selectedUser = users.find(u => u.id === eventData.user_id);
        if (selectedUser && selectedUser.role !== 'administrator') {
          setPermissionError(`${selectedUser.name} heeft geen rechten om ${document.document_type} gebeurtenissen te beheren.`);
          return;
        }
      }
    }

    setLoading(true);
    try {
      const formattedDate = new Date(eventData.scheduled_date).toISOString();

      // Bepaal het type gebeurtenis op basis van het document
      const eventType = isQuotation ? 'checklist oplevering installatie' : document.document_type;

      console.log('Creating event with data:', {
        customer_id: document.customer_id,
        event_type: eventType,
        description: eventData.description,
        scheduled_date: formattedDate,
        user_id: eventData.user_id,
        document_id: document.id
      });

      await createEvent(
        document.customer_id,
        eventType,
        eventData.description,
        formattedDate,
        eventData.user_id,
        document.id
      );
      onEventCreated();
      onClose();
    } catch (err: any) {
      setError(err.response?.data?.error || "Fout bij het aanmaken van de gebeurtenis");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Add a useEffect to fetch user permissions when users are loaded
  useEffect(() => {
    const fetchUserPermissions = async () => {
      try {
        // Only fetch for non-admin users
        const nonAdminUsers = users.filter(user => user.role !== 'administrator');
        const permissionsMap: Record<number, UserPermissions> = {};

        for (const user of nonAdminUsers) {
          try {
            setCheckingPermissions(true);
            const userWithPermissions = await getUserPermissions(user.id);
            if (userWithPermissions.permissions) {
              permissionsMap[user.id] = userWithPermissions.permissions;
            }
          } catch (err) {
            console.error(`Error fetching permissions for user ${user.id}:`, err);
          }
        }

        setUserPermissions(permissionsMap);
      } catch (err) {
        console.error('Error fetching user permissions:', err);
      } finally {
        setCheckingPermissions(false);
      }
    };

    if (users.length > 0) {
      fetchUserPermissions();
    }
  }, [users]);

  // Check if the selected user has permission for this document type
  const checkUserPermission = (userId: number): boolean => {
    if (!userId) return false;

    const selectedUser = users.find(u => u.id === userId);

    // Administrators can handle all document types
    if (selectedUser?.role === 'administrator') return true;

    // Als er geen document is, kunnen we geen rechten controleren
    if (!document) return true;

    // Check if the user has permission for this document type
    const userPerms = userPermissions[userId];
    if (!userPerms) return false;

    return userPerms[document.document_type]?.can_upload === true;
  };

  const handleInputChange = (field: string, value: any) => {
    // If changing user_id, check permissions
    if (field === 'user_id') {
      const userId = value as number;

      // Clear any previous permission error
      setPermissionError(null);

      // If a user is selected, check permissions
      if (userId && document) {
        const hasPermission = checkUserPermission(userId);
        if (!hasPermission) {
          const selectedUser = users.find(u => u.id === userId);
          if (selectedUser && selectedUser.role !== 'administrator') {
            setPermissionError(`${selectedUser.name || selectedUser.email} heeft geen rechten om ${document.document_type} gebeurtenissen te beheren.`);
          }
        }
      }
    }

    setEventData({ ...eventData, [field]: value });
    setFormModified(true);
  };

  const handleClose = () => {
    if (formModified) {
      showConfirmation({
        title: "Wijzigingen negeren",
        message: "U heeft niet-opgeslagen wijzigingen. Weet u zeker dat u dit venster wilt sluiten?",
        confirmText: "Negeren",
        cancelText: "Annuleren",
        confirmButtonClass: "bg-red-600 hover:bg-red-700",
        onConfirm: () => onClose()
      });
    } else {
      onClose();
    }
  };

  if (loading) {
    return <LoadingSpinner message="Loading..." fullScreen={false} />;
  }

  return (
    <div className="fixed inset-0 modal-overlay p-4 flex items-center justify-center z-50" onClick={handleClose}>
      <div className="modal-content w-full max-w-lg max-h-[90vh] overflow-y-auto bg-white dark:bg-dark-secondary shadow-xl rounded-lg" onClick={(e) => e.stopPropagation()}>
        <div className="flex justify-between items-center mb-6 border-b border-gray-200 dark:border-gray-700 pb-3 px-6 pt-4">
          <h2 className="text-xl sm:text-2xl font-semibold text-amspm-primary uppercase">
            Nieuwe Geplande Gebeurtenis
          </h2>
          <button
            type="button"
            onClick={handleClose}
            className="text-gray-500 hover:text-gray-700 text-xl transition-colors duration-200"
            disabled={loading}
            aria-label="Close"
          >
            &times;
          </button>
        </div>
        {error && (
          <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 mx-6 rounded shadow-sm">
            <div className="flex">
              <div className="flex-shrink-0">
                <FaExclamationTriangle className="h-5 w-5 text-red-500" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {permissionError && (
          <div className="bg-orange-100 border-l-4 border-orange-500 text-orange-700 p-4 mb-6 mx-6 rounded shadow-sm">
            <div className="flex">
              <div className="flex-shrink-0">
                <FaExclamationTriangle className="h-5 w-5 text-orange-500" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-orange-800">Rechten Fout</p>
                <p className="text-sm">{permissionError}</p>
                <p className="mt-1 text-xs">Selecteer een andere gebruiker of neem contact op met een beheerder om rechten bij te werken.</p>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="px-6 pb-6 space-y-4">
          <div className="form-group">
            <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
              {isQuotation ? 'Offerte' : 'Document'}
            </label>
            <p className="text-gray-700 dark:text-gray-300">
              {isQuotation
                ? `Offerte (Document ID: ${document.id}, Klant ID: ${document.customer_id})`
                : `${document.document_type} (Klant ID: ${document.customer_id})`
              }
            </p>
          </div>
          <div className="form-group">
            <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
              Gebruiker Toewijzen <span className="text-red-500">*</span>
            </label>
            <select
              value={eventData.user_id}
              onChange={(e) => handleInputChange('user_id', parseInt(e.target.value))}
              className="input"
              required
              disabled={loading}
            >
              <option value={0}>Selecteer Gebruiker</option>
              {users.map((user) => (
                <option key={user.id} value={user.id}>
                  {user.name ? `${user.name}` : user.email} ({user.role === 'administrator' ? 'Beheerder' : 'Monteur'})
                </option>
              ))}
            </select>
          </div>
          <div className="form-group">
            <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
              Geplande Datum <span className="text-red-500">*</span>
            </label>
            <input
              type="datetime-local"
              value={eventData.scheduled_date}
              onChange={(e) => handleInputChange('scheduled_date', e.target.value)}
              className="input"
              required
              disabled={loading}
            />
          </div>
          <div className="form-group">
            <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
              Omschrijving <span className="text-red-500">*</span>
            </label>
            <textarea
              value={eventData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className="input"
              required
              disabled={loading}
              rows={4}
            />
          </div>
          <div className="col-span-1 sm:col-span-2 flex flex-col-reverse sm:flex-row sm:space-x-4 space-y-2 space-y-reverse sm:space-y-0 justify-end mt-6">
            <button
              type="button"
              onClick={handleClose}
              className="btn btn-outline w-full sm:w-auto"
              disabled={loading}
            >
              Annuleren
            </button>
            <button
              type="submit"
              className="btn btn-primary w-full sm:w-auto"
              disabled={loading || !!permissionError || checkingPermissions}
            >
              {loading ? "Bezig..." : checkingPermissions ? "Rechten controleren..." : "Gebeurtenis Aanmaken"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateUpcomingEventModal;
