import React, { useState, useEffect } from 'react';

interface CustomDateInputProps {
  id: string;
  name: string;
  value: string | null;
  onChange: (name: string, value: string | null) => void;
  className?: string;
  disabled?: boolean;
  hasError?: boolean;
}

const CustomDateInput: React.FC<CustomDateInputProps> = ({
  id,
  name,
  value,
  onChange,
  className = '',
  disabled = false,
  hasError = false
}) => {
  // State for day, month, and year
  const [day, setDay] = useState<string>('');
  const [month, setMonth] = useState<string>('');
  const [year, setYear] = useState<string>('');

  // Update day, month, and year when value changes
  useEffect(() => {
    if (value) {
      try {
        const date = new Date(value);
        if (!isNaN(date.getTime())) {
          setYear(date.getFullYear().toString());
          setMonth((date.getMonth() + 1).toString().padStart(2, '0'));
          setDay(date.getDate().toString().padStart(2, '0'));
        }
      } catch (e) {
        console.error('Invalid date:', value);
      }
    } else {
      setDay('');
      setMonth('');
      setYear('');
    }
  }, [value]);

  // Update the parent component when day, month, or year changes
  const updateDate = () => {
    if (day && month && year) {
      const formattedDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      onChange(name, formattedDate);
    } else {
      onChange(name, null);
    }
  };

  return (
    <div className="flex space-x-2">
      <div className="w-1/4">
        <input
          type="text"
          placeholder="DD"
          value={day}
          onChange={(e) => {
            const newDay = e.target.value.replace(/\D/g, '').slice(0, 2);
            setDay(newDay);
            if (newDay.length === 2) {
              document.getElementById(`${id}-month`)?.focus();
            }
          }}
          onBlur={updateDate}
          className={`input text-center ${hasError ? 'border-red-500' : ''} ${className}`}
          disabled={disabled}
          maxLength={2}
          id={`${id}-day`}
        />
      </div>
      <div className="w-1/4">
        <input
          type="text"
          placeholder="MM"
          value={month}
          onChange={(e) => {
            const newMonth = e.target.value.replace(/\D/g, '').slice(0, 2);
            setMonth(newMonth);
            if (newMonth.length === 2) {
              document.getElementById(`${id}-year`)?.focus();
            }
          }}
          onBlur={updateDate}
          className={`input text-center ${hasError ? 'border-red-500' : ''} ${className}`}
          disabled={disabled}
          maxLength={2}
          id={`${id}-month`}
        />
      </div>
      <div className="w-2/4">
        <input
          type="text"
          placeholder="JJJJ"
          value={year}
          onChange={(e) => {
            const newYear = e.target.value.replace(/\D/g, '').slice(0, 4);
            setYear(newYear);
          }}
          onBlur={updateDate}
          className={`input text-center ${hasError ? 'border-red-500' : ''} ${className}`}
          disabled={disabled}
          maxLength={4}
          id={`${id}-year`}
        />
      </div>
    </div>
  );
};

export default CustomDateInput;
