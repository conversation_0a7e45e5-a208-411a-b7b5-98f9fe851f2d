import React, { useState, useEffect, useRef } from 'react';
import { Customer } from '../types/customer';
import { customerSchema, validateData } from '../utils/validation';
import { useConfirmation } from '../context/ConfirmationContext';
import { FaExclamationTriangle } from 'react-icons/fa';

interface CustomerModalProps {
  customer: Partial<Customer>;
  onClose: () => void;
  onSubmit: (e: React.FormEvent) => void;
  setCustomer: (customer: Partial<Customer>) => void;
  isEditing: boolean;
  submitting: boolean;
  embedded?: boolean; // Whether the modal is embedded in a page rather than shown as a modal
}

const CustomerModal: React.FC<CustomerModalProps> = ({
  customer,
  onClose,
  onSubmit,
  setCustomer,
  isEditing,
  submitting,
  embedded = false
}) => {
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<'basic' | 'contact' | 'financial' | 'additional'>('basic');
  const [formModified, setFormModified] = useState(false);
  const initialCustomerRef = useRef<Partial<Customer>>({...customer});
  const { showConfirmation } = useConfirmation();

  // Track form modifications
  useEffect(() => {
    // Compare current customer with initial state
    const hasChanges = JSON.stringify(customer) !== JSON.stringify(initialCustomerRef.current);
    setFormModified(hasChanges);
  }, [customer]);

  // Function to handle modal close with confirmation if needed
  const handleClose = () => {
    if (formModified) {
      showConfirmation({
        title: "Wijzigingen negeren",
        message: "U heeft niet-opgeslagen wijzigingen. Weet u zeker dat u dit venster wilt sluiten?",
        confirmText: "Negeren",
        cancelText: "Annuleren",
        confirmButtonClass: "bg-red-600 hover:bg-red-700",
        onConfirm: () => onClose()
      });
    } else {
      onClose();
    }
  };

  // Render the form content (shared between modal and embedded modes)
  const renderFormContent = () => (
    <>
      {/* Tab Navigation */}
      <div className="px-6 mb-6">
        <div className="flex flex-wrap border-b border-gray-200 dark:border-gray-700">
          <button
            className={`mr-4 py-2 px-1 font-medium text-sm ${activeTab === 'basic'
              ? 'text-amspm-primary dark:text-dark-accent border-b-2 border-amspm-primary dark:border-dark-accent'
              : 'text-gray-500 dark:text-gray-400 hover:text-amspm-primary dark:hover:text-dark-accent'}`}
            onClick={() => setActiveTab('basic')}
            disabled={submitting}
            type="button"
          >
            Basis Informatie
          </button>
          <button
            className={`mr-4 py-2 px-1 font-medium text-sm ${activeTab === 'contact'
              ? 'text-amspm-primary dark:text-dark-accent border-b-2 border-amspm-primary dark:border-dark-accent'
              : 'text-gray-500 dark:text-gray-400 hover:text-amspm-primary dark:hover:text-dark-accent'}`}
            onClick={() => setActiveTab('contact')}
            disabled={submitting}
            type="button"
          >
            Contactgegevens
          </button>
          <button
            className={`mr-4 py-2 px-1 font-medium text-sm ${activeTab === 'financial'
              ? 'text-amspm-primary dark:text-dark-accent border-b-2 border-amspm-primary dark:border-dark-accent'
              : 'text-gray-500 dark:text-gray-400 hover:text-amspm-primary dark:hover:text-dark-accent'}`}
            onClick={() => setActiveTab('financial')}
            disabled={submitting}
            type="button"
          >
            Financiële Gegevens
          </button>
          <button
            className={`mr-4 py-2 px-1 font-medium text-sm ${activeTab === 'additional'
              ? 'text-amspm-primary dark:text-dark-accent border-b-2 border-amspm-primary dark:border-dark-accent'
              : 'text-gray-500 dark:text-gray-400 hover:text-amspm-primary dark:hover:text-dark-accent'}`}
            onClick={() => setActiveTab('additional')}
            disabled={submitting}
            type="button"
          >
            Aanvullende Informatie
          </button>
        </div>
      </div>

      {validationErrors.length > 0 && (
        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6 mx-6 rounded shadow-sm">
          <div className="flex">
            <div className="flex-shrink-0">
              <FaExclamationTriangle className="h-5 w-5 text-yellow-500" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">Corrigeer de volgende fouten:</h3>
              <ul className="mt-2 text-sm text-yellow-700 list-disc list-inside">
                {validationErrors.map((err, index) => (
                  <li key={index}>{err}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </>
  );

  // Form submission handler
  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setValidationErrors([]);

    // Validate input data
    const { isValid, errors } = await validateData(customerSchema, customer);

    if (!isValid) {
      setValidationErrors(errors);
      return;
    }

    // If validation passes, proceed with the original onSubmit
    onSubmit(e);
  };

  // Render the component based on whether it's embedded or not
  if (embedded) {
    return (
      <div className="w-full">
        {renderFormContent()}

        <form onSubmit={handleFormSubmit} className="px-6 pb-6">

          {/* Basic Information Tab */}
          <div className={`${activeTab === 'basic' ? 'block' : 'hidden'}`}>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Naam <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={customer.name || ''}
                  onChange={(e) => setCustomer({ ...customer, name: e.target.value })}
                  className="input"
                  required
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Code
                </label>
                <input
                  type="text"
                  value={customer.code || ''}
                  onChange={(e) => setCustomer({ ...customer, code: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  KvK Nummer
                </label>
                <input
                  type="text"
                  value={customer.kvk_number || ''}
                  onChange={(e) => setCustomer({ ...customer, kvk_number: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Datum
                </label>
                <input
                  type="date"
                  value={customer.date || ''}
                  onChange={(e) => setCustomer({ ...customer, date: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Aanhef
                </label>
                <select
                  value={customer.title || ''}
                  onChange={(e) => setCustomer({ ...customer, title: e.target.value })}
                  className="input"
                  disabled={submitting}
                >
                  <option value="">Selecteer aanhef</option>
                  <option value="Dhr.">Dhr.</option>
                  <option value="Mevr.">Mevr.</option>
                  <option value="Bedrijf">Bedrijf</option>
                </select>
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Geslacht
                </label>
                <select
                  value={customer.gender || ''}
                  onChange={(e) => setCustomer({ ...customer, gender: e.target.value })}
                  className="input"
                  disabled={submitting}
                >
                  <option value="">Selecteer geslacht</option>
                  <option value="M">Man</option>
                  <option value="V">Vrouw</option>
                  <option value="O">Overig</option>
                </select>
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Contactpersoon
                </label>
                <input
                  type="text"
                  value={customer.contact_person || ''}
                  onChange={(e) => setCustomer({ ...customer, contact_person: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Soort
                </label>
                <select
                  value={customer.customer_type || ''}
                  onChange={(e) => setCustomer({ ...customer, customer_type: e.target.value })}
                  className="input"
                  disabled={submitting}
                >
                  <option value="">Selecteer type</option>
                  <option value="Particulier">Particulier</option>
                  <option value="Zakelijk">Zakelijk</option>
                  <option value="Overheid">Overheid</option>
                </select>
              </div>
            </div>
          </div>

          {/* Contact Information Tab */}
          <div className={`${activeTab === 'contact' ? 'block' : 'hidden'}`}>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Adres
                </label>
                <input
                  type="text"
                  value={customer.address || ''}
                  onChange={(e) => setCustomer({ ...customer, address: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Postcode
                </label>
                <input
                  type="text"
                  value={customer.postal_code || ''}
                  onChange={(e) => setCustomer({ ...customer, postal_code: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Plaats
                </label>
                <input
                  type="text"
                  value={customer.city || ''}
                  onChange={(e) => setCustomer({ ...customer, city: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Land
                </label>
                <input
                  type="text"
                  value={customer.country || ''}
                  onChange={(e) => setCustomer({ ...customer, country: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Adres 2
                </label>
                <input
                  type="text"
                  value={customer.address2 || ''}
                  onChange={(e) => setCustomer({ ...customer, address2: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Postcode 2
                </label>
                <input
                  type="text"
                  value={customer.postal_code2 || ''}
                  onChange={(e) => setCustomer({ ...customer, postal_code2: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Plaats 2
                </label>
                <input
                  type="text"
                  value={customer.city2 || ''}
                  onChange={(e) => setCustomer({ ...customer, city2: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Land 2
                </label>
                <input
                  type="text"
                  value={customer.country2 || ''}
                  onChange={(e) => setCustomer({ ...customer, country2: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Telefoon
                </label>
                <input
                  type="tel"
                  value={customer.phone || ''}
                  onChange={(e) => setCustomer({ ...customer, phone: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Mobiel
                </label>
                <input
                  type="tel"
                  value={customer.mobile || ''}
                  onChange={(e) => setCustomer({ ...customer, mobile: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Fax
                </label>
                <input
                  type="tel"
                  value={customer.fax || ''}
                  onChange={(e) => setCustomer({ ...customer, fax: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Email
                </label>
                <input
                  type="email"
                  value={customer.email || ''}
                  onChange={(e) => setCustomer({ ...customer, email: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Website
                </label>
                <input
                  type="url"
                  value={customer.website || ''}
                  onChange={(e) => setCustomer({ ...customer, website: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group sm:col-span-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="no_email"
                    checked={customer.no_email || false}
                    onChange={(e) => setCustomer({ ...customer, no_email: e.target.checked })}
                    className="form-checkbox h-5 w-5 text-amspm-primary"
                    disabled={submitting}
                  />
                  <label htmlFor="no_email" className="ml-2 text-gray-700 dark:text-gray-300">
                    Geen e-mail ontvangen
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Financial Information Tab */}
          <div className={`${activeTab === 'financial' ? 'block' : 'hidden'}`}>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  BTW-nummer
                </label>
                <input
                  type="text"
                  value={customer.vat_number || ''}
                  onChange={(e) => setCustomer({ ...customer, vat_number: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Bankrekening
                </label>
                <input
                  type="text"
                  value={customer.bank_account || ''}
                  onChange={(e) => setCustomer({ ...customer, bank_account: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Girorekening
                </label>
                <input
                  type="text"
                  value={customer.giro_account || ''}
                  onChange={(e) => setCustomer({ ...customer, giro_account: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  IBAN
                </label>
                <input
                  type="text"
                  value={customer.iban || ''}
                  onChange={(e) => setCustomer({ ...customer, iban: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  BIC
                </label>
                <input
                  type="text"
                  value={customer.bic || ''}
                  onChange={(e) => setCustomer({ ...customer, bic: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Betalingstermijn (dagen)
                </label>
                <input
                  type="number"
                  value={customer.payment_term || ''}
                  onChange={(e) => setCustomer({ ...customer, payment_term: parseInt(e.target.value) || null })}
                  className="input"
                  disabled={submitting}
                  min="0"
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Email-facturen
                </label>
                <input
                  type="email"
                  value={customer.invoice_email || ''}
                  onChange={(e) => setCustomer({ ...customer, invoice_email: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Email-herinneringen
                </label>
                <input
                  type="email"
                  value={customer.reminder_email || ''}
                  onChange={(e) => setCustomer({ ...customer, reminder_email: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>
            </div>
          </div>

          {/* Additional Information Tab */}
          <div className={`${activeTab === 'additional' ? 'block' : 'hidden'}`}>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Sepa-machtigingsoort
                </label>
                <select
                  value={customer.sepa_auth_type || ''}
                  onChange={(e) => setCustomer({ ...customer, sepa_auth_type: e.target.value })}
                  className="input"
                  disabled={submitting}
                >
                  <option value="">Selecteer machtigingsoort</option>
                  <option value="Eenmalig">Eenmalig</option>
                  <option value="Doorlopend">Doorlopend</option>
                </select>
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Machtigingskenmerk
                </label>
                <input
                  type="text"
                  value={customer.mandate_reference || ''}
                  onChange={(e) => setCustomer({ ...customer, mandate_reference: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Machtigingsdatum
                </label>
                <input
                  type="date"
                  value={customer.mandate_date || ''}
                  onChange={(e) => setCustomer({ ...customer, mandate_date: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Nieuwsbriefgroepen
                </label>
                <input
                  type="text"
                  value={customer.newsletter_groups || ''}
                  onChange={(e) => setCustomer({ ...customer, newsletter_groups: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Abonnementen
                </label>
                <input
                  type="text"
                  value={customer.subscriptions || ''}
                  onChange={(e) => setCustomer({ ...customer, subscriptions: e.target.value })}
                  className="input"
                  disabled={submitting}
                />
              </div>

              <div className="form-group sm:col-span-2">
                <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                  Algemene Notities
                </label>
                <textarea
                  value={customer.notes || ''}
                  onChange={(e) => setCustomer({ ...customer, notes: e.target.value })}
                  className="input"
                  rows={4}
                  disabled={submitting}
                  placeholder="Voeg algemene notities over deze klant toe..."
                />
              </div>
            </div>
          </div>

          <div className="col-span-1 sm:col-span-2 flex flex-col-reverse sm:flex-row sm:space-x-4 space-y-2 space-y-reverse sm:space-y-0 justify-end mt-6">
            <button
              type="button"
              onClick={handleClose}
              className="btn btn-outline w-full sm:w-auto"
              disabled={submitting}
            >
              Annuleren
            </button>
            <button
              type="submit"
              className="btn btn-primary w-full sm:w-auto"
              disabled={submitting}
            >
              {submitting ? "Bezig..." : isEditing ? "Bijwerken" : "Aanmaken"}
            </button>
          </div>
        </form>
      </div>
    );
  } else {
    // Modal overlay version
    return (
      <div className="fixed inset-0 modal-overlay p-4 flex items-center justify-center z-50" onClick={handleClose}>
        <div className="modal-content max-w-4xl max-h-[90vh] overflow-y-auto w-full bg-white dark:bg-dark-secondary shadow-xl rounded-lg" onClick={(e) => e.stopPropagation()}>
          <div className="flex justify-between items-center mb-6 border-b border-gray-200 dark:border-gray-700 pb-3 px-6 pt-4">
            <h2 className="text-xl sm:text-2xl font-semibold text-amspm-primary uppercase">
              {isEditing ? "Klant Bijwerken" : "Nieuwe Klant"}
            </h2>
            <button
              type="button"
              onClick={handleClose}
              className="text-gray-500 hover:text-gray-700 text-xl transition-colors duration-200"
              disabled={submitting}
              aria-label="Close"
            >
              &times;
            </button>
          </div>

          {renderFormContent()}

          <form onSubmit={handleFormSubmit} className="px-6 pb-6">
            {/* Basic Information Tab */}
            <div className={`${activeTab === 'basic' ? 'block' : 'hidden'}`}>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    Naam <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={customer.name || ''}
                    onChange={(e) => setCustomer({ ...customer, name: e.target.value })}
                    className="input"
                    required
                    disabled={submitting}
                  />
                </div>

                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    Code
                  </label>
                  <input
                    type="text"
                    value={customer.code || ''}
                    onChange={(e) => setCustomer({ ...customer, code: e.target.value })}
                    className="input"
                    disabled={submitting}
                  />
                </div>

                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    KvK-nummer
                  </label>
                  <input
                    type="text"
                    value={customer.kvk_number || ''}
                    onChange={(e) => setCustomer({ ...customer, kvk_number: e.target.value })}
                    className="input"
                    disabled={submitting}
                  />
                </div>

                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    Contactpersoon
                  </label>
                  <input
                    type="text"
                    value={customer.contact_person || ''}
                    onChange={(e) => setCustomer({ ...customer, contact_person: e.target.value })}
                    className="input"
                    disabled={submitting}
                  />
                </div>

                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    Geslacht
                  </label>
                  <select
                    value={customer.gender || ''}
                    onChange={(e) => setCustomer({ ...customer, gender: e.target.value })}
                    className="input"
                    disabled={submitting}
                  >
                    <option value="">Selecteer geslacht</option>
                    <option value="M">Man</option>
                    <option value="V">Vrouw</option>
                    <option value="O">Anders</option>
                  </select>
                </div>

                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    Titel
                  </label>
                  <input
                    type="text"
                    value={customer.title || ''}
                    onChange={(e) => setCustomer({ ...customer, title: e.target.value })}
                    className="input"
                    disabled={submitting}
                  />
                </div>
              </div>
            </div>

            {/* Contact Information Tab */}
            <div className={`${activeTab === 'contact' ? 'block' : 'hidden'}`}>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    Adres
                  </label>
                  <input
                    type="text"
                    value={customer.address || ''}
                    onChange={(e) => setCustomer({ ...customer, address: e.target.value })}
                    className="input"
                    disabled={submitting}
                  />
                </div>

                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    Postcode
                  </label>
                  <input
                    type="text"
                    value={customer.postal_code || ''}
                    onChange={(e) => setCustomer({ ...customer, postal_code: e.target.value })}
                    className="input"
                    disabled={submitting}
                  />
                </div>

                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    Stad
                  </label>
                  <input
                    type="text"
                    value={customer.city || ''}
                    onChange={(e) => setCustomer({ ...customer, city: e.target.value })}
                    className="input"
                    disabled={submitting}
                  />
                </div>

                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    Land
                  </label>
                  <input
                    type="text"
                    value={customer.country || ''}
                    onChange={(e) => setCustomer({ ...customer, country: e.target.value })}
                    className="input"
                    disabled={submitting}
                  />
                </div>

                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    Telefoon
                  </label>
                  <input
                    type="tel"
                    value={customer.phone || ''}
                    onChange={(e) => setCustomer({ ...customer, phone: e.target.value })}
                    className="input"
                    disabled={submitting}
                  />
                </div>

                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    Mobiel
                  </label>
                  <input
                    type="tel"
                    value={customer.mobile || ''}
                    onChange={(e) => setCustomer({ ...customer, mobile: e.target.value })}
                    className="input"
                    disabled={submitting}
                  />
                </div>

                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    E-mail
                  </label>
                  <input
                    type="email"
                    value={customer.email || ''}
                    onChange={(e) => setCustomer({ ...customer, email: e.target.value })}
                    className="input"
                    disabled={submitting}
                  />
                </div>

                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    Website
                  </label>
                  <input
                    type="url"
                    value={customer.website || ''}
                    onChange={(e) => setCustomer({ ...customer, website: e.target.value })}
                    className="input"
                    disabled={submitting}
                  />
                </div>
              </div>
            </div>

            {/* Financial Information Tab */}
            <div className={`${activeTab === 'financial' ? 'block' : 'hidden'}`}>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    BTW-nummer
                  </label>
                  <input
                    type="text"
                    value={customer.vat_number || ''}
                    onChange={(e) => setCustomer({ ...customer, vat_number: e.target.value })}
                    className="input"
                    disabled={submitting}
                  />
                </div>

                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    IBAN
                  </label>
                  <input
                    type="text"
                    value={customer.iban || ''}
                    onChange={(e) => setCustomer({ ...customer, iban: e.target.value })}
                    className="input"
                    disabled={submitting}
                  />
                </div>

                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    Bankrekening
                  </label>
                  <input
                    type="text"
                    value={customer.bank_account || ''}
                    onChange={(e) => setCustomer({ ...customer, bank_account: e.target.value })}
                    className="input"
                    disabled={submitting}
                  />
                </div>

                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    Girorekening
                  </label>
                  <input
                    type="text"
                    value={customer.giro_account || ''}
                    onChange={(e) => setCustomer({ ...customer, giro_account: e.target.value })}
                    className="input"
                    disabled={submitting}
                  />
                </div>
              </div>
            </div>

            {/* Additional Information Tab */}
            <div className={`${activeTab === 'additional' ? 'block' : 'hidden'}`}>
              <div className="grid grid-cols-1 gap-6">
                <div className="form-group">
                  <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
                    Opmerkingen
                  </label>
                  <textarea
                    value={customer.notes || ''}
                    onChange={(e) => setCustomer({ ...customer, notes: e.target.value })}
                    className="input"
                    rows={4}
                    disabled={submitting}
                  />
                </div>
              </div>
            </div>

            {/* Form buttons */}
            <div className="flex justify-end mt-6 space-x-3">
              <button
                type="button"
                onClick={handleClose}
                className="btn btn-outline"
                disabled={submitting}
              >
                Annuleren
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={submitting}
              >
                {submitting ? "Bezig..." : isEditing ? "Bijwerken" : "Aanmaken"}
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }
};

export default CustomerModal;
