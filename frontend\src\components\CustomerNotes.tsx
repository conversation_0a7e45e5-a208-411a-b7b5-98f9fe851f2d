import React, { useState, useEffect } from 'react';
import { CustomerNote } from '../types/customer_note';
import { getCustomerNotes, createCustomerNote, updateCustomerNote, deleteCustomerNote } from '../services/customerNoteService';
import { useAuth } from '../context/AuthContext';
import { useConfirmation } from '../context/ConfirmationContext';
import { FaPlus, FaEdit, FaTrash, FaSave, FaTimes } from 'react-icons/fa';
import LoadingSpinner from './LoadingSpinner';

interface CustomerNotesProps {
  customerId: number;
}

const CustomerNotes: React.FC<CustomerNotesProps> = ({ customerId }) => {
  const [notes, setNotes] = useState<CustomerNote[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [newNote, setNewNote] = useState<string>('');
  const [showAddForm, setShowAddForm] = useState<boolean>(false);
  const [editingNoteId, setEditingNoteId] = useState<number | null>(null);
  const [editContent, setEditContent] = useState<string>('');
  const [submitting, setSubmitting] = useState<boolean>(false);
  const { user } = useAuth();
  const { showConfirmation } = useConfirmation();

  useEffect(() => {
    fetchNotes();
  }, [customerId]);

  const fetchNotes = async () => {
    try {
      setLoading(true);
      const fetchedNotes = await getCustomerNotes(customerId);
      setNotes(fetchedNotes);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch notes');
    } finally {
      setLoading(false);
    }
  };

  const handleAddNote = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newNote.trim()) return;

    try {
      setSubmitting(true);
      const createdNote = await createCustomerNote({
        customer_id: customerId,
        content: newNote.trim()
      });
      setNotes([createdNote, ...notes]);
      setNewNote('');
      setShowAddForm(false);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to add note');
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdateNote = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editContent.trim() || !editingNoteId) return;

    try {
      setSubmitting(true);
      const updatedNote = await updateCustomerNote(editingNoteId, editContent.trim());
      setNotes(notes.map(note => note.id === editingNoteId ? updatedNote : note));
      setEditingNoteId(null);
      setEditContent('');
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to update note');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteNote = (noteId: number) => {
    showConfirmation({
      title: 'Delete Note',
      message: 'Are you sure you want to delete this note? This action cannot be undone.',
      confirmText: 'Delete',
      cancelText: 'Cancel',
      onConfirm: async () => {
        try {
          await deleteCustomerNote(noteId);
          setNotes(notes.filter(note => note.id !== noteId));
          setError(null);
        } catch (err: any) {
          setError(err.response?.data?.error || 'Failed to delete note');
        }
      }
    });
  };

  const startEditing = (note: CustomerNote) => {
    setEditingNoteId(note.id);
    setEditContent(note.content);
  };

  const cancelEditing = () => {
    setEditingNoteId(null);
    setEditContent('');
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('nl-NL', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  if (loading && notes.length === 0) {
    return <LoadingSpinner />;
  }

  return (
    <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-amspm-text">Notes</h2>
        {!showAddForm && (
          <button
            onClick={() => setShowAddForm(true)}
            className="btn btn-primary btn-sm flex items-center"
            disabled={submitting}
          >
            <FaPlus className="mr-2" /> Add Note
          </button>
        )}
      </div>

      {error && <p className="text-red-500 mb-4">{error}</p>}

      {showAddForm && (
        <form onSubmit={handleAddNote} className="mb-6 bg-gray-50 dark:bg-dark-secondary p-4 rounded-md">
          <div className="mb-3">
            <label htmlFor="newNote" className="block text-sm font-medium text-gray-700 dark:text-dark-text mb-1">
              New Note
            </label>
            <textarea
              id="newNote"
              value={newNote}
              onChange={(e) => setNewNote(e.target.value)}
              className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md focus:ring-amspm-primary focus:border-amspm-primary dark:bg-dark-input dark:text-dark-text"
              rows={3}
              placeholder="Enter your note here..."
              disabled={submitting}
              required
            />
          </div>
          <div className="flex justify-end space-x-2">
            <button
              type="button"
              onClick={() => setShowAddForm(false)}
              className="btn btn-outline btn-sm"
              disabled={submitting}
            >
              <FaTimes className="mr-1" /> Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary btn-sm"
              disabled={submitting || !newNote.trim()}
            >
              <FaSave className="mr-1" /> Save
            </button>
          </div>
        </form>
      )}

      {notes.length === 0 ? (
        <p className="text-gray-500 dark:text-dark-text-light italic">No notes available for this customer.</p>
      ) : (
        <div className="space-y-4">
          {notes.map((note) => (
            <div key={note.id} className="border-b border-gray-200 dark:border-gray-700 pb-4 last:border-0 last:pb-0">
              {editingNoteId === note.id ? (
                <form onSubmit={handleUpdateNote} className="bg-gray-50 dark:bg-dark-secondary p-3 rounded-md">
                  <textarea
                    value={editContent}
                    onChange={(e) => setEditContent(e.target.value)}
                    className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md focus:ring-amspm-primary focus:border-amspm-primary dark:bg-dark-input dark:text-dark-text mb-2"
                    rows={3}
                    disabled={submitting}
                    required
                  />
                  <div className="flex justify-end space-x-2">
                    <button
                      type="button"
                      onClick={cancelEditing}
                      className="btn btn-outline btn-sm"
                      disabled={submitting}
                    >
                      <FaTimes className="mr-1" /> Cancel
                    </button>
                    <button
                      type="submit"
                      className="btn btn-primary btn-sm"
                      disabled={submitting || !editContent.trim()}
                    >
                      <FaSave className="mr-1" /> Save
                    </button>
                  </div>
                </form>
              ) : (
                <>
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <span className="font-medium text-amspm-primary dark:text-dark-accent">{note.user_name}</span>
                      <span className="text-sm text-gray-500 dark:text-dark-text-light ml-2">
                        {formatDate(note.created_at)}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => startEditing(note)}
                        className="text-gray-500 hover:text-amspm-primary dark:text-gray-400 dark:hover:text-dark-accent"
                        title="Edit"
                      >
                        <FaEdit />
                      </button>
                      <button
                        onClick={() => handleDeleteNote(note.id)}
                        className="text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400"
                        title="Delete"
                      >
                        <FaTrash />
                      </button>
                    </div>
                  </div>
                  <p className="text-gray-700 dark:text-dark-text whitespace-pre-line">{note.content}</p>
                </>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CustomerNotes;
