import React, { useState } from 'react';
import { Document } from '../types/document';
import {
  FaChevronDown,
  FaChevronRight,
  FaDownload,
  FaTrash,
  FaEye,
  FaCalendarAlt,
  FaExclamationTriangle,
  FaCheckCircle,
  FaFileAlt,
  FaInfoCircle
} from 'react-icons/fa';

interface DocumentCategorySectionProps {
  categoryKey: string;
  category: any;
  documents: { [key: string]: Document[] };
  onDelete: (id: number) => void;
  submitting: boolean;
  onPreview?: (document: Document) => void;
  onDownload?: (document: Document) => void;
}

const DocumentCategorySection: React.FC<DocumentCategorySectionProps> = ({
  categoryKey,
  category,
  documents,
  onDelete,
  submitting,
  onPreview,
  onDownload
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  
  const totalDocuments = Object.values(documents).reduce((sum, docs) => sum + docs.length, 0);
  
  if (totalDocuments === 0) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('nl-NL', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const getStatusIcon = (document: Document) => {
    // For documents without version status (not_applicable), show different icon
    if (document.status === "not_applicable") {
      return <FaInfoCircle className="text-blue-500" title="Geen versie status" />;
    }

    if (document.expiry_date) {
      const expiryDate = new Date(document.expiry_date);
      const now = new Date();
      const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

      if (daysUntilExpiry < 0) {
        return <FaExclamationTriangle className="text-red-500" title="Verlopen" />;
      } else if (daysUntilExpiry <= 30) {
        return <FaExclamationTriangle className="text-yellow-500" title={`Verloopt over ${daysUntilExpiry} dagen`} />;
      }
    }
    return <FaCheckCircle className="text-green-500" title="Actief" />;
  };

  const formatDocumentTypeName = (type: string) => {
    const typeNames: { [key: string]: string } = {
      'beveiligingscertificaat': 'Beveiligingscertificaat',
      'onderhoudscontract': 'Onderhoudscontract',
      'meldkamercontract': 'Meldkamercontract',
      'checklist oplevering installatie': 'Checklist Oplevering',
      'vrije_documenten': 'Vrije Documenten',
      'projectietekening': 'Projectietekening',
      'beveiligingsplan': 'Beveiligingsplan',
      'kabeltekeningen': 'Kabeltekeningen',
      'intakedocument': 'Intakedocument',
      'onderhoudsbon': 'Onderhoudsbon',
      'werkbon': 'Werkbon',
      'offerte': 'Offerte',
      'factuur': 'Factuur'
    };
    return typeNames[type] || type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <div className={`border rounded-lg ${category.borderColor} ${category.bgColor} overflow-hidden`}>
      {/* Category Header */}
      <div 
        className="p-4 cursor-pointer hover:bg-opacity-80 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <category.icon className={`text-xl ${category.color}`} />
            <div>
              <h3 className="text-lg font-semibold text-amspm-text dark:text-dark-text">
                {category.title}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {totalDocuments} document{totalDocuments !== 1 ? 'en' : ''}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${category.color} bg-white dark:bg-gray-800`}>
              {totalDocuments}
            </span>
            {isExpanded ? (
              <FaChevronDown className="text-gray-400" />
            ) : (
              <FaChevronRight className="text-gray-400" />
            )}
          </div>
        </div>
      </div>

      {/* Documents List */}
      {isExpanded && (
        <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-dark-card">
          {Object.entries(documents).map(([documentType, docs]) => (
            <div key={documentType} className="border-b border-gray-100 dark:border-gray-800 last:border-b-0">
              {/* Document Type Subheader */}
              <div className="px-4 py-2 bg-gray-50 dark:bg-gray-800">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {formatDocumentTypeName(documentType)} ({docs.length})
                </h4>
              </div>
              
              {/* Document Items */}
              <div className="divide-y divide-gray-100 dark:divide-gray-800">
                {docs.map((doc) => (
                  <div key={doc.id} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 flex-1 min-w-0">
                        <div className="flex-shrink-0">
                          {getStatusIcon(doc)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <FaFileAlt className="text-gray-400 flex-shrink-0" />
                            <p className="text-sm font-medium text-amspm-text dark:text-dark-text truncate">
                              {doc.name || `${formatDocumentTypeName(doc.document_type)}`}
                            </p>
                          </div>
                          <div className="flex items-center space-x-4 mt-1">
                            <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                              <FaCalendarAlt />
                              <span>Geüpload: {formatDate(doc.created_at)}</span>
                            </div>
                            {doc.expiry_date && (
                              <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                                <FaCalendarAlt />
                                <span>Verloopt: {formatDate(doc.expiry_date)}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      {/* Action Buttons */}
                      <div className="flex items-center space-x-2 flex-shrink-0">
                        {onPreview && (
                          <button
                            onClick={() => onPreview(doc)}
                            className="p-2 text-gray-400 hover:text-amspm-primary dark:hover:text-dark-accent transition-colors"
                            title="Bekijk document"
                          >
                            <FaEye />
                          </button>
                        )}
                        {onDownload && (
                          <button
                            onClick={() => onDownload(doc)}
                            className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                            title="Download document"
                          >
                            <FaDownload />
                          </button>
                        )}
                        <button
                          onClick={() => onDelete(doc.id)}
                          disabled={submitting}
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors disabled:opacity-50"
                          title="Verwijder document"
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DocumentCategorySection;
