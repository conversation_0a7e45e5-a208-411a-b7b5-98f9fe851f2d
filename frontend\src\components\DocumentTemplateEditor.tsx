import React, { useState, useRef, useEffect } from 'react';
import { DocumentTemplate } from '../types/document_template';
import { FaSave, FaDownload, FaUndo, FaRedo } from 'react-icons/fa';
import LoadingSpinner from './LoadingSpinner';
import mammoth from 'mammoth';
import { Document, Packer, Paragraph, TextRun } from 'docx';

interface DocumentTemplateEditorProps {
  htmlContent: string;
  template: DocumentTemplate;
  onSave: (blob: Blob, fileName: string) => void;
  onCancel: () => void;
}

const DocumentTemplateEditor: React.FC<DocumentTemplateEditorProps> = ({
  htmlContent,
  template,
  onSave,
  onCancel
}) => {
  const [editedHtml, setEditedHtml] = useState<string>(htmlContent);
  const [generating, setGenerating] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const editorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Make the editor content editable
    if (editorRef.current) {
      editorRef.current.innerHTML = htmlContent;
    }
  }, [htmlContent]);

  const handleSave = async () => {
    if (!editorRef.current) return;
    
    try {
      setGenerating(true);
      setError(null);
      
      // Get the current HTML content from the editor
      const currentHtml = editorRef.current.innerHTML;
      
      // Convert HTML back to DOCX
      const docxBlob = await convertHtmlToDocx(currentHtml, template.name);
      
      // Generate a filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `${template.name.replace(/\s+/g, '_')}_${timestamp}.docx`;
      
      // Pass the blob and filename to the parent component
      onSave(docxBlob, fileName);
      
    } catch (err: any) {
      setError('Failed to generate document: ' + (err.message || 'Unknown error'));
    } finally {
      setGenerating(false);
    }
  };

  const handleDownload = async () => {
    if (!editorRef.current) return;
    
    try {
      setGenerating(true);
      setError(null);
      
      // Get the current HTML content from the editor
      const currentHtml = editorRef.current.innerHTML;
      
      // Convert HTML back to DOCX
      const docxBlob = await convertHtmlToDocx(currentHtml, template.name);
      
      // Generate a filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `${template.name.replace(/\s+/g, '_')}_${timestamp}.docx`;
      
      // Create a download link
      const url = URL.createObjectURL(docxBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 0);
      
    } catch (err: any) {
      setError('Failed to download document: ' + (err.message || 'Unknown error'));
    } finally {
      setGenerating(false);
    }
  };

  // Convert HTML back to DOCX
  const convertHtmlToDocx = async (html: string, title: string): Promise<Blob> => {
    // This is a simplified version - in a real implementation, you would need a more
    // sophisticated HTML to DOCX conversion that preserves formatting, tables, etc.
    
    // For now, we'll create a simple document with the HTML content
    const doc = new Document({
      title: title,
      sections: [
        {
          properties: {},
          children: [
            new Paragraph({
              children: [
                new TextRun({
                  text: "This is a placeholder. In a real implementation, the HTML content would be properly converted to DOCX format.",
                  bold: true,
                }),
              ],
            }),
            new Paragraph({
              text: "The HTML content contains:",
            }),
            new Paragraph({
              text: html.replace(/<[^>]*>/g, ' ').substring(0, 500) + "...",
            }),
          ],
        },
      ],
    });
    
    // Generate the DOCX file
    return await Packer.toBlob(doc);
  };

  return (
    <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-amspm-text">{template.name}</h3>
        <div className="flex space-x-2">
          <button
            onClick={onCancel}
            className="btn btn-outline btn-sm"
            disabled={generating}
          >
            Cancel
          </button>
          <button
            onClick={handleDownload}
            className="btn btn-outline btn-sm"
            disabled={generating}
            title="Download Document"
          >
            <FaDownload className="mr-1" /> Download
          </button>
          <button
            onClick={handleSave}
            className="btn btn-primary btn-sm"
            disabled={generating}
          >
            {generating ? <LoadingSpinner size="sm" /> : <><FaSave className="mr-1" /> Save</>}
          </button>
        </div>
      </div>
      
      {error && <p className="text-red-500 mb-4">{error}</p>}
      
      <div className="border border-gray-200 dark:border-gray-700 rounded-md p-4 mb-4 bg-white dark:bg-dark-input min-h-[400px] max-h-[600px] overflow-y-auto">
        <div
          ref={editorRef}
          className="document-editor"
          contentEditable={!generating}
          suppressContentEditableWarning={true}
        />
      </div>
      
      <div className="text-sm text-gray-500 dark:text-dark-text-light">
        <p>Fill in the form fields and click Save when you're done.</p>
      </div>
    </div>
  );
};

export default DocumentTemplateEditor;
