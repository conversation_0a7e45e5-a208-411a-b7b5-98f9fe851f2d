import React, { useState, useEffect } from 'react';
import { DocumentTemplate } from '../types/document_template';
import { getTemplatesByDocumentType, getTemplateFileUrl } from '../services/documentTemplateService';
import LoadingSpinner from './LoadingSpinner';
import { FaFileWord, FaDownload } from 'react-icons/fa';

interface DocumentTemplateSelectorProps {
  documentType: string;
  onTemplateSelected: (template: DocumentTemplate) => void;
}

const DocumentTemplateSelector: React.FC<DocumentTemplateSelectorProps> = ({
  documentType,
  onTemplateSelected
}) => {
  const [templates, setTemplates] = useState<DocumentTemplate[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplate | null>(null);

  useEffect(() => {
    if (documentType) {
      fetchTemplates();
    }
  }, [documentType]);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const fetchedTemplates = await getTemplatesByDocumentType(documentType);
      setTemplates(fetchedTemplates);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch templates');
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateSelect = (template: DocumentTemplate) => {
    setSelectedTemplate(template);
    onTemplateSelected(template);
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-4">
      <h3 className="text-lg font-semibold text-amspm-text mb-4">Select Template</h3>

      {error && <p className="text-red-500 mb-4">{error}</p>}

      {templates.length === 0 ? (
        <p className="text-gray-500 dark:text-dark-text-light italic">
          No templates available for this document type.
        </p>
      ) : (
        <>
          <div className="space-y-2 mb-4">
            {templates.map((template) => (
              <div
                key={template.id}
                className={`p-3 border rounded-md cursor-pointer transition-colors ${
                  selectedTemplate?.id === template.id
                    ? 'border-amspm-primary bg-amspm-light dark:bg-dark-secondary'
                    : 'border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-dark-secondary'
                }`}
                onClick={() => handleTemplateSelect(template)}
              >
                <div className="flex items-center">
                  <FaFileWord className="text-amspm-primary dark:text-dark-accent mr-2" size={18} />
                  <div className="flex-1">
                    <h4 className="font-medium text-amspm-text dark:text-dark-text">{template.name}</h4>
                    {template.description && (
                      <p className="text-sm text-gray-500 dark:text-dark-text-light">{template.description}</p>
                    )}
                  </div>
                  <a
                    href={getTemplateFileUrl(template.id)}
                    className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 ml-2"
                    title="Download Template"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <FaDownload />
                  </a>
                </div>
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default DocumentTemplateSelector;
