import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Customer } from '../types/customer';
import { User } from '../types/user';
import { searchCustomers, getAllCustomersNoPage } from '../services/customerService';
import { eventSchema, validateData } from '../utils/validation';
import { useConfirmation } from '../context/ConfirmationContext';

interface EventModalProps {
  event: {
    customer_id: number | null;
    event_type: string;
    description: string;
    scheduled_date: string;
    user_ids: number[];
    // Keep legacy field for backward compatibility
    user_id: number | null;
  };
  onClose: () => void;
  onSubmit: (e: React.FormEvent) => void;
  setEvent: (event: {
    customer_id: number | null;
    event_type: string;
    description: string;
    scheduled_date: string;
    user_ids: number[];
    user_id: number | null;
  }) => void;
  isEditing: boolean;
  submitting: boolean;
  customers: Customer[];
  users: User[];
}

const EventModal: React.FC<EventModalProps> = ({
  event,
  onClose,
  onSubmit,
  setEvent,
  isEditing,
  submitting,
  customers,
  users,
}) => {
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const EVENT_TYPES = [
    "offerte",
    "werkbon",
    "onderhoudsbon",
    "onderhoudscontract",
    "meldkamercontract",
    "beveiligingscertificaat",
    "intakedocument",
    "projectietekening",
    "beveiligingsplan",
    "kabeltekeningen",
    "checklist oplevering installatie",
    "factuur"
  ];

  const [customerSearch, setCustomerSearch] = useState('');
  const [searchResults, setSearchResults] = useState<Customer[]>([]);
  const [showCustomerDropdown, setShowCustomerDropdown] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [formModified, setFormModified] = useState(false);
  const initialEventRef = useRef({...event});
  const { showConfirmation } = useConfirmation();

  useEffect(() => {
    // Set initial customer search value if editing an event with a customer
    if (isEditing && event.customer_id && customerSearch === '') {
      const selectedCustomer = customers.find(c => c.id === event.customer_id);
      if (selectedCustomer) {
        setCustomerSearch(selectedCustomer.name);
      }
    }
  }, [isEditing, event.customer_id, customers, customerSearch]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (!target.closest('.customer-search-container') && !target.closest('.browse-customers-btn')) {
        setShowCustomerDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);



  // Load search results when the search term changes
  useEffect(() => {
    // Don't search if the term is too short
    if (customerSearch.trim().length < 2) {
      // Keep existing results if dropdown is showing
      if (!showCustomerDropdown) {
        setSearchResults([]);
      }
      return;
    }

    const fetchSearchResults = async () => {
      setIsSearching(true);
      try {
        const response = await searchCustomers(customerSearch);
        setSearchResults(response.customers);
      } catch (error) {
        console.error('Error searching customers:', error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    };

    // Debounce the search to avoid too many API calls
    const timeoutId = setTimeout(() => {
      fetchSearchResults();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [customerSearch, showCustomerDropdown, customers]);

  // Track form modifications
  useEffect(() => {
    // Compare current event with initial state
    const hasChanges = JSON.stringify(event) !== JSON.stringify(initialEventRef.current);
    setFormModified(hasChanges);
  }, [event]);

  // Function to handle modal close with confirmation if needed
  const handleClose = () => {
    if (formModified) {
      showConfirmation({
        title: "Wijzigingen negeren",
        message: "U heeft niet-opgeslagen wijzigingen. Weet u zeker dat u dit venster wilt sluiten?",
        confirmText: "Negeren",
        cancelText: "Annuleren",
        confirmButtonClass: "bg-red-600 hover:bg-red-700",
        onConfirm: () => onClose()
      });
    } else {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 p-4 flex items-center justify-center z-50"
      onClick={handleClose}
      style={{ pointerEvents: 'auto' }}
    >
      <div
        className="modal-content w-full max-w-lg max-h-[90vh] overflow-y-auto bg-white dark:bg-dark-secondary shadow-xl rounded-lg"
        onClick={(e) => e.stopPropagation()}
        style={{ pointerEvents: 'auto' }}
      >
        <div className="flex justify-between items-center mb-6 border-b border-gray-200 dark:border-gray-700 pb-3 px-6 pt-4">
          <h2 className="text-xl sm:text-2xl font-semibold text-amspm-primary uppercase">
            {isEditing ? "Gebeurtenis Bijwerken" : "Nieuwe Gebeurtenis"}
          </h2>
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleClose();
            }}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-xl transition-colors duration-200 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
            disabled={submitting}
            aria-label="Close"
            style={{ pointerEvents: 'auto', cursor: 'pointer' }}
          >
            &times;
          </button>
        </div>

        {validationErrors.length > 0 && (
          <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6 mx-6 rounded shadow-sm">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800">Corrigeer de volgende fouten:</h3>
                <ul className="mt-2 text-sm text-yellow-700 list-disc list-inside">
                  {validationErrors.map((err, index) => (
                    <li key={index}>{err}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={async (e) => {
          e.preventDefault();
          setValidationErrors([]);

          // Validate input data
          const { isValid, errors } = await validateData(eventSchema, event);

          if (!isValid) {
            setValidationErrors(errors);
            return;
          }

          // Submit the form
          onSubmit(e);
        }} className="space-y-6 px-6">
          <div className="form-group">
            <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
              Customer <span className="text-red-500">*</span>
            </label>
            <div className="relative customer-search-container">
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <div className="relative flex-grow">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <svg className="w-5 h-5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                  </div>
                  <input
                    type="text"
                    placeholder="Search customer by name..."
                    value={customerSearch}
                    onChange={(e) => {
                      setCustomerSearch(e.target.value);
                      if (e.target.value.trim() !== '') {
                        setShowCustomerDropdown(true);
                      }
                    }}
                    onFocus={() => {
                      if (customerSearch.trim() !== '') {
                        setShowCustomerDropdown(true);
                      }
                    }}
                    className="input w-full text-sm pl-10"
                    disabled={submitting}
                  />
                </div>
                <button
                  type="button"
                  className="btn btn-secondary whitespace-nowrap browse-customers-btn text-xs sm:text-sm flex items-center justify-center"
                  onClick={async () => {
                    if (!showCustomerDropdown) {
                      setIsSearching(true);
                      try {
                        const response = await getAllCustomersNoPage();
                        setSearchResults(response.customers);
                      } catch (error) {
                        console.error('Error fetching all customers:', error);
                      } finally {
                        setIsSearching(false);
                      }
                    }
                    setShowCustomerDropdown(!showCustomerDropdown);
                  }}
                  disabled={submitting}
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                  Browse Customers
                </button>
              </div>
              {showCustomerDropdown && searchResults.length > 0 && (
                <div className="absolute z-10 mt-1 w-full bg-white dark:bg-dark-secondary border border-gray-300 dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-auto">
                  {isSearching ? (
                    <div className="p-4 text-center text-gray-500 dark:text-gray-400 flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-amspm-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Searching...
                    </div>
                  ) : (
                    <>
                      {searchResults.map((customer) => (
                        <div
                          key={customer.id}
                          className={`px-4 py-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150 ${event.customer_id === customer.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}
                          onClick={() => {
                            setEvent({
                              ...event,
                              customer_id: customer.id
                            });
                            setCustomerSearch(customer.name);
                            setShowCustomerDropdown(false);
                          }}
                        >
                          <div className="font-medium text-amspm-text dark:text-dark-text">{customer.name}</div>
                          {customer.address && (
                            <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">{customer.address}</div>
                          )}
                        </div>
                      ))}

                      {searchResults.length === 0 && customerSearch.trim().length >= 2 && (
                        <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                          No customers found matching "{customerSearch}"
                        </div>
                      )}
                    </>
                  )}
                </div>
              )}
              {showCustomerDropdown && customerSearch.trim().length >= 2 && searchResults.length === 0 && !isSearching && (
                <div className="absolute z-10 mt-1 w-full bg-white dark:bg-dark-secondary border border-gray-300 dark:border-gray-700 rounded-md shadow-lg p-4 text-center text-gray-500 dark:text-gray-400">
                  No customers found
                </div>
              )}
            </div>
            {event.customer_id && (
              <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 rounded-md">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-medium text-blue-800 dark:text-blue-300 text-sm uppercase">Selected Customer:</p>
                    <p className="text-gray-800 dark:text-gray-200 font-medium mt-1">{customers.find(c => c.id === event.customer_id)?.name || 'Unknown'}</p>
                    {customers.find(c => c.id === event.customer_id)?.address && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{customers.find(c => c.id === event.customer_id)?.address}</p>
                    )}
                  </div>
                  <button
                    type="button"
                    className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 transition-colors duration-150 flex items-center"
                    onClick={() => {
                      setEvent({
                        ...event,
                        customer_id: null
                      });
                      setCustomerSearch('');
                    }}
                  >
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Clear
                  </button>
                </div>
              </div>
            )}
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Search for a customer by name or browse all customers.
            </p>
          </div>
          <div className="form-group">
            <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
              Event Type <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <select
                value={event.event_type}
                onChange={(e) => {
                  setEvent({ ...event, event_type: e.target.value });
                }}
                className="input pr-10 appearance-none bg-white dark:bg-dark-input"
                required
                disabled={submitting}
              >
                <option value="">Select Event Type</option>
                {EVENT_TYPES.map((type) => (
                  <option key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1).replace(/_/g, ' ')}
                  </option>
                ))}
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
                <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                  <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                </svg>
              </div>
            </div>
          </div>
          <div className="form-group">
            <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
              Description <span className="text-red-500">*</span>
            </label>
            <textarea
              value={event.description}
              onChange={(e) => setEvent({ ...event, description: e.target.value })}
              className="input min-h-[100px] resize-y"
              placeholder="Enter a detailed description of the event..."
              required
              disabled={submitting}
            />
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Provide clear instructions and details about this event.
            </p>
          </div>
          <div className="form-group">
            <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
              Scheduled Date <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg className="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd"></path>
                </svg>
              </div>
              <input
                type="datetime-local"
                value={event.scheduled_date}
                onChange={(e) => setEvent({ ...event, scheduled_date: e.target.value })}
                className="input pl-10"
                required
                disabled={submitting}
              />
            </div>
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Select the date and time when this event is scheduled to occur.
            </p>
          </div>
          <div className="form-group">
            <label className="block text-amspm-primary dark:text-dark-accent font-medium mb-2 uppercase text-sm">
              Assign Users <span className="text-red-500">*</span>
            </label>
            <div className="space-y-2">
              {users.map((user) => {
                const isSelected = event.user_ids.includes(user.id);

                return (
                  <div key={user.id} className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id={`user-${user.id}`}
                      checked={isSelected}
                      onChange={(e) => {
                        const newUserIds = e.target.checked
                          ? [...event.user_ids, user.id]
                          : event.user_ids.filter(id => id !== user.id);

                        setEvent({
                          ...event,
                          user_ids: newUserIds,
                          user_id: newUserIds.length > 0 ? newUserIds[0] : null // Keep legacy field updated
                        });
                      }}
                      className="h-4 w-4 text-amspm-primary focus:ring-amspm-primary border-gray-300 rounded"
                      disabled={submitting}
                    />
                    <label
                      htmlFor={`user-${user.id}`}
                      className={`flex-1 text-sm text-gray-700 dark:text-gray-300 ${submitting ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                    >
                      {user.name ? `${user.name}` : user.email} ({user.role})
                    </label>
                  </div>
                );
              })}
            </div>

            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Select one or more users who will be responsible for this event. Users without permission for the selected event type will be disabled.
            </p>
          </div>
          <div className="mt-8 pt-5 border-t border-gray-200 dark:border-gray-700 px-6 pb-6">
            <div className="flex flex-col-reverse sm:flex-row sm:justify-between sm:space-x-4 space-y-3 space-y-reverse sm:space-y-0">
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleClose();
                }}
                className="btn btn-outline w-full sm:w-auto flex items-center justify-center"
                disabled={submitting}
                style={{ pointerEvents: 'auto', cursor: 'pointer' }}
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                Annuleren
              </button>
              <button
                type="submit"
                className="btn btn-secondary w-full sm:w-auto flex items-center justify-center"
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Bezig...
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    {isEditing ? "Bijwerken" : "Aanmaken"}
                  </>
                )}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EventModal;


