import React, { useState, useRef } from 'react';
import { exportCustomersToCSV, exportCustomersToJSON, exportCustomersToExcel, importCustomers } from '../services/exportService';
import { FaFileExport, FaFileImport, FaFileCsv, FaFileCode, FaUpload, FaSpinner, FaFileExcel } from 'react-icons/fa';

const ExportImportCustomers: React.FC = () => {
  const [importing, setImporting] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [importResult, setImportResult] = useState<{ message: string; imported: number; failed: number } | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleExportCSV = async () => {
    try {
      setExporting(true);
      setError(null);

      const blob = await exportCustomersToCSV();

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `customers_export_${new Date().toISOString().slice(0, 10)}.csv`;
      document.body.appendChild(a);
      a.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Failed to export customers to CSV:', err);
      setError('Failed to export customers. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  const handleExportJSON = async () => {
    try {
      setExporting(true);
      setError(null);

      const blob = await exportCustomersToJSON();

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `customers_export_${new Date().toISOString().slice(0, 10)}.json`;
      document.body.appendChild(a);
      a.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Failed to export customers to JSON:', err);
      setError('Failed to export customers. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  const handleExportExcel = async () => {
    try {
      setExporting(true);
      setError(null);

      const blob = await exportCustomersToExcel();

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `customers_export_${new Date().toISOString().slice(0, 10)}.xlsx`;
      document.body.appendChild(a);
      a.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Failed to export customers to Excel:', err);
      setError('Failed to export customers. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  const handleImportClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file extension
    const fileExt = file.name.split('.').pop()?.toLowerCase();
    if (fileExt !== 'csv' && fileExt !== 'json' && fileExt !== 'xlsx' && fileExt !== 'xls') {
      setError('Please upload a CSV, JSON, or Excel file (.xlsx, .xls).');
      return;
    }

    try {
      setImporting(true);
      setError(null);
      setImportResult(null);

      const result = await importCustomers(file);
      setImportResult(result);
    } catch (err: any) {
      console.error('Failed to import customers:', err);
      setError(err.response?.data?.error || 'Failed to import customers. Please try again.');
    } finally {
      setImporting(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <div className="bg-white dark:bg-dark-secondary rounded-lg shadow-sm p-4">
      <h2 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-4 flex items-center">
        <FaFileExport className="mr-2" /> Export/Import Customers
      </h2>

      {error && (
        <div className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-300 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {importResult && (
        <div className="bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-700 text-green-700 dark:text-green-300 px-4 py-3 rounded mb-4">
          <p>{importResult.message}</p>
          <p className="text-sm mt-1">
            Successfully imported: <span className="font-semibold">{importResult.imported}</span> |
            Failed: <span className="font-semibold">{importResult.failed}</span>
          </p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="border border-amspm-light-gray dark:border-dark-border rounded-lg p-4">
          <h3 className="text-md font-medium text-amspm-text dark:text-dark-text mb-3 flex items-center">
            <FaFileExport className="mr-2" /> Export Customers
          </h3>
          <p className="text-sm text-amspm-text-light dark:text-dark-text-light mb-4">
            Export your customer data in CSV, JSON, or Excel format for backup or analysis.
          </p>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={handleExportCSV}
              disabled={exporting}
              className="btn btn-outline text-xs py-2 px-3 flex items-center"
            >
              {exporting ? (
                <FaSpinner className="animate-spin mr-1" />
              ) : (
                <FaFileCsv className="mr-1" />
              )}
              Export as CSV
            </button>
            <button
              onClick={handleExportJSON}
              disabled={exporting}
              className="btn btn-outline text-xs py-2 px-3 flex items-center"
            >
              {exporting ? (
                <FaSpinner className="animate-spin mr-1" />
              ) : (
                <FaFileCode className="mr-1" />
              )}
              Export as JSON
            </button>
            <button
              onClick={handleExportExcel}
              disabled={exporting}
              className="btn btn-outline text-xs py-2 px-3 flex items-center"
            >
              {exporting ? (
                <FaSpinner className="animate-spin mr-1" />
              ) : (
                <FaFileExcel className="mr-1" />
              )}
              Export as Excel
            </button>
          </div>
        </div>

        <div className="border border-amspm-light-gray dark:border-dark-border rounded-lg p-4">
          <h3 className="text-md font-medium text-amspm-text dark:text-dark-text mb-3 flex items-center">
            <FaFileImport className="mr-2" /> Import Customers
          </h3>
          <p className="text-sm text-amspm-text-light dark:text-dark-text-light mb-4">
            Import customer data from CSV, JSON, or Excel files. The file should match the export format.
          </p>
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".csv,.json,.xlsx,.xls"
            className="hidden"
          />
          <button
            onClick={handleImportClick}
            disabled={importing}
            className="btn btn-outline text-xs py-2 px-3 flex items-center"
          >
            {importing ? (
              <FaSpinner className="animate-spin mr-1" />
            ) : (
              <FaUpload className="mr-1" />
            )}
            {importing ? 'Importing...' : 'Select File to Import'}
          </button>
        </div>
      </div>

      <div className="mt-4 text-xs text-amspm-text-light dark:text-dark-text-light">
        <p className="font-semibold mb-1">Notes:</p>
        <ul className="list-disc pl-5 space-y-1">
          <li>CSV exports include all customer fields in a tabular format.</li>
          <li>JSON exports include all customer data in a structured format.</li>
          <li>Excel exports provide a spreadsheet format that's easy to edit.</li>
          <li>When importing, make sure your file follows the same structure as the exports.</li>
          <li>For CSV imports, the first row should contain column headers.</li>
          <li>For JSON imports, the file should contain an array of customer objects.</li>
          <li>For Excel imports, the system supports both standard exports and AMSPM format:</li>
          <li className="ml-4">- Standard format: First row contains headers like Name, Code, etc.</li>
          <li className="ml-4">- AMSPM format: Data starts at row 10 with headers in row 9</li>
          <li className="ml-4">- Dutch headers are supported: Datum, Code, Naam, KvKnr, Contactpersoon, Adres, etc.</li>
        </ul>
      </div>
    </div>
  );
};

export default ExportImportCustomers;
