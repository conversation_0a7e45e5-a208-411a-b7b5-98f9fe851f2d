import React, { useState, useEffect, useRef } from 'react';
import { Event } from '../types/event';
import { handleEventSchema, validateData } from '../utils/validation';
import { useConfirmation } from '../context/ConfirmationContext';
import { DocumentTemplate } from '../types/document_template';
import { Customer } from '../types/customer';
import DocumentTemplateSelector from './DocumentTemplateSelector';
import TemplateFormEditor from './TemplateFormEditor';
import { getCustomerById } from '../services/customerService';
import { TemplateService } from '../services/templateService';
import LoadingSpinner from './LoadingSpinner';
import { FaUpload, FaFileAlt, FaArrowLeft, FaCheck } from 'react-icons/fa';

interface HandleEventModalProps {
  event: Event;
  onClose: () => void;
  onCompleteWithFile: (event: Event, templateFile?: File) => void;
  submitting: boolean;
  file: File | null;
  setFile: (file: File | null) => void;
  newExpiryDate: string;
  setNewExpiryDate: (date: string) => void;
  expiryType: "date" | "niet_van_toepassing";
  setExpiryType: (type: "date" | "niet_van_toepassing") => void;
  documentNotApplicable: boolean;
  setDocumentNotApplicable: (value: boolean) => void;
  useVersionStatus: boolean;
  setUseVersionStatus: (use: boolean) => void;
  versionStatus: "active" | "inactive";
  setVersionStatus: (status: "active" | "inactive") => void;
}

const HandleEventModal: React.FC<HandleEventModalProps> = ({
  event,
  onClose,
  onCompleteWithFile,
  submitting,
  file,
  setFile,
  newExpiryDate,
  setNewExpiryDate,
  expiryType,
  setExpiryType,
  documentNotApplicable,
  setDocumentNotApplicable,
  useVersionStatus,
  setUseVersionStatus,
  versionStatus,
  setVersionStatus
}) => {
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [formModified, setFormModified] = useState(false);
  const { showConfirmation } = useConfirmation();

  // Template-related state
  const [documentSource, setDocumentSource] = useState<'upload' | 'template'>('upload');
  const [selectedTemplate, setSelectedTemplate] = useState<DocumentTemplate | null>(null);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loadingCustomer, setLoadingCustomer] = useState<boolean>(false);
  const [step, setStep] = useState<'source' | 'template' | 'form' | 'final'>('source');
  const [templateFile, setTemplateFile] = useState<File | null>(null);
  const [processingTemplate, setProcessingTemplate] = useState<boolean>(false);

  const initialStateRef = useRef({
    file,
    newExpiryDate,
    expiryType,
    documentNotApplicable,
    useVersionStatus,
    versionStatus
  });
  // Load customer data when event changes
  useEffect(() => {
    const loadCustomer = async () => {
      if (event.customer_id) {
        try {
          setLoadingCustomer(true);
          const customerData = await getCustomerById(event.customer_id);
          setCustomer(customerData);
        } catch (error) {
          console.error('Error loading customer:', error);
        } finally {
          setLoadingCustomer(false);
        }
      }
    };

    loadCustomer();
  }, [event.customer_id]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFile(e.target.files[0]);
      setFormModified(true);
    }
  };

  const handleTemplateSelected = (template: DocumentTemplate) => {
    setSelectedTemplate(template);
    setStep('form');
  };

  const handleTemplateFormSave = async (blob: Blob, fileName: string) => {
    try {
      setProcessingTemplate(true);

      // Create a File object from the Blob
      const file = new File([blob], fileName, {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      });

      setTemplateFile(file);
      setStep('final');
    } catch (error) {
      console.error('Error processing template:', error);
      setValidationErrors(['Failed to process template. Please try again.']);
    } finally {
      setProcessingTemplate(false);
    }
  };

  // Track form modifications
  useEffect(() => {
    const currentState = {
      file,
      newExpiryDate,
      expiryType,
      documentNotApplicable,
      useVersionStatus,
      versionStatus,
      documentSource,
      selectedTemplate,
      templateFile
    };

    // Compare current state with initial state
    const hasChanges = JSON.stringify(currentState) !== JSON.stringify(initialStateRef.current);
    setFormModified(hasChanges);
  }, [file, newExpiryDate, expiryType, documentNotApplicable, useVersionStatus, versionStatus, documentSource, selectedTemplate, templateFile]);

  // Handle button clicks
  const handleButtonClick = async (action: string) => {
    if (action === 'complete-with-file') {
      setValidationErrors([]);

      // Determine which file to use
      const fileToUse = documentSource === 'template' ? templateFile : file;

      // Validate form data
      const { isValid, errors } = await validateData(handleEventSchema, {
        file: fileToUse,
        expiryType,
        newExpiryDate,
        documentNotApplicable,
        useVersionStatus,
        versionStatus
      });

      if (!isValid) {
        setValidationErrors(errors);
        return;
      }

      // If validation passes, proceed with the action
      onCompleteWithFile(event, fileToUse || undefined);
    } else if (action === 'close') {
      if (formModified) {
        showConfirmation({
          title: "Wijzigingen negeren",
          message: "U heeft niet-opgeslagen wijzigingen. Weet u zeker dat u dit venster wilt sluiten?",
          confirmText: "Negeren",
          cancelText: "Annuleren",
          confirmButtonClass: "bg-red-600 hover:bg-red-700",
          onConfirm: () => onClose()
        });
      } else {
        onClose();
      }
    }
  };

  const handleBackToSource = () => {
    setStep('source');
    setSelectedTemplate(null);
    setTemplateFile(null);
  };

  const handleBackToTemplate = () => {
    setStep('template');
    setTemplateFile(null);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style={{pointerEvents: 'auto'}}>
      {/* Dark overlay */}
      <div
        className="absolute inset-0"
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          handleButtonClick('close');
        }}
        style={{pointerEvents: 'auto', cursor: 'pointer'}}
      ></div>

      {/* Modal content */}
      <div
        className="bg-white dark:bg-dark-secondary rounded-lg shadow-lg p-6 w-full max-w-2xl relative z-50 max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
        style={{pointerEvents: 'auto'}}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-xl font-medium text-amspm-text">Gebeurtenis Afhandelen</h3>
            <div className="text-sm text-gray-500 mt-1">
              {event.event_type} - ID: {event.id}
            </div>
          </div>

          {/* Progress indicator */}
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${step === 'source' ? 'bg-amspm-primary' : 'bg-gray-300'}`}></div>
            <div className={`w-3 h-3 rounded-full ${step === 'template' ? 'bg-amspm-primary' : 'bg-gray-300'}`}></div>
            <div className={`w-3 h-3 rounded-full ${step === 'form' ? 'bg-amspm-primary' : 'bg-gray-300'}`}></div>
            <div className={`w-3 h-3 rounded-full ${step === 'final' ? 'bg-amspm-primary' : 'bg-gray-300'}`}></div>
          </div>
        </div>

        {validationErrors.length > 0 && (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
            <ul className="list-disc pl-5">
              {validationErrors.map((err, index) => (
                <li key={index}>{err}</li>
              ))}
            </ul>
          </div>
        )}

        {loadingCustomer && (
          <div className="flex justify-center py-4">
            <LoadingSpinner />
          </div>
        )}

        {/* Step-based content */}
        {step === 'source' && (
          <div className="space-y-6">
            <div className="text-center">
              <h4 className="text-lg font-medium text-amspm-text mb-4">Kies Document Bron</h4>
              <p className="text-gray-600 mb-6">Hoe wilt u het document voor deze gebeurtenis aanleveren?</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Upload File Option */}
              <div
                className={`p-6 border-2 rounded-lg cursor-pointer transition-all ${
                  documentSource === 'upload'
                    ? 'border-amspm-primary bg-amspm-light'
                    : 'border-gray-300 hover:border-amspm-primary hover:bg-gray-50'
                }`}
                onClick={() => setDocumentSource('upload')}
              >
                <div className="text-center">
                  <FaUpload className="mx-auto text-3xl text-amspm-primary mb-3" />
                  <h5 className="font-medium text-amspm-text mb-2">Bestand Uploaden</h5>
                  <p className="text-sm text-gray-600">Upload een bestaand document van uw computer</p>
                </div>
              </div>

              {/* Template Option */}
              <div
                className={`p-6 border-2 rounded-lg cursor-pointer transition-all ${
                  documentSource === 'template'
                    ? 'border-amspm-primary bg-amspm-light'
                    : 'border-gray-300 hover:border-amspm-primary hover:bg-gray-50'
                }`}
                onClick={() => setDocumentSource('template')}
              >
                <div className="text-center">
                  <FaFileAlt className="mx-auto text-3xl text-amspm-primary mb-3" />
                  <h5 className="font-medium text-amspm-text mb-2">Template Gebruiken</h5>
                  <p className="text-sm text-gray-600">Kies een template en vul deze in</p>
                </div>
              </div>
            </div>

            {/* Document Not Applicable Option */}
            <div className="border-t pt-4">
              <label className="inline-flex items-center">
                <input
                  type="checkbox"
                  checked={documentNotApplicable}
                  onChange={(e) => {
                    setDocumentNotApplicable(e.target.checked);
                    if (e.target.checked) {
                      setFile(null);
                      setTemplateFile(null);
                    }
                  }}
                  disabled={submitting}
                  className="form-checkbox"
                />
                <span className="ml-2 text-amspm-text">Document niet van toepassing</span>
              </label>
            </div>
          </div>
        )}

        {step === 'template' && documentSource === 'template' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-lg font-medium text-amspm-text">Selecteer Template</h4>
              <button
                onClick={handleBackToSource}
                className="btn btn-outline btn-sm flex items-center"
                disabled={submitting}
              >
                <FaArrowLeft className="mr-2" /> Terug
              </button>
            </div>

            <DocumentTemplateSelector
              documentType={event.event_type}
              onTemplateSelected={handleTemplateSelected}
            />
          </div>
        )}

        {step === 'form' && selectedTemplate && customer && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-lg font-medium text-amspm-text">Template Invullen</h4>
              <button
                onClick={() => setStep('template')}
                className="btn btn-outline btn-sm flex items-center"
                disabled={submitting || processingTemplate}
              >
                <FaArrowLeft className="mr-2" /> Terug
              </button>
            </div>

            {processingTemplate && (
              <div className="flex justify-center py-4">
                <LoadingSpinner />
              </div>
            )}

            <TemplateFormEditor
              template={selectedTemplate}
              customer={customer}
              onSave={handleTemplateFormSave}
              onCancel={() => setStep('template')}
              disabled={processingTemplate}
            />
          </div>
        )}

        {(step === 'final' || (step === 'source' && documentSource === 'upload')) && (
          <div className="space-y-4">
            {step === 'final' && (
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-medium text-amspm-text">Voltooien</h4>
                <button
                  onClick={handleBackToTemplate}
                  className="btn btn-outline btn-sm flex items-center"
                  disabled={submitting}
                >
                  <FaArrowLeft className="mr-2" /> Terug
                </button>
              </div>
            )}

            {documentSource === 'upload' && (
              <div className="form-group">
                <label className="block text-amspm-text font-medium mb-1 uppercase">
                  Nieuw Document Uploaden ({event.event_type})
                </label>
                <input
                  type="file"
                  accept="image/*,.pdf,.doc,.docx"
                  onChange={handleFileChange}
                  className="input"
                  disabled={submitting || documentNotApplicable}
                />
              </div>
            )}

            {documentSource === 'template' && templateFile && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center">
                  <FaCheck className="text-green-600 mr-2" />
                  <span className="text-green-800 font-medium">Template succesvol ingevuld</span>
                </div>
                <p className="text-green-700 text-sm mt-1">
                  Document: {templateFile.name}
                </p>
              </div>
            )}
            {!documentNotApplicable && (
              <>
                <div className="form-group">
                  <label className="block text-amspm-text font-medium mb-1 uppercase">Vervaldatum Type</label>
                  <select
                    value={expiryType}
                    onChange={(e) => {
                      setExpiryType(e.target.value as "date" | "niet_van_toepassing");
                      if (e.target.value === "niet_van_toepassing") {
                        setNewExpiryDate("");
                      }
                    }}
                    className="input"
                    disabled={submitting}
                  >
                    <option value="date">Datum</option>
                    <option value="niet_van_toepassing">Niet van toepassing</option>
                  </select>
                </div>

                {expiryType === "date" && (
                  <div className="form-group">
                    <label className="block text-amspm-text font-medium mb-1 uppercase">Vervaldatum</label>
                    <input
                      type="datetime-local"
                      value={newExpiryDate}
                      onChange={(e) => setNewExpiryDate(e.target.value)}
                      className="input"
                      disabled={submitting}
                    />
                  </div>
                )}

                <div className="form-group">
                  <div className="mb-2">
                    <label className="inline-flex items-center">
                      <input
                        type="checkbox"
                        checked={useVersionStatus}
                        onChange={(e) => setUseVersionStatus(e.target.checked)}
                        disabled={submitting}
                        className="form-checkbox"
                      />
                      <span className="ml-2 text-amspm-text font-medium uppercase">Use Version Status</span>
                    </label>
                  </div>
                  {useVersionStatus && (
                    <select
                      value={versionStatus}
                      onChange={(e) => setVersionStatus(e.target.value as "active" | "inactive")}
                      className="input"
                      disabled={submitting}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  )}
                </div>
              </>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col space-y-3 mt-6 pt-6 border-t">
          {step === 'source' && (
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => {
                  if (documentSource === 'template') {
                    setStep('template');
                  } else {
                    handleButtonClick('complete-with-file');
                  }
                }}
                className="btn btn-secondary flex-1"
                disabled={submitting || (!documentNotApplicable && documentSource === 'upload' && !file)}
              >
                {documentSource === 'template' ? 'Volgende' : 'Voltooien'}
              </button>
            </div>
          )}

          {(step === 'final' || (step === 'source' && documentSource === 'upload')) && (
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => handleButtonClick('complete-with-file')}
                className="btn btn-secondary flex-1"
                disabled={submitting || (!documentNotApplicable && !file && !templateFile)}
              >
                {submitting
                  ? "Bezig..."
                  : documentNotApplicable && expiryType === "niet_van_toepassing"
                  ? "Gebeurtenis Voltooien"
                  : "Voltooien"}
              </button>
            </div>
          )}

          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleButtonClick('close');
            }}
            className="btn btn-danger w-full"
            disabled={submitting}
          >
            Annuleren
          </button>
        </div>
      </div>
    </div>
  );
};

export default HandleEventModal;
