import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import { useTheme } from "../context/ThemeContext";
import Sidebar from "./Sidebar";
import { FaBars, FaMoon, FaSun, FaChevronRight, FaChevronLeft } from "react-icons/fa";

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { theme, toggleTheme } = useTheme();
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(window.innerWidth >= 1024); // Open by default on large screens
  const [isMobile, setIsMobile] = useState(window.innerWidth < 1024);
  const [initialLoad, setInitialLoad] = useState(true);

  useEffect(() => {
    // Set initial state after component mounts to avoid hydration issues
    if (initialLoad) {
      setInitialLoad(false);
      const isNowMobile = window.innerWidth < 1024;
      setSidebarOpen(!isNowMobile); // Open by default on desktop, closed on mobile
      setIsMobile(isNowMobile);
    }

    const handleResize = () => {
      const isNowMobile = window.innerWidth < 1024;
      setIsMobile(isNowMobile);

      // Only auto-close sidebar when switching from desktop to mobile
      if (isNowMobile && !isMobile && sidebarOpen) {
        setSidebarOpen(false);
      }
      // Only auto-open sidebar when switching from mobile to desktop
      else if (!isNowMobile && isMobile && !sidebarOpen) {
        setSidebarOpen(true);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [sidebarOpen, initialLoad, isMobile]);

  const handleLogout = async () => {
    try {
      await logout();
      navigate("/login");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  if (!user) return null;

  return (
    <div className="flex h-screen bg-amspm-background dark:bg-dark-primary overflow-hidden">
      {/* Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        toggleSidebar={toggleSidebar}
        onLogout={handleLogout}
      />

      {/* Main content */}
      <div
        className={`flex-1 flex flex-col transition-all duration-300 w-full ${
          sidebarOpen ? "lg:ml-64" : "lg:ml-20"
        }`}
      >
        {/* Header */}
        <header className="bg-white dark:bg-dark-secondary shadow-sm h-14 sm:h-16 flex items-center justify-between px-3 sm:px-4 sticky top-0 z-10 transition-colors duration-200">
          <div className="flex items-center">
            {/* Mobile sidebar toggle */}
            <button
              onClick={toggleSidebar}
              className="mr-2 sm:mr-3 lg:hidden p-2 rounded-full hover:bg-amspm-light-gray dark:hover:bg-gray-700 transition-colors duration-200"
              aria-label="Toggle sidebar"
            >
              <FaBars className="text-amspm-text dark:text-dark-text" size={18} />
            </button>
            <h1 className="text-lg sm:text-xl font-semibold text-amspm-text dark:text-dark-text truncate">
              Customer Management
            </h1>
          </div>
          <div className="flex items-center">
            <button
              onClick={toggleTheme}
              className="p-2 rounded-full hover:bg-amspm-light-gray dark:hover:bg-gray-700 transition-colors duration-200"
              aria-label="Toggle theme"
            >
              {theme === "dark" ? (
                <FaSun className="text-yellow-400" size={18} />
              ) : (
                <FaMoon className="text-amspm-text" size={18} />
              )}
            </button>
          </div>
        </header>

        {/* Main content */}
        <main className="flex-1 overflow-auto p-2 sm:p-3 md:p-4 lg:p-6 transition-colors duration-200">
          <div className="container mx-auto px-0 sm:px-2 md:px-4">
            {children}
          </div>
        </main>

        {/* Footer */}
        <footer className="bg-white dark:bg-dark-secondary border-t border-amspm-light-gray dark:border-dark-border py-2 sm:py-3 px-3 sm:px-4 transition-colors duration-200">
          <div className="container mx-auto flex flex-col sm:flex-row justify-between items-center space-y-1 sm:space-y-0">
            <p className="text-xs text-amspm-text-light dark:text-dark-text-light">
              &copy; {new Date().getFullYear()} AMSPM Customer Management
            </p>
            <p className="text-xs text-amspm-text-light dark:text-dark-text-light">
              Version 1.0.0
            </p>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default Layout;
