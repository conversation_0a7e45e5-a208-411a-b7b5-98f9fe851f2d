import React from 'react';

export interface LoadingSpinnerProps {
  message?: string;
  fullScreen?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  message = "Loading...",
  fullScreen = true,
  size = 'md',
  className = ''
}) => {
  // Determine spinner size based on prop
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-4 w-4 border-b-1';
      case 'lg':
        return 'h-16 w-16 border-b-3';
      case 'md':
      default:
        return 'h-12 w-12 border-b-2';
    }
  };

  // Determine text size based on spinner size
  const getTextSize = () => {
    switch (size) {
      case 'sm':
        return 'text-xs mt-2';
      case 'lg':
        return 'text-lg mt-4';
      case 'md':
      default:
        return 'mt-4';
    }
  };

  const spinnerContent = (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div className={`animate-spin rounded-full ${getSizeClasses()} border-amspm-primary`}></div>
      {message && (
        <p className={`${getTextSize()} text-amspm-text dark:text-dark-text font-medium`}>{message}</p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white dark:bg-dark-primary bg-opacity-90 dark:bg-opacity-90 flex items-center justify-center z-50">
        {spinnerContent}
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center p-4">
      {spinnerContent}
    </div>
  );
};

export default LoadingSpinner;