import React from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";

interface NavbarProps {
  theme: string;
  toggleTheme: () => void;
}

const Navbar: React.FC<NavbarProps> = ({ theme, toggleTheme }) => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await logout();
      navigate("/login");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  if (!user) return null;

  return (
    <nav className="navbar">
      <ul className="nav-list">
        {/* Show user dashboard link to all users */}
        <li>
          <Link to="/user-dashboard">User Dashboard</Link>
        </li>

        {/* Administrator-specific links */}
        {user.role === "administrator" && (
          <>
            <li>
              <Link to="/dashboard">Admin Dashboard</Link>
            </li>
            <li>
              <Link to="/users">Users</Link>
            </li>
            <li>
              <Link to="/customers">Customers</Link>
            </li>
            <li>
              <Link to="/events">Events</Link>
            </li>
          </>
        )}

        {/* User role-specific content */}
        {user.role === "monteur" && (
          <li>
            <Link to="/user-dashboard">My Events</Link>
          </li>
        )}

        {user.role === "verkoper" && (
          <li>
            <Link to="/user-dashboard">My Dashboard</Link>
          </li>
        )}

        <li>
          <button onClick={handleLogout}>Logout</button>
        </li>
      </ul>
    </nav>
  );
};

export default Navbar;
