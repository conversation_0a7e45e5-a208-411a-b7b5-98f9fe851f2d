import React from 'react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  totalItems: number;
  itemsPerPage: number;
  onItemsPerPageChange: (perPage: number) => void;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  totalItems,
  itemsPerPage,
  onItemsPerPageChange,
}) => {
  const itemsPerPageOptions = [10, 20, 50, 100];

  return (
    <div className="flex flex-col sm:flex-row justify-between items-center mt-4 gap-2 sm:gap-4">
      <div className="flex flex-col sm:flex-row items-center gap-2 w-full sm:w-auto">
        <span className="text-xs sm:text-sm text-gray-700 text-center sm:text-left">
          Showing {Math.min((currentPage - 1) * itemsPerPage + 1, totalItems)} to{' '}
          {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} entries
        </span>
        <select
          value={itemsPerPage}
          onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
          className="border rounded px-2 py-1 text-xs sm:text-sm w-full sm:w-auto"
        >
          {itemsPerPageOptions.map((option) => (
            <option key={option} value={option}>
              {option} per page
            </option>
          ))}
        </select>
      </div>
      <div className="flex flex-wrap justify-center gap-1 sm:gap-2 mt-2 sm:mt-0">
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="px-2 sm:px-3 py-1 rounded border text-xs sm:text-sm disabled:opacity-50"
        >
          Prev
        </button>
        {Array.from({ length: totalPages }, (_, i) => i + 1)
          // Show only first, last, current and adjacent pages on mobile
          .filter(page => {
            if (totalPages <= 5) return true;
            if (page === 1 || page === totalPages) return true;
            if (Math.abs(page - currentPage) <= 1) return true;
            return false;
          })
          .map((page, index, array) => {
            // Add ellipsis
            const showEllipsisBefore = index > 0 && array[index - 1] !== page - 1;
            const showEllipsisAfter = index < array.length - 1 && array[index + 1] !== page + 1;

            return (
              <React.Fragment key={page}>
                {showEllipsisBefore && <span className="px-1 py-1 text-gray-500">...</span>}
                <button
                  onClick={() => onPageChange(page)}
                  className={`px-2 sm:px-3 py-1 rounded border text-xs sm:text-sm ${
                    currentPage === page ? 'bg-amspm-primary text-white' : ''
                  }`}
                >
                  {page}
                </button>
                {showEllipsisAfter && <span className="px-1 py-1 text-gray-500">...</span>}
              </React.Fragment>
            );
          })}
        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="px-2 sm:px-3 py-1 rounded border text-xs sm:text-sm disabled:opacity-50"
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default Pagination;