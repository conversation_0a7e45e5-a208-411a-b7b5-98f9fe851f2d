import React, { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import { useTheme } from "../context/ThemeContext";
import {
  FaHome,
  FaUsers,
  FaCalendarAlt,
  FaSignOutAlt,
  FaUserCog,
  FaChevronRight,
  FaChevronDown,
  FaChevronLeft,
  FaHistory,
  FaFileWord,
  FaBoxOpen,
  FaFileInvoiceDollar,
  FaClock,
  FaUserClock,
  FaUser
} from "react-icons/fa";

interface SidebarProps {
  isOpen: boolean;
  toggleSidebar: () => void;
  onLogout: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, toggleSidebar, onLogout }) => {
  const { user } = useAuth();
  useTheme(); // Initialize theme context
  const location = useLocation();
  const [expandedMenus, setExpandedMenus] = useState<Record<string, boolean>>({});

  if (!user) return null;

  const toggleMenu = (menu: string) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menu]: !prev[menu]
    }));
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const menuItemClass = (path: string) => {
    return `flex items-center py-3 px-4 w-full rounded-lg transition-colors duration-200 min-h-[44px] touch-manipulation ${
      isActive(path)
        ? "bg-amspm-primary text-amspm-secondary font-medium"
        : "text-amspm-text dark:text-dark-text hover:bg-amspm-light-gray dark:hover:bg-gray-700"
    }`;
  };

  const subMenuItemClass = (path: string) => {
    return `flex items-center py-2 px-4 pl-10 w-full rounded-lg transition-colors duration-200 min-h-[40px] touch-manipulation ${
      isActive(path)
        ? "bg-amspm-primary bg-opacity-80 text-amspm-secondary font-medium"
        : "text-amspm-text dark:text-dark-text hover:bg-amspm-light-gray dark:hover:bg-gray-700"
    }`;
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={toggleSidebar}
        ></div>
      )}

      {/* Sidebar */}
      <div
        className={`fixed top-0 left-0 h-full bg-white dark:bg-dark-secondary shadow-lg z-50 transition-all duration-300 ease-in-out border-r border-amspm-light-gray dark:border-dark-border flex flex-col ${
          isOpen ? "w-[85vw] sm:w-[320px] md:w-64" : "w-0 lg:w-16 xl:w-20"
        } ${isOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"} overflow-hidden touch-manipulation`}
      >
        {/* Logo and toggle */}
        <div className="flex items-center h-14 sm:h-16 px-2 sm:px-4 border-b border-amspm-light-gray dark:border-dark-border transition-colors duration-200">
          {/* Toggle button fixed to the left */}
          <button
            onClick={toggleSidebar}
            className="flex-shrink-0 focus:outline-none focus:ring-2 focus:ring-amspm-primary rounded-md transition-colors duration-200 hover:bg-amspm-light-gray dark:hover:bg-gray-700 p-2 mr-2 min-h-[40px] min-w-[40px] touch-manipulation"
            aria-label="Toggle sidebar"
          >
            {isOpen ? (
              <FaChevronLeft className="text-amspm-primary" size={18} />
            ) : (
              <FaChevronRight className="text-amspm-primary" size={18} />
            )}
          </button>

          {/* Logo */}
          <div className="flex items-center">
            {isOpen && (
              <span className="ml-2 font-bold text-amspm-primary text-lg sm:text-xl">AMSPM</span>
            )}
          </div>
        </div>

        {/* User info */}
        <div className={`px-2 sm:px-4 py-2 sm:py-4 border-b border-amspm-light-gray dark:border-dark-border transition-colors duration-200 ${!isOpen && "hidden lg:block lg:px-1 xl:px-2"}`}>
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full bg-amspm-primary text-amspm-secondary flex items-center justify-center font-bold text-lg">
              {user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
            </div>
            {isOpen && (
              <div className="ml-3">
                <p className="font-medium text-amspm-text dark:text-dark-text truncate max-w-[160px]">{user.name || user.email}</p>
                <p className="text-xs text-amspm-text-light dark:text-dark-text-light capitalize">{user.role}</p>
                {!user.name && <p className="text-xs text-amspm-text-light dark:text-dark-text-light italic">Set your name in Users</p>}
              </div>
            )}
          </div>
        </div>

        {/* Navigation - with overflow scrolling */}
        <nav className="mt-2 sm:mt-4 px-1 sm:px-2 flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-amspm-light-gray scrollbar-track-transparent hover:scrollbar-thumb-amspm-primary dark:scrollbar-thumb-gray-700 dark:hover:scrollbar-thumb-gray-600">
          <ul className="space-y-2 pb-4">
            {/* Common links for all users */}
            <li>
              <Link to="/user-dashboard" className={menuItemClass("/user-dashboard")}>
                <FaHome className={`${isOpen ? "mr-2 sm:mr-3" : "mx-auto"}`} size={18} />
                {isOpen && <span className="text-sm sm:text-base">Dashboard</span>}
              </Link>
            </li>

            <li>
              <Link to="/calendar" className={menuItemClass("/calendar")}>
                <FaCalendarAlt className={`${isOpen ? "mr-2 sm:mr-3" : "mx-auto"}`} size={18} />
                {isOpen && <span className="text-sm sm:text-base">Calendar</span>}
              </Link>
            </li>

            <li>
              <Link to="/time-tracking" className={menuItemClass("/time-tracking")}>
                <FaClock className={`${isOpen ? "mr-2 sm:mr-3" : "mx-auto"}`} size={18} />
                {isOpen && <span className="text-sm sm:text-base">Uren registratie</span>}
              </Link>
            </li>

            {/* Administrator-specific links */}
            {user.role === "administrator" && (
              <>
                <li>
                  <Link to="/dashboard" className={menuItemClass("/dashboard")}>
                    <FaUserCog className={`${isOpen ? "mr-2 sm:mr-3" : "mx-auto"}`} size={18} />
                    {isOpen && <span className="text-sm sm:text-base">Admin Dashboard</span>}
                  </Link>
                </li>

                <li>
                  {isOpen ? (
                    <button
                      onClick={() => toggleMenu("management")}
                      className={`flex items-center justify-between py-2 sm:py-3 px-3 sm:px-4 w-full rounded-lg transition-colors duration-200 text-amspm-text dark:text-dark-text hover:bg-amspm-light-gray dark:hover:bg-gray-700`}
                    >
                      <div className="flex items-center">
                        <FaUsers className="mr-2 sm:mr-3" size={18} />
                        <span className="text-sm sm:text-base">Management</span>
                      </div>
                      {expandedMenus["management"] ? <FaChevronDown size={14} /> : <FaChevronRight size={14} />}
                    </button>
                  ) : (
                    <button
                      onClick={() => toggleMenu("management")}
                      className={`flex items-center justify-center py-3 px-4 w-full rounded-lg transition-colors duration-200 text-amspm-text dark:text-dark-text hover:bg-amspm-light-gray dark:hover:bg-gray-700`}
                    >
                      <FaUsers size={18} />
                    </button>
                  )}

                  {(isOpen && expandedMenus["management"]) && (
                    <ul className="mt-1 space-y-1">
                      <li>
                        <Link to="/users" className={subMenuItemClass("/users")}>
                          <span>Users</span>
                        </Link>
                      </li>
                      <li>
                        <Link to="/customers" className={subMenuItemClass("/customers")}>
                          <span>Customers</span>
                        </Link>
                      </li>
                    </ul>
                  )}
                </li>

                <li>
                  <Link to="/events" className={menuItemClass("/events")}>
                    <FaCalendarAlt className={`${isOpen ? "mr-2 sm:mr-3" : "mx-auto"}`} size={18} />
                    {isOpen && <span className="text-sm sm:text-base">Events</span>}
                  </Link>
                </li>

                <li>
                  <Link to="/audit" className={menuItemClass("/audit")}>
                    <FaHistory className={`${isOpen ? "mr-2 sm:mr-3" : "mx-auto"}`} size={18} />
                    {isOpen && <span className="text-sm sm:text-base">Audit Logs</span>}
                  </Link>
                </li>

                <li>
                  <Link to="/document-templates" className={menuItemClass("/document-templates")}>
                    <FaFileWord className={`${isOpen ? "mr-2 sm:mr-3" : "mx-auto"}`} size={18} />
                    {isOpen && <span className="text-sm sm:text-base">Document Templates</span>}
                  </Link>
                </li>

                <li>
                  <Link to="/products" className={menuItemClass("/products")}>
                    <FaBoxOpen className={`${isOpen ? "mr-2 sm:mr-3" : "mx-auto"}`} size={18} />
                    {isOpen && <span className="text-sm sm:text-base">Products</span>}
                  </Link>
                </li>

                <li>
                  <Link to="/quotations" className={menuItemClass("/quotations")}>
                    <FaFileInvoiceDollar className={`${isOpen ? "mr-2 sm:mr-3" : "mx-auto"}`} size={18} />
                    {isOpen && <span className="text-sm sm:text-base">Quotations</span>}
                  </Link>
                </li>

                <li>
                  <Link to="/time-tracking-admin" className={menuItemClass("/time-tracking-admin")}>
                    <FaUserClock className={`${isOpen ? "mr-2 sm:mr-3" : "mx-auto"}`} size={18} />
                    {isOpen && <span className="text-sm sm:text-base">Uren beheer</span>}
                  </Link>
                </li>
              </>
            )}

            {/* Sessions link removed */}
          </ul>
        </nav>

        {/* Profile and Logout buttons - outside of scrollable area */}
        <div className="mt-auto px-1 sm:px-2 py-2 border-t border-amspm-light-gray dark:border-dark-border">
          <Link
            to="/profile"
            className={`flex items-center py-2 sm:py-3 px-3 sm:px-4 w-full rounded-lg transition-colors duration-200 mb-2 min-h-[44px] touch-manipulation ${
              isActive("/profile")
                ? "bg-amspm-primary text-amspm-secondary font-medium"
                : "text-amspm-text dark:text-dark-text hover:bg-amspm-light-gray dark:hover:bg-gray-700"
            }`}
          >
            <FaUser className={`${isOpen ? "mr-2 sm:mr-3" : "mx-auto"}`} size={18} />
            {isOpen && <span className="text-sm sm:text-base">Mijn Profiel</span>}
          </Link>

          <button
            onClick={onLogout}
            className={`flex items-center py-2 sm:py-3 px-3 sm:px-4 w-full rounded-lg transition-colors duration-200 text-red-600 hover:bg-red-50 dark:hover:bg-red-900 dark:hover:bg-opacity-20 min-h-[44px] touch-manipulation`}
          >
            <FaSignOutAlt className={`${isOpen ? "mr-2 sm:mr-3" : "mx-auto"}`} size={18} />
            {isOpen && <span className="text-sm sm:text-base">Logout</span>}
          </button>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
