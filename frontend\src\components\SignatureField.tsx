import React from 'react';
import SignaturePad from './SignaturePad';
import { FaSignature } from 'react-icons/fa';

interface SignatureFieldProps {
  name: string;
  label: string;
  value?: string;
  onChange: (name: string, value: string) => void;
  disabled?: boolean;
  required?: boolean;
  className?: string;
}

const SignatureField: React.FC<SignatureFieldProps> = ({
  name,
  label,
  value,
  onChange,
  disabled = false,
  required = false,
  className = ''
}) => {
  const handleSignatureChange = (signature: string) => {
    onChange(name, signature);
  };

  return (
    <div className={`form-control ${className}`}>
      <label className="label">
        <span className="label-text text-amspm-text dark:text-dark-text flex items-center gap-2">
          <FaSignature className="text-amspm-primary" />
          {label}
          {required && <span className="text-red-500">*</span>}
        </span>
      </label>
      
      <div className="signature-field-wrapper">
        <SignaturePad
          value={value}
          onChange={handleSignatureChange}
          disabled={disabled}
          width={350}
          height={150}
          className="w-full"
        />
      </div>
      
      {/* Validation message placeholder */}
      {required && !value && (
        <label className="label">
          <span className="label-text-alt text-red-500">
            Signature is required
          </span>
        </label>
      )}
    </div>
  );
};

export default SignatureField;
