import React, { useRef, useEffect, useState } from 'react';
import SignatureCanvas from 'react-signature-canvas';
import { FaUndo, FaCheck, FaTimes, FaEdit } from 'react-icons/fa';

interface SignaturePadProps {
  value?: string; // Base64 signature data
  onChange: (signature: string) => void;
  label?: string;
  disabled?: boolean;
  width?: number;
  height?: number;
  className?: string;
}

const SignaturePad: React.FC<SignaturePadProps> = ({
  value,
  onChange,
  label,
  disabled = false,
  width = 400,
  height = 200,
  className = ''
}) => {
  const sigCanvas = useRef<SignatureCanvas>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [hasSignature, setHasSignature] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    if (value && sigCanvas.current && !isEditing) {
      // Load existing signature
      sigCanvas.current.fromDataURL(value);
      setHasSignature(true);
    }
  }, [value, isEditing]);

  useEffect(() => {
    setHasSignature(!!value && !isEditing);
  }, [value, isEditing]);

  const handleClear = () => {
    if (sigCanvas.current) {
      sigCanvas.current.clear();
      setHasSignature(false);
      setIsDrawing(false);
      onChange('');
    }
  };

  const handleSave = () => {
    if (sigCanvas.current) {
      const signatureData = sigCanvas.current.toDataURL('image/png');
      onChange(signatureData);
      setHasSignature(true);
      setIsEditing(false);
    }
  };

  const handleCancel = () => {
    if (sigCanvas.current) {
      if (value) {
        sigCanvas.current.fromDataURL(value);
        setHasSignature(true);
      } else {
        sigCanvas.current.clear();
        setHasSignature(false);
      }
      setIsEditing(false);
      setIsDrawing(false);
    }
  };

  const handleEdit = () => {
    setIsEditing(true);
    setHasSignature(false);
  };

  const handleBegin = () => {
    setIsDrawing(true);
    if (!isEditing) {
      setIsEditing(true);
      setHasSignature(false);
    }
  };

  const handleEnd = () => {
    setIsDrawing(false);
  };

  return (
    <div className={`signature-pad-container ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-amspm-text dark:text-dark-text mb-2">
          {label}
        </label>
      )}
      
      <div className="relative">
        <div 
          className={`border-2 rounded-lg overflow-hidden ${
            disabled 
              ? 'border-gray-200 bg-gray-50' 
              : isEditing 
                ? 'border-amspm-primary bg-white' 
                : hasSignature 
                  ? 'border-green-300 bg-green-50' 
                  : 'border-gray-300 bg-white'
          }`}
          style={{ width: width + 4, height: height + 4 }}
        >
          <SignatureCanvas
            ref={sigCanvas}
            canvasProps={{
              width,
              height,
              className: 'signature-canvas',
              style: { 
                display: 'block',
                touchAction: 'none'
              }
            }}
            backgroundColor="transparent"
            penColor="#000000"
            minWidth={1}
            maxWidth={3}
            velocityFilterWeight={0.7}
            onBegin={handleBegin}
            onEnd={handleEnd}
            clearOnResize={false}
          />
          
          {/* Overlay for disabled state */}
          {disabled && (
            <div className="absolute inset-0 bg-gray-100 bg-opacity-50 flex items-center justify-center">
              <span className="text-gray-500 text-sm">Signature disabled</span>
            </div>
          )}
          
          {/* Placeholder text when no signature */}
          {!hasSignature && !isDrawing && !disabled && (
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <span className="text-gray-400 text-sm">
                {isEditing ? 'Draw your signature here' : 'Click to sign'}
              </span>
            </div>
          )}
        </div>
        
        {/* Action buttons */}
        {!disabled && (
          <div className="flex gap-2 mt-2">
            {isEditing ? (
              <>
                <button
                  type="button"
                  onClick={handleSave}
                  className="btn btn-sm bg-green-600 hover:bg-green-700 text-white border-none"
                  title="Save signature"
                >
                  <FaCheck className="w-3 h-3" />
                  Save
                </button>
                <button
                  type="button"
                  onClick={handleClear}
                  className="btn btn-sm bg-yellow-600 hover:bg-yellow-700 text-white border-none"
                  title="Clear signature"
                >
                  <FaUndo className="w-3 h-3" />
                  Clear
                </button>
                <button
                  type="button"
                  onClick={handleCancel}
                  className="btn btn-sm bg-gray-600 hover:bg-gray-700 text-white border-none"
                  title="Cancel editing"
                >
                  <FaTimes className="w-3 h-3" />
                  Cancel
                </button>
              </>
            ) : hasSignature ? (
              <button
                type="button"
                onClick={handleEdit}
                className="btn btn-sm bg-amspm-primary hover:bg-amspm-primary-dark text-white border-none"
                title="Edit signature"
              >
                <FaEdit className="w-3 h-3" />
                Edit
              </button>
            ) : (
              <button
                type="button"
                onClick={() => setIsEditing(true)}
                className="btn btn-sm bg-amspm-primary hover:bg-amspm-primary-dark text-white border-none"
                title="Add signature"
              >
                <FaEdit className="w-3 h-3" />
                Sign
              </button>
            )}
          </div>
        )}
      </div>
      
      {/* Status indicator */}
      <div className="mt-1">
        {hasSignature && !isEditing && (
          <span className="text-xs text-green-600 flex items-center gap-1">
            <FaCheck className="w-3 h-3" />
            Signature saved
          </span>
        )}
        {isEditing && (
          <span className="text-xs text-blue-600">
            Draw your signature above and click Save
          </span>
        )}
      </div>
    </div>
  );
};

export default SignaturePad;
