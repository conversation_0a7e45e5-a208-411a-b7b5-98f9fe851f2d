import React from 'react';

interface SortableTableHeaderProps {
  label: string;
  field: string;
  currentSortField: string;
  sortDirection: 'asc' | 'desc' | 'none';
  onSort: (field: string) => void;
  className?: string;
}

const SortableTableHeader: React.FC<SortableTableHeaderProps> = ({
  label,
  field,
  currentSortField,
  sortDirection,
  onSort,
  className = ''
}) => {
  // Get sort icon
  const getSortIcon = () => {
    if (currentSortField !== field || sortDirection === 'none') {
      return null;
    }
    return sortDirection === 'asc' ? '↑' : '↓';
  };

  return (
    <th
      scope="col"
      className={`px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${className}`}
      onClick={() => onSort(field)}
    >
      {label} {getSortIcon()}
    </th>
  );
};

export default SortableTableHeader;
