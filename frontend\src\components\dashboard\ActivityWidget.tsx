import React from 'react';
import { FaUser, FaBuilding, FaFileAlt, FaCalendarAlt } from 'react-icons/fa';

interface Activity {
  id: number;
  type: 'user' | 'customer' | 'document' | 'event';
  action: string;
  subject: string;
  timestamp: string;
  user: {
    id: number;
    name: string;
  };
}

interface ActivityWidgetProps {
  activities: Activity[];
  maxItems?: number;
  onViewAll?: () => void;
}

const ActivityWidget: React.FC<ActivityWidgetProps> = ({
  activities,
  maxItems = 5,
  onViewAll
}) => {
  const getActivityIcon = (type: Activity['type']) => {
    switch (type) {
      case 'user':
        return <FaUser className="text-blue-500" />;
      case 'customer':
        return <FaBuilding className="text-green-500" />;
      case 'document':
        return <FaFileAlt className="text-orange-500" />;
      case 'event':
        return <FaCalendarAlt className="text-purple-500" />;
      default:
        return <FaUser className="text-blue-500" />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.round(diffMs / 60000);
    const diffHours = Math.round(diffMs / 3600000);
    const diffDays = Math.round(diffMs / 86400000);

    if (diffMins < 60) {
      return `${diffMins} min${diffMins !== 1 ? 's' : ''} ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    } else if (diffDays < 7) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <div className="card bg-white shadow-sm hover:shadow-md transition-shadow duration-300">
      <div className="card-content">
        <h3 className="text-lg font-semibold text-amspm-text mb-4">Recent Activity</h3>
        
        {activities.length === 0 ? (
          <p className="text-gray-500 text-center py-4">No recent activity</p>
        ) : (
          <div className="space-y-4">
            {activities.slice(0, maxItems).map((activity) => (
              <div key={activity.id} className="flex items-start">
                <div className="flex-shrink-0 mr-3 mt-1">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-grow">
                  <p className="text-sm text-amspm-text">
                    <span className="font-medium">{activity.user.name}</span> {activity.action}{' '}
                    <span className="font-medium">{activity.subject}</span>
                  </p>
                  <p className="text-xs text-gray-500">{formatTimestamp(activity.timestamp)}</p>
                </div>
              </div>
            ))}
          </div>
        )}
        
        {activities.length > maxItems && onViewAll && (
          <button 
            onClick={onViewAll}
            className="text-amspm-primary hover:text-amspm-primary-dark text-sm font-medium mt-4 w-full text-center"
          >
            View All Activity
          </button>
        )}
      </div>
    </div>
  );
};

export default ActivityWidget;
