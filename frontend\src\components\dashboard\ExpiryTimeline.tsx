import React from 'react';
import { Link } from 'react-router-dom';
import { FaExclamationTriangle, FaExclamation, FaCheck } from 'react-icons/fa';
import { Document } from '../../types/document';

interface ExpiryTimelineProps {
  documents: Document[];
  maxItems?: number;
  onViewAll?: () => void;
}

const ExpiryTimeline: React.FC<ExpiryTimelineProps> = ({
  documents,
  maxItems = 5,
  onViewAll
}) => {
  // Sort documents by expiry date (closest first)
  const sortedDocuments = [...documents].sort((a, b) => {
    const dateA = a.expiry_date ? new Date(a.expiry_date).getTime() : Infinity;
    const dateB = b.expiry_date ? new Date(b.expiry_date).getTime() : Infinity;
    return dateA - dateB;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'red':
        return <FaExclamationTriangle className="text-red-500" size={16} />;
      case 'orange':
        return <FaExclamation className="text-orange-500" size={16} />;
      case 'green':
        return <FaCheck className="text-green-500" size={16} />;
      default:
        return <FaCheck className="text-green-500" size={16} />;
    }
  };

  const formatExpiryDate = (date: string | null) => {
    if (!date) return 'No expiry date';
    
    const expiryDate = new Date(date);
    const now = new Date();
    const diffTime = expiryDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return `Expired ${Math.abs(diffDays)} days ago`;
    } else if (diffDays === 0) {
      return 'Expires today';
    } else if (diffDays === 1) {
      return 'Expires tomorrow';
    } else if (diffDays < 30) {
      return `Expires in ${diffDays} days`;
    } else {
      return `Expires on ${expiryDate.toLocaleDateString()}`;
    }
  };

  return (
    <div className="card bg-white shadow-sm hover:shadow-md transition-shadow duration-300">
      <div className="card-content">
        <h3 className="text-lg font-semibold text-amspm-text mb-4">Document Expiry Timeline</h3>
        
        {sortedDocuments.length === 0 ? (
          <p className="text-gray-500 text-center py-4">No documents with expiry dates</p>
        ) : (
          <div className="space-y-4">
            {sortedDocuments.slice(0, maxItems).map((document) => (
              <div key={document.id} className="flex items-start">
                <div className="flex-shrink-0 mr-3 mt-1">
                  {getStatusIcon(document.expiry_status || 'green')}
                </div>
                <div className="flex-grow">
                  <Link
                    to={`/customers/${document.customer_id}/documents`}
                    className="text-sm font-medium text-amspm-text hover:text-amspm-primary"
                  >
                    {document.document_type} - {document.customer_name}
                  </Link>
                  <p className="text-xs text-gray-500">{document.name}</p>
                  <p className="text-xs text-gray-500">{formatExpiryDate(document.expiry_date)}</p>
                </div>
              </div>
            ))}
          </div>
        )}
        
        {documents.length > maxItems && onViewAll && (
          <button 
            onClick={onViewAll}
            className="text-amspm-primary hover:text-amspm-primary-dark text-sm font-medium mt-4 w-full text-center"
          >
            View All Expirations
          </button>
        )}
      </div>
    </div>
  );
};

export default ExpiryTimeline;
