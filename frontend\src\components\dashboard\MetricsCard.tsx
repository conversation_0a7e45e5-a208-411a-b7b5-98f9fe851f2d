import React from 'react';
import { IconType } from 'react-icons';

interface MetricsCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color?: string;
  subtext?: string | React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  onClick?: () => void;
}

const MetricsCard: React.FC<MetricsCardProps> = ({
  title,
  value,
  icon,
  color = 'text-amspm-primary',
  subtext,
  trend,
  onClick
}) => {
  return (
    <div
      className={`card bg-white shadow-sm hover:shadow-md transition-shadow duration-300 ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      <div className="card-content">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-semibold text-amspm-text">{title}</h3>
          <div className={color}>{icon}</div>
        </div>
        <p className={`text-3xl font-bold ${color}`}>{value}</p>

        {subtext && (
          typeof subtext === 'string' ? (
            <p className="text-sm text-gray-600 mt-2">{subtext}</p>
          ) : (
            <div className="text-sm text-gray-600 mt-2">{subtext}</div>
          )
        )}

        {trend && (
          <div className="flex items-center mt-2">
            <span className={`text-sm ${trend.isPositive ? 'text-green-500' : 'text-red-500'} font-medium`}>
              {trend.isPositive ? '↑' : '↓'} {trend.value}%
            </span>
            <span className="text-xs text-gray-500 ml-1">vs last month</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default MetricsCard;
