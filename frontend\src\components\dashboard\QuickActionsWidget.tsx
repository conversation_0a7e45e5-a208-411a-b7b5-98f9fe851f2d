import React from 'react';
import { Link } from 'react-router-dom';
import { IconType } from 'react-icons';

interface QuickAction {
  id: string;
  title: string;
  icon: React.ReactNode;
  path: string;
  color?: string;
}

interface QuickActionsWidgetProps {
  actions: QuickAction[];
}

const QuickActionsWidget: React.FC<QuickActionsWidgetProps> = ({ actions }) => {
  return (
    <div className="card bg-white shadow-sm hover:shadow-md transition-shadow duration-300">
      <div className="card-content">
        <h3 className="text-lg font-semibold text-amspm-text mb-4">Quick Actions</h3>
        
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
          {actions.map((action) => (
            <Link
              key={action.id}
              to={action.path}
              className="flex flex-col items-center justify-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200"
            >
              <div className={`text-2xl mb-2 ${action.color || 'text-amspm-primary'}`}>
                {action.icon}
              </div>
              <span className="text-sm font-medium text-center text-amspm-text">{action.title}</span>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default QuickActionsWidget;
