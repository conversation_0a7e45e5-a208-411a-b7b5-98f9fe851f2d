import React, { useState, useEffect } from 'react';
import { getAllCategories } from '../../services/productService';
import { FaFilter, FaTimes } from 'react-icons/fa';
import LoadingSpinner from '../LoadingSpinner';

interface CategoryFilterProps {
  selectedCategory: string | null;
  onCategoryChange: (category: string | null) => void;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({ selectedCategory, onCategoryChange }) => {
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await getAllCategories();
        setCategories(data);
      } catch (err: any) {
        console.error('Failed to fetch categories:', err);
        setError(err.response?.data?.error || 'Fout bij ophalen van categorieën');
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const handleCategorySelect = (category: string | null) => {
    onCategoryChange(category);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="btn btn-outline flex items-center"
        title="Filter op categorie"
      >
        <FaFilter className="mr-2" />
        {selectedCategory ? (
          <span className="flex items-center">
            {selectedCategory}
            <FaTimes
              className="ml-2 text-gray-500 hover:text-red-500"
              onClick={(e) => {
                e.stopPropagation();
                handleCategorySelect(null);
              }}
            />
          </span>
        ) : (
          'Filter op categorie'
        )}
      </button>

      {isOpen && (
        <div className="absolute z-10 mt-1 w-64 bg-white dark:bg-dark-card shadow-lg rounded-md border border-gray-200 dark:border-gray-700 max-h-96 overflow-auto">
          {loading ? (
            <div className="p-4 text-center">
              <LoadingSpinner size="sm" />
            </div>
          ) : error ? (
            <div className="p-4 text-red-500 dark:text-red-400 text-sm">
              {error}
            </div>
          ) : categories.length === 0 ? (
            <div className="p-4 text-gray-500 dark:text-gray-400 text-sm text-center">
              Geen categorieën gevonden
            </div>
          ) : (
            <ul>
              <li
                className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-dark-secondary cursor-pointer border-b border-gray-200 dark:border-gray-700"
                onClick={() => handleCategorySelect(null)}
              >
                <span className="font-medium text-amspm-primary dark:text-dark-accent">
                  Alle categorieën
                </span>
              </li>
              {categories.map((category) => (
                <li
                  key={category}
                  className={`px-4 py-2 hover:bg-gray-100 dark:hover:bg-dark-secondary cursor-pointer border-b border-gray-200 dark:border-gray-700 last:border-b-0 ${
                    selectedCategory === category ? 'bg-gray-100 dark:bg-dark-secondary' : ''
                  }`}
                  onClick={() => handleCategorySelect(category)}
                >
                  {category}
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
};

export default CategoryFilter;
