import React, { useState } from 'react';
import { Product } from '../../types/product';
import { createProduct, updateProduct } from '../../services/productService';
import { FaSave, FaTimes } from 'react-icons/fa';
import LoadingSpinner from '../LoadingSpinner';
import * as Yup from 'yup';

interface ProductFormProps {
  product?: Product;
  onCancel: () => void;
  onSuccess: () => void;
}

// Validation schema
const productSchema = Yup.object().shape({
  name: Yup.string().required('Naam is verplicht'),
  gross_price: Yup.number()
    .nullable()
    .min(0, 'Brutoprijs mag niet negatief zijn'),
  discount_percentage: Yup.number()
    .nullable()
    .min(0, 'Korting mag niet negatief zijn')
    .max(100, 'Korting mag niet meer dan 100% zijn'),
  net_price: Yup.number()
    .nullable()
    .min(0, 'Nettoprijs mag niet negatief zijn'),
  product_code: Yup.string().nullable(),
  description: Yup.string().nullable(),
  category: Yup.string().nullable(),
  subcategory: Yup.string().nullable(),
  info: Yup.string().nullable(),
});

const ProductForm: React.FC<ProductFormProps> = ({ product, onCancel, onSuccess }) => {
  const [formData, setFormData] = useState<Partial<Product>>(
    product || {
      name: '',
      gross_price: null,
      discount_percentage: null,
      net_price: null,
      product_code: '',
      description: '',
      category: '',
      subcategory: '',
      info: '',
    }
  );
  const [submitting, setSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    // Convert to number for number inputs
    const parsedValue = type === 'number' ? (value ? parseFloat(value) : 0) : value;

    setFormData({
      ...formData,
      [name]: parsedValue,
    });

    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: '',
      });
    }
  };

  const validateForm = async (): Promise<boolean> => {
    try {
      await productSchema.validate(formData, { abortEarly: false });
      return true;
    } catch (err) {
      if (err instanceof Yup.ValidationError) {
        const validationErrors: Record<string, string> = {};
        err.inner.forEach((error) => {
          if (error.path) {
            validationErrors[error.path] = error.message;
          }
        });
        setErrors(validationErrors);
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const isValid = await validateForm();
    if (!isValid) return;

    try {
      setSubmitting(true);

      if (product) {
        // Update existing product
        await updateProduct(product.id, formData);
      } else {
        // Create new product
        await createProduct(formData);
      }

      onSuccess();
    } catch (err: any) {
      console.error('Failed to save product:', err);

      // Handle validation errors from the server
      if (err.response?.data?.details) {
        setErrors(err.response.data.details);
      } else {
        setErrors({
          form: err.response?.data?.error || 'Fout bij opslaan van product',
        });
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
      <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text mb-4">
        {product ? 'Product bewerken' : 'Nieuw product'}
      </h2>

      <form onSubmit={handleSubmit} className="space-y-6">
        {errors.form && (
          <div className="bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 p-3 rounded-md">
            {errors.form}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="form-group">
            <label htmlFor="product_code" className="form-label">
              Productcode
            </label>
            <input
              type="text"
              id="product_code"
              name="product_code"
              value={formData.product_code || ''}
              onChange={handleChange}
              className={`input ${errors.product_code ? 'border-red-500' : ''}`}
              disabled={submitting}
            />
            {errors.product_code && (
              <p className="text-red-500 text-sm mt-1">{errors.product_code}</p>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="name" className="form-label">
              Naam <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name || ''}
              onChange={handleChange}
              className={`input ${errors.name ? 'border-red-500' : ''}`}
              required
              disabled={submitting}
            />
            {errors.name && (
              <p className="text-red-500 text-sm mt-1">{errors.name}</p>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="gross_price" className="form-label">
              Brutoprijs
            </label>
            <input
              type="number"
              id="gross_price"
              name="gross_price"
              value={formData.gross_price || ''}
              onChange={handleChange}
              className={`input ${errors.gross_price ? 'border-red-500' : ''}`}
              step="0.01"
              min="0"
              disabled={submitting}
            />
            {errors.gross_price && (
              <p className="text-red-500 text-sm mt-1">{errors.gross_price}</p>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="discount_percentage" className="form-label">
              Korting (%)
            </label>
            <input
              type="number"
              id="discount_percentage"
              name="discount_percentage"
              value={formData.discount_percentage || ''}
              onChange={handleChange}
              className={`input ${errors.discount_percentage ? 'border-red-500' : ''}`}
              step="0.01"
              min="0"
              max="100"
              disabled={submitting}
            />
            {errors.discount_percentage && (
              <p className="text-red-500 text-sm mt-1">{errors.discount_percentage}</p>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="net_price" className="form-label">
              Nettoprijs
            </label>
            <input
              type="number"
              id="net_price"
              name="net_price"
              value={formData.net_price || ''}
              onChange={handleChange}
              className={`input ${errors.net_price ? 'border-red-500' : ''}`}
              step="0.01"
              min="0"
              disabled={submitting}
            />
            {errors.net_price && (
              <p className="text-red-500 text-sm mt-1">{errors.net_price}</p>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="category" className="form-label">
              Categorie
            </label>
            <input
              type="text"
              id="category"
              name="category"
              value={formData.category || ''}
              onChange={handleChange}
              className={`input ${errors.category ? 'border-red-500' : ''}`}
              disabled={submitting}
            />
            {errors.category && (
              <p className="text-red-500 text-sm mt-1">{errors.category}</p>
            )}
          </div>

          <div className="form-group">
            <label htmlFor="subcategory" className="form-label">
              Subcategorie
            </label>
            <input
              type="text"
              id="subcategory"
              name="subcategory"
              value={formData.subcategory || ''}
              onChange={handleChange}
              className={`input ${errors.subcategory ? 'border-red-500' : ''}`}
              disabled={submitting}
            />
            {errors.subcategory && (
              <p className="text-red-500 text-sm mt-1">{errors.subcategory}</p>
            )}
          </div>

          <div className="form-group md:col-span-2">
            <label htmlFor="description" className="form-label">
              Omschrijving
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description || ''}
              onChange={handleChange}
              className={`input min-h-[100px] ${errors.description ? 'border-red-500' : ''}`}
              disabled={submitting}
            />
            {errors.description && (
              <p className="text-red-500 text-sm mt-1">{errors.description}</p>
            )}
          </div>

          <div className="form-group md:col-span-2">
            <label htmlFor="info" className="form-label">
              Extra informatie
            </label>
            <textarea
              id="info"
              name="info"
              value={formData.info || ''}
              onChange={handleChange}
              className={`input min-h-[100px] ${errors.info ? 'border-red-500' : ''}`}
              disabled={submitting}
            />
            {errors.info && (
              <p className="text-red-500 text-sm mt-1">{errors.info}</p>
            )}
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="btn btn-outline"
            disabled={submitting}
          >
            <FaTimes className="mr-2" /> Annuleren
          </button>

          <button
            type="submit"
            className="btn btn-primary"
            disabled={submitting}
          >
            {submitting ? (
              <>
                <LoadingSpinner size="sm" /> Opslaan...
              </>
            ) : (
              <>
                <FaSave className="mr-2" /> Opslaan
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProductForm;
