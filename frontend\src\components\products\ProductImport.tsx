import React, { useState, useRef } from 'react';
import { importProducts } from '../../services/productService';
import { FaUpload, FaFileExcel } from 'react-icons/fa';
import LoadingSpinner from '../LoadingSpinner';

interface ProductImportProps {
  onImportSuccess: () => void;
}

const ProductImport: React.FC<ProductImportProps> = ({ onImportSuccess }) => {
  const [file, setFile] = useState<File | null>(null);
  const [importing, setImporting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [importResult, setImportResult] = useState<{
    message: string;
    imported: number;
    updated: number;
    failed: number;
  } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
      setError(null);
      setImportResult(null);
    }
  };

  const handleImport = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!file) {
      setError('Selecteer een bestand om te importeren');
      return;
    }

    try {
      setImporting(true);
      setError(null);
      setImportResult(null);

      const result = await importProducts(file);
      setImportResult(result);
      onImportSuccess();
    } catch (err: any) {
      console.error('Failed to import products:', err);
      setError(err.response?.data?.error || 'Fout bij importeren van producten. Probeer het opnieuw.');
    } finally {
      setImporting(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      setFile(null);
    }
  };

  return (
    <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
      <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text mb-4 flex items-center">
        <FaFileExcel className="mr-2 text-amspm-primary" /> OSEC Prijslijst Importeren
      </h2>

      <p className="text-gray-600 dark:text-dark-text-light mb-4">
        Upload een Excel bestand met de OSEC prijslijst. Het systeem zal de producten importeren en bestaande producten bijwerken.
      </p>

      <div className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 p-4 rounded-md mb-4">
        <h3 className="font-semibold mb-2">Verwachte kolomnamen in het Excel bestand:</h3>
        <ul className="list-disc list-inside space-y-1">
          <li>Artikelnr - Productnummer/code</li>
          <li>Artikel - Productnaam</li>
          <li>Bruto prijs - Brutoprijs van het product</li>
          <li>Korting - Kortingspercentage (optioneel)</li>
          <li>Netto prijs - Nettoprijs na korting (optioneel)</li>
          <li>Groep - Productcategorie (optioneel)</li>
          <li>Groepnaam - Subcategorie (optioneel)</li>
          <li>Info - Extra informatie (optioneel)</li>
        </ul>
      </div>

      <form onSubmit={handleImport} className="space-y-4">
        <div className="border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg p-6 text-center">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
            accept=".xlsx,.xls"
            id="product-import-file"
          />

          <label
            htmlFor="product-import-file"
            className="cursor-pointer flex flex-col items-center justify-center"
          >
            <FaUpload className="text-4xl text-amspm-primary dark:text-dark-accent mb-2" />
            <span className="text-amspm-text dark:text-dark-text font-medium">
              {file ? file.name : 'Klik om een Excel bestand te selecteren'}
            </span>
            <span className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Alleen .xlsx en .xls bestanden
            </span>
          </label>
        </div>

        {error && (
          <div className="bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 p-3 rounded-md">
            {error}
          </div>
        )}

        {importResult && (
          <div className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 p-3 rounded-md">
            <p>{importResult.message}</p>
            <p className="mt-2">
              <span className="font-medium">Geïmporteerd:</span> {importResult.imported} producten
              <br />
              <span className="font-medium">Bijgewerkt:</span> {importResult.updated} producten
              <br />
              <span className="font-medium">Mislukt:</span> {importResult.failed} producten
            </p>
          </div>
        )}

        <div className="flex justify-end">
          <button
            type="submit"
            className="btn btn-primary"
            disabled={!file || importing}
          >
            {importing ? (
              <>
                <LoadingSpinner size="sm" /> Importeren...
              </>
            ) : (
              <>Importeren</>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProductImport;
