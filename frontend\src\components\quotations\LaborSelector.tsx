import React, { useState } from 'react';
import { FaClock } from 'react-icons/fa';
import { LABOR_HOUR_RATE } from '../../constants/pricing';

export interface LaborOption {
  hours: number;
  label: string;
}

interface LaborSelectorProps {
  onSelect: (hours: number) => void;
  hourlyRate?: number;
  defaultOption?: number;
}

const LaborSelector: React.FC<LaborSelectorProps> = ({
  onSelect,
  hourlyRate = LABOR_HOUR_RATE,
  defaultOption = 0.5
}) => {
  const [selectedHours, setSelectedHours] = useState<number>(defaultOption);
  const [customHours, setCustomHours] = useState<string>('');
  const [showCustomInput, setShowCustomInput] = useState<boolean>(false);

  // Predefined labor options
  const laborOptions: LaborOption[] = [
    { hours: 0.5, label: '30 minuten' },
    { hours: 1, label: '1 uur' },
    { hours: 3, label: '3 uur' },
    { hours: -1, label: 'Aangepast...' }
  ];

  const handleOptionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = parseFloat(e.target.value);
    setSelectedHours(value);

    if (value === -1) {
      // Custom option selected
      setShowCustomInput(true);
      // Don't call onSelect yet, wait for custom input
    } else {
      setShowCustomInput(false);
      onSelect(value);
    }
  };

  const handleCustomHoursChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomHours(e.target.value);
  };

  const handleCustomHoursBlur = () => {
    const hours = parseFloat(customHours);
    if (!isNaN(hours) && hours > 0) {
      onSelect(hours);
    }
  };

  const handleCustomHoursKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const hours = parseFloat(customHours);
      if (!isNaN(hours) && hours > 0) {
        onSelect(hours);
      }
    }
  };

  return (
    <div className="bg-white dark:bg-dark-card border border-gray-200 dark:border-gray-700 rounded-md p-4 mb-4">
      <div className="flex items-center mb-3">
        <FaClock className="text-blue-500 dark:text-blue-400 mr-2" />
        <h3 className="font-medium text-amspm-text dark:text-dark-text">Arbeidstijd selecteren</h3>
      </div>

      <div className="flex flex-col sm:flex-row sm:items-center gap-3">
        <div className="flex-grow">
          <select
            value={selectedHours}
            onChange={handleOptionChange}
            className="input w-full"
          >
            {laborOptions.map(option => (
              <option key={option.hours} value={option.hours}>
                {option.label} {option.hours > 0 ? `(€${(option.hours * hourlyRate).toFixed(2)})` : ''}
              </option>
            ))}
          </select>
        </div>

        {showCustomInput && (
          <div className="flex items-center gap-2">
            <input
              type="number"
              value={customHours}
              onChange={handleCustomHoursChange}
              onBlur={handleCustomHoursBlur}
              onKeyDown={handleCustomHoursKeyDown}
              placeholder="Aantal uren"
              className="input w-32"
              min="0.1"
              step="0.1"
              autoFocus
            />
            <span className="text-sm text-gray-500 dark:text-gray-400">
              uur (€{customHours && !isNaN(parseFloat(customHours))
                ? (parseFloat(customHours) * hourlyRate).toFixed(2)
                : '0.00'})
            </span>
          </div>
        )}
      </div>

      <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
        Arbeid wordt berekend tegen een tarief van €{hourlyRate.toFixed(2)} per uur
      </p>
    </div>
  );
};

export default LaborSelector;
