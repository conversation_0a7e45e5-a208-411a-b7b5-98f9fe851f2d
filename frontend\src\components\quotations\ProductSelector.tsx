import React, { useState, useEffect } from 'react';
import { Product } from '../../types/product';
import { searchProducts, getAjaxProducts } from '../../services/productService';
import { FaSearch, FaPlus } from 'react-icons/fa';
import LoadingSpinner from '../LoadingSpinner';
import LaborSelector from './LaborSelector';
import { LABOR_HOUR_RATE } from '../../constants/pricing';

interface ProductSelectorProps {
  onProductSelected: (product: Product, laborHours?: number) => void;
}

const ProductSelector: React.FC<ProductSelectorProps> = ({ onProductSelected }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showResults, setShowResults] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [laborHours, setLaborHours] = useState<number>(0.5);
  const [ajaxFilterActive, setAjaxFilterActive] = useState(false);

  useEffect(() => {
    if (searchTerm.length >= 2 || ajaxFilterActive) {
      const delayDebounceFn = setTimeout(() => {
        searchProductsHandler();
      }, 300);

      return () => clearTimeout(delayDebounceFn);
    } else {
      setProducts([]);
    }
  }, [searchTerm, ajaxFilterActive]);

  const searchProductsHandler = async () => {
    if (searchTerm.length < 2 && !ajaxFilterActive) return;

    try {
      setLoading(true);
      setError(null);

      let response;

      // If Ajax filter is active, use the dedicated Ajax products function
      if (ajaxFilterActive) {
        response = await getAjaxProducts(searchTerm);
      } else {
        response = await searchProducts(searchTerm);
      }

      setProducts(response.products);
      setShowResults(true);
    } catch (err: any) {
      console.error('Failed to search products:', err);
      setError(err.response?.data?.error || 'Fout bij zoeken van producten');
    } finally {
      setLoading(false);
    }
  };

  // Function to toggle Ajax filter
  const toggleAjaxFilter = () => {
    const newFilterState = !ajaxFilterActive;
    setAjaxFilterActive(newFilterState);

    // If activating the filter, trigger a search
    if (newFilterState) {
      searchProductsHandler();
    } else if (searchTerm.length >= 2) {
      // If deactivating but we have a search term, search again without filter
      searchProductsHandler();
    } else {
      // If deactivating and no search term, clear results
      setProducts([]);
      setShowResults(false);
    }
  };

  const handleProductSelect = (product: Product) => {
    console.log('Selected product:', product);
    console.log('Product selling_price:', product.selling_price);
    setSelectedProduct(product);
    // Reset labor hours to default when selecting a new product
    setLaborHours(0.5);
    setSearchTerm('');
    setProducts([]);
    setShowResults(false);
  };

  const handleLaborHoursSelect = (hours: number) => {
    setLaborHours(hours);
  };

  const handleAddProduct = () => {
    if (selectedProduct) {
      onProductSelected(selectedProduct, laborHours);
      setSelectedProduct(null);
    }
  };

  const handleClickOutside = () => {
    setTimeout(() => {
      setShowResults(false);
    }, 200);
  };

  return (
    <div>
      {selectedProduct ? (
        <div className="bg-white dark:bg-dark-card border border-gray-200 dark:border-gray-700 rounded-md p-4 mb-4">
          <div className="flex justify-between items-start mb-3">
            <div>
              <h3 className="font-medium text-amspm-text dark:text-dark-text">{selectedProduct.name}</h3>
              <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mt-1">
                <span>{selectedProduct.product_code || 'Geen productcode'}</span>
                {selectedProduct.category && (
                  <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${selectedProduct.category === '130' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'}`}>
                    {selectedProduct.category === '130' ? 'Ajax' : `Groep ${selectedProduct.category}`}
                  </span>
                )}
              </div>
              <div className="text-amspm-primary dark:text-dark-accent font-medium mt-1">
                € {(selectedProduct.selling_price || 0).toFixed(2)}
              </div>
            </div>
            <button
              onClick={() => setSelectedProduct(null)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
              ×
            </button>
          </div>

          <LaborSelector
            onSelect={handleLaborHoursSelect}
            hourlyRate={LABOR_HOUR_RATE}
            defaultOption={0.5}
          />

          <button
            onClick={handleAddProduct}
            className="btn btn-primary w-full mt-2"
          >
            <FaPlus className="mr-2" /> Product toevoegen met arbeid
          </button>
        </div>
      ) : (
        <div className="relative">
          <div className="space-y-2">
            <div className="relative">
              <input
                type="text"
                placeholder="Zoek producten..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onFocus={() => (searchTerm.length >= 2 || ajaxFilterActive) && setShowResults(true)}
                onBlur={handleClickOutside}
                className="input w-full pl-10"
              />
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>

            <button
              type="button"
              onClick={toggleAjaxFilter}
              className={`w-full py-2 px-4 text-sm font-medium rounded-md flex items-center justify-center transition-colors ${
                ajaxFilterActive
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:hover:bg-blue-900/50'
              }`}
            >
              {ajaxFilterActive ? (
                <>
                  <span className="mr-2">✓</span> AJAX Systemen (Groep 130)
                </>
              ) : (
                <>AJAX Systemen (Groep 130)</>
              )}
            </button>
          </div>

          {showResults && (
            <div className="absolute z-10 mt-1 w-full bg-white dark:bg-dark-card shadow-lg rounded-md border border-gray-200 dark:border-gray-700 max-h-60 overflow-auto">
              {loading ? (
                <div className="p-4 text-center">
                  <LoadingSpinner size="sm" />
                </div>
              ) : error ? (
                <div className="p-4 text-red-500 dark:text-red-400 text-sm">
                  {error}
                </div>
              ) : products.length === 0 ? (
                <div className="p-4 text-gray-500 dark:text-gray-400 text-sm text-center">
                  Geen producten gevonden
                </div>
              ) : (
                <ul>
                  {products.map((product) => (
                    <li
                      key={product.id}
                      className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-dark-secondary cursor-pointer border-b border-gray-200 dark:border-gray-700 last:border-b-0"
                      onClick={() => handleProductSelect(product)}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-medium text-amspm-text dark:text-dark-text">
                            {product.name}
                          </div>
                          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                            <span>{product.product_code || 'Geen productcode'}</span>
                            {product.category && (
                              <span className={`ml-2 px-2 py-0.5 rounded-full text-xs ${product.category === '130' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'}`}>
                                {product.category === '130' ? 'Ajax' : `Groep ${product.category}`}
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="text-amspm-primary dark:text-dark-accent font-medium">
                          € {(product.selling_price || 0).toFixed(2)}
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ProductSelector;
