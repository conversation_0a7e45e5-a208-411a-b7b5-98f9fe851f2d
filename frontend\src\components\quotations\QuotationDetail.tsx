import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, Link } from 'react-router-dom';
import { Quotation } from '../../types/quotation';
import { getQuotationById, generateQuotationPdf, updateQuotationStatus, rejectAndDeleteQuotation } from '../../services/quotationService';
import { useConfirmation } from '../../context/ConfirmationContext';
import { FaEdit, FaFileAlt, FaArrowLeft, FaCheck, FaTimes, FaCalendarPlus } from 'react-icons/fa';
import LoadingSpinner from '../LoadingSpinner';
import QuotationItemList from './QuotationItemList';
import CreateUpcomingEventModal from '../CreateUpcomingEventModal';
import { Event } from '../../types/event';

const QuotationDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showConfirmation } = useConfirmation();

  const [quotation, setQuotation] = useState<Quotation | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [generating, setGenerating] = useState(false);
  const [showInstallationModal, setShowInstallationModal] = useState(false);
  const [installationEvents, setInstallationEvents] = useState<Event[]>([]);
  const [loadingEvents, setLoadingEvents] = useState(false);

  useEffect(() => {
    const fetchQuotation = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const data = await getQuotationById(parseInt(id));
        setQuotation(data);

        // Als de offerte een document_id heeft, haal dan de installatie-events op
        if (data.document_id) {
          await fetchInstallationEvents(data.document_id);
        }
      } catch (err: any) {
        console.error('Failed to fetch quotation:', err);
        setError(err.response?.data?.error || 'Fout bij ophalen van offerte');
      } finally {
        setLoading(false);
      }
    };

    fetchQuotation();
  }, [id]);

  const fetchInstallationEvents = async (documentId: number) => {
    try {
      setLoadingEvents(true);
      console.log('Fetching installation events for document:', documentId);

      // Haal events op die gekoppeld zijn aan het document
      const response = await fetch(`/api/events?document_id=${documentId}&event_type=checklist oplevering installatie`);
      const data = await response.json();

      console.log('Received installation events:', data.events);
      setInstallationEvents(data.events || []);
    } catch (err: any) {
      console.error('Failed to fetch installation events:', err);
    } finally {
      setLoadingEvents(false);
    }
  };

  const handleGeneratePdf = async () => {
    if (!id) return;

    showConfirmation({
      title: 'PDF genereren',
      message: 'Wil je een PDF genereren van deze offerte?',
      confirmText: 'Genereren',
      cancelText: 'Annuleren',
      onConfirm: async () => {
        try {
          setGenerating(true);
          const result = await generateQuotationPdf(parseInt(id));
          navigate(`/documents/${result.document.id}`);
        } catch (err: any) {
          console.error('Failed to generate PDF:', err);
          setError(err.response?.data?.error || 'Fout bij genereren van PDF');
        } finally {
          setGenerating(false);
        }
      }
    });
  };

  const handleAccept = async () => {
    if (!id || !quotation) return;

    showConfirmation({
      title: 'Offerte accepteren',
      message: `Weet je zeker dat je de offerte "${quotation.title}" wilt accepteren?`,
      confirmText: 'Accepteren',
      cancelText: 'Annuleren',
      confirmButtonClass: 'bg-green-600 hover:bg-green-700',
      onConfirm: async () => {
        try {
          setLoading(true);
          console.log('Accepting quotation:', id);
          await updateQuotationStatus(parseInt(id), 'accepted');
          // Refresh the quotation data
          const updatedQuotation = await getQuotationById(parseInt(id));
          setQuotation(updatedQuotation);

          // Ask if user wants to schedule an installation
          showConfirmation({
            title: 'Installatie inplannen',
            message: 'Wil je nu een installatie inplannen voor deze geaccepteerde offerte?',
            confirmText: 'Ja, plan installatie',
            cancelText: 'Nee, later',
            confirmButtonClass: 'bg-blue-600 hover:bg-blue-700',
            onConfirm: () => {
              // Toon de installatie modal
              setShowInstallationModal(true);
            }
          });
        } catch (err: any) {
          console.error('Failed to accept quotation:', err);
          setError(err.response?.data?.error || 'Fout bij accepteren van offerte');
        } finally {
          setLoading(false);
        }
      }
    });
  };

  const handleReject = async () => {
    if (!id || !quotation) return;

    showConfirmation({
      title: 'Offerte afwijzen',
      message: `Weet je zeker dat je de offerte "${quotation.title}" wilt afwijzen? De offerte en mogelijk de klant worden direct verwijderd.`,
      confirmText: 'Afwijzen en verwijderen',
      cancelText: 'Annuleren',
      confirmButtonClass: 'bg-red-600 hover:bg-red-700',
      onConfirm: async () => {
        try {
          setLoading(true);
          console.log('Rejecting quotation:', id);
          const result = await rejectAndDeleteQuotation(parseInt(id));
          console.log('Rejection result:', result);

          // Show success message and navigate back to quotations list
          showConfirmation({
            title: 'Offerte afgewezen',
            message: `De offerte is succesvol afgewezen en verwijderd. ${
              result.customer_deleted ? 'De klant is ook verwijderd omdat er geen andere offertes waren.' : ''
            }`,
            confirmText: 'OK',
            showCancel: false,
            confirmButtonClass: 'bg-blue-600 hover:bg-blue-700',
            onConfirm: () => {
              navigate('/quotations');
            }
          });
        } catch (err: any) {
          console.error('Failed to reject and delete quotation:', err);
          setError(err.response?.data?.error || 'Fout bij afwijzen en verwijderen van offerte');
          setLoading(false);
        }
      }
    });
  };

  const handleEventCreated = () => {
    // Vernieuw de offerte data
    if (id) {
      getQuotationById(parseInt(id))
        .then(data => setQuotation(data))
        .catch(err => console.error('Failed to refresh quotation:', err));
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'concept':
        return <span className="badge badge-gray">Concept</span>;
      case 'sent':
        return <span className="badge badge-blue">Verzonden</span>;
      case 'accepted':
        return <span className="badge badge-green">Geaccepteerd</span>;
      case 'rejected':
        return <span className="badge badge-red">Afgewezen</span>;
      default:
        return <span className="badge badge-gray">{status}</span>;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 p-6 rounded-md">
        <h2 className="text-xl font-semibold mb-2">Fout</h2>
        <p>{error}</p>
        <button
          onClick={() => navigate('/quotations')}
          className="btn btn-outline mt-4"
        >
          Terug naar offertes
        </button>
      </div>
    );
  }

  if (!quotation) {
    return (
      <div className="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 p-6 rounded-md">
        <h2 className="text-xl font-semibold mb-2">Offerte niet gevonden</h2>
        <button
          onClick={() => navigate('/quotations')}
          className="btn btn-outline mt-4"
        >
          Terug naar offertes
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <button
          onClick={() => navigate('/quotations')}
          className="btn btn-outline btn-sm"
        >
          <FaArrowLeft className="mr-2" /> Terug naar offertes
        </button>

        <div className="flex space-x-3">
          {quotation.status !== 'accepted' && quotation.status !== 'rejected' && (
            <>
              <button
                onClick={handleAccept}
                className="btn bg-green-600 hover:bg-green-700 text-white border-none"
                title="Accepteren"
              >
                <FaCheck className="mr-2" /> Geaccepteerd
              </button>
              <button
                onClick={handleReject}
                className="btn bg-red-600 hover:bg-red-700 text-white border-none"
                title="Afwijzen"
              >
                <FaTimes className="mr-2" /> Geweigerd
              </button>
            </>
          )}

          <Link
            to={`/quotations/${quotation.id}/edit`}
            className="btn btn-outline"
          >
            <FaEdit className="mr-2" /> Bewerken
          </Link>

          <button
            onClick={handleGeneratePdf}
            className="btn btn-secondary"
            disabled={generating}
          >
            {generating ? (
              <>
                <LoadingSpinner size="sm" /> Genereren...
              </>
            ) : (
              <>
                <FaFileAlt className="mr-2" /> PDF genereren
              </>
            )}
          </button>
        </div>
      </div>

      <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
        <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-4 mb-6">
          <div>
            <h1 className="text-2xl font-bold text-amspm-text dark:text-dark-text">
              {quotation.title}
            </h1>
            <p className="text-gray-600 dark:text-dark-text-light mt-1">
              Offertenummer: {quotation.quotation_number || 'Nog niet toegekend'}
            </p>
          </div>

          <div className="flex flex-col items-end">
            <div className="text-lg font-semibold text-amspm-text dark:text-dark-text">
              {getStatusBadge(quotation.status)}
            </div>
            <p className="text-gray-600 dark:text-dark-text-light mt-1">
              Geldig tot: {quotation.valid_until ? new Date(quotation.valid_until).toLocaleDateString() : 'Niet gespecificeerd'}
            </p>

            {/* Toon knop voor inplannen installatie als de offerte geaccepteerd is en er nog geen installatie-event is */}
            {quotation.status === 'accepted' && installationEvents.length === 0 && (
              <button
                onClick={() => setShowInstallationModal(true)}
                className="btn btn-sm bg-blue-600 hover:bg-blue-700 text-white border-none mt-2"
                title="Installatie inplannen"
              >
                <FaCalendarPlus className="mr-1" /> Installatie inplannen
              </button>
            )}

            {/* Toon informatie over geplande installatie als er al een installatie-event is */}
            {quotation.status === 'accepted' && installationEvents.length > 0 && (
              <div className="text-sm text-green-600 dark:text-green-400 mt-2">
                <span className="font-medium">Installatie gepland:</span> {new Date(installationEvents[0].scheduled_date).toLocaleString()}
              </div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <h2 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-2">
              Klantgegevens
            </h2>
            <p className="text-amspm-text dark:text-dark-text">
              {quotation.customer_name}
            </p>
          </div>

          <div>
            <h2 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-2">
              Offerte details
            </h2>
            <p className="text-gray-600 dark:text-dark-text-light">
              Gemaakt door: {quotation.created_by_name || 'Onbekend'}
            </p>
            <p className="text-gray-600 dark:text-dark-text-light">
              Aangemaakt op: {new Date(quotation.created_at).toLocaleDateString()}
            </p>
            <p className="text-gray-600 dark:text-dark-text-light">
              Laatst bijgewerkt: {new Date(quotation.updated_at).toLocaleDateString()}
            </p>
          </div>
        </div>

        {quotation.introduction && (
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-2">
              Inleiding
            </h2>
            <div className="bg-gray-50 dark:bg-dark-secondary p-4 rounded-md whitespace-pre-wrap">
              {quotation.introduction}
            </div>
          </div>
        )}
      </div>

      <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
        <h2 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-4">
          Producten
        </h2>

        <QuotationItemList
          items={quotation.items}
          onDeleteItem={() => {}}
          onUpdateItem={() => {}}
          onMoveItem={() => {}}
          readOnly={true}
        />

        <div className="mt-6 flex justify-end">
          <div className="w-64 space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-dark-text-light">Subtotaal:</span>
              <span className="text-amspm-text dark:text-dark-text">€ {(quotation.subtotal || 0).toFixed(2)}</span>
            </div>

            {(quotation.discount_percentage || 0) > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-dark-text-light">Korting ({quotation.discount_percentage || 0}%):</span>
                <span className="text-amspm-text dark:text-dark-text">€ {(quotation.discount_amount || 0).toFixed(2)}</span>
              </div>
            )}

            <div className="flex justify-between font-medium">
              <span className="text-gray-600 dark:text-dark-text-light">Totaal excl. BTW:</span>
              <span className="text-amspm-text dark:text-dark-text">€ {(quotation.total_excl_vat || 0).toFixed(2)}</span>
            </div>

            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-dark-text-light">BTW ({quotation.vat_percentage || 0}%):</span>
              <span className="text-amspm-text dark:text-dark-text">€ {(quotation.vat_amount || 0).toFixed(2)}</span>
            </div>

            <div className="flex justify-between text-lg font-bold pt-2 border-t border-gray-200 dark:border-gray-700">
              <span className="text-amspm-text dark:text-dark-text">Totaal incl. BTW:</span>
              <span className="text-amspm-primary dark:text-dark-accent">€ {(quotation.total_incl_vat || 0).toFixed(2)}</span>
            </div>
          </div>
        </div>
      </div>

      {quotation.conclusion && (
        <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
          <h2 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-2">
            Afsluiting
          </h2>
          <div className="bg-gray-50 dark:bg-dark-secondary p-4 rounded-md whitespace-pre-wrap">
            {quotation.conclusion}
          </div>
        </div>
      )}

      {quotation.document_id && (
        <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
          <h2 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-2">
            Gegenereerde documenten
          </h2>
          <div className="flex items-center">
            <FaFileAlt className="text-amspm-primary dark:text-dark-accent mr-2" />
            <Link
              to={`/documents/${quotation.document_id}`}
              className="text-amspm-primary dark:text-dark-accent hover:underline"
            >
              Bekijk offerte document
            </Link>
          </div>
        </div>
      )}

      {/* Installatie modal */}
      {showInstallationModal && quotation && quotation.document_id && (
        <CreateUpcomingEventModal
          document={{
            id: quotation.document_id,
            customer_id: quotation.customer_id,
            document_type: "offerte",
            // Andere vereiste velden
            event_id: null,
            file_url: "",
            uploaded_by: "0",
            expiry_date: null,
            created_at: "",
            expiry_status: "green",
            status: "active",
            sub_documents: []
          }}
          onClose={() => setShowInstallationModal(false)}
          onEventCreated={handleEventCreated}
        />
      )}
    </div>
  );
};

export default QuotationDetail;
