import React from 'react';
import { QuotationItem } from '../../types/quotation';
import { FaTrash, FaArrowUp, FaArrowDown } from 'react-icons/fa';
import { LABOR_HOUR_RATE } from '../../constants/pricing';

interface QuotationItemListProps {
  items: QuotationItem[];
  onDeleteItem: (itemId: number) => void;
  onUpdateItem: (itemId: number, data: Partial<QuotationItem>) => void;
  onMoveItem: (itemId: number, direction: 'up' | 'down') => void;
  readOnly?: boolean;
}

const QuotationItemList: React.FC<QuotationItemListProps> = ({
  items,
  onDeleteItem,
  onUpdateItem,
  onMoveItem,
  readOnly = false
}) => {
  const handleQuantityChange = (itemId: number, value: string) => {
    const quantity = parseFloat(value);
    if (!isNaN(quantity) && quantity > 0) {
      onUpdateItem(itemId, { quantity });
    }
  };

  const handleUnitPriceChange = (itemId: number, value: string) => {
    const unitPrice = parseFloat(value);
    if (!isNaN(unitPrice) && unitPrice >= 0) {
      onUpdateItem(itemId, { unit_price: unitPrice });
    }
  };

  const handleDiscountChange = (itemId: number, value: string) => {
    const discount = parseFloat(value);
    if (!isNaN(discount) && discount >= 0 && discount <= 100) {
      onUpdateItem(itemId, { discount_percentage: discount });
    }
  };

  const handleDescriptionChange = (itemId: number, value: string) => {
    onUpdateItem(itemId, { description: value });
  };

  if (items.length === 0) {
    return (
      <div className="bg-gray-50 dark:bg-dark-secondary p-4 rounded-md text-center text-gray-500 dark:text-gray-400">
        Nog geen items toegevoegd aan deze offerte.
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full bg-white dark:bg-dark-card shadow-md rounded-lg">
        <thead className="bg-gray-50 dark:bg-dark-secondary">
          <tr>
            {!readOnly && (
              <th className="w-16 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                Volgorde
              </th>
            )}
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
              Omschrijving
            </th>
            <th className="w-24 px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
              Aantal
            </th>
            <th className="w-32 px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
              Prijs per stuk
            </th>
            <th className="w-24 px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
              Korting %
            </th>
            <th className="w-32 px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
              Totaal
            </th>
            {!readOnly && (
              <th className="w-16 px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-dark-text-light uppercase tracking-wider">
                Acties
              </th>
            )}
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
          {items.map((item, index) => (
            <tr
              key={item.id}
              className={`hover:bg-gray-50 dark:hover:bg-dark-secondary ${
                !item.product_id && item.description?.toLowerCase().includes('arbeid')
                  ? 'bg-blue-50 dark:bg-blue-900/10 border-l-4 border-blue-300 dark:border-blue-700'
                  : ''
              }`}
            >
              {!readOnly && (
                <td className="px-4 py-3 whitespace-nowrap">
                  <div className="flex flex-col space-y-1">
                    <button
                      onClick={() => onMoveItem(item.id, 'up')}
                      disabled={index === 0}
                      className={`text-gray-500 hover:text-amspm-primary dark:text-gray-400 dark:hover:text-dark-accent ${
                        index === 0 ? 'opacity-30 cursor-not-allowed' : ''
                      }`}
                      title="Omhoog verplaatsen"
                    >
                      <FaArrowUp size={14} />
                    </button>
                    <button
                      onClick={() => onMoveItem(item.id, 'down')}
                      disabled={index === items.length - 1}
                      className={`text-gray-500 hover:text-amspm-primary dark:text-gray-400 dark:hover:text-dark-accent ${
                        index === items.length - 1 ? 'opacity-30 cursor-not-allowed' : ''
                      }`}
                      title="Omlaag verplaatsen"
                    >
                      <FaArrowDown size={14} />
                    </button>
                  </div>
                </td>
              )}
              <td className="px-4 py-3 text-sm text-amspm-text dark:text-dark-text">
                {readOnly || (!item.product_id && item.description?.toLowerCase().includes('arbeid')) ? (
                  <div>
                    {!item.product_id && item.description?.toLowerCase().includes('arbeid') ? (
                      <>
                        <div className="flex items-center mb-1">
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 mr-2">
                            Arbeid
                          </span>
                          <span className="text-xs text-blue-600 dark:text-blue-400">
                            Automatisch berekend op basis van product
                          </span>
                        </div>
                        <div className="font-medium text-blue-700 dark:text-blue-400">
                          <div>{item.description}</div>
                          <div className="text-xs mt-1 text-blue-600 dark:text-blue-400">
                            {item.quantity} {item.quantity === 1 ? 'uur' : 'uren'} × €{LABOR_HOUR_RATE.toFixed(2)} per uur
                          </div>
                        </div>
                      </>
                    ) : (
                      <div>
                        {item.description}
                        {item.product_code && (
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Productcode: {item.product_code}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ) : (
                  <div>
                    <textarea
                      value={item.description}
                      onChange={(e) => handleDescriptionChange(item.id, e.target.value)}
                      className="input w-full min-h-[60px] text-sm"
                      placeholder="Productomschrijving"
                    />
                  </div>
                )}
              </td>
              <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                {!item.product_id && item.description?.toLowerCase().includes('arbeid') ? (
                  <span className="text-blue-700 dark:text-blue-400">
                    {item.quantity} {item.quantity === 1 ? 'uur' : 'uren'}
                  </span>
                ) : readOnly ? (
                  item.quantity
                ) : (
                  <input
                    type="number"
                    value={item.quantity}
                    onChange={(e) => handleQuantityChange(item.id, e.target.value)}
                    className="input w-full text-right"
                    min="0.01"
                    step="0.01"
                  />
                )}
              </td>
              <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                {!item.product_id && item.description?.toLowerCase().includes('arbeid') ? (
                  <span className="text-blue-700 dark:text-blue-400">
                    € {item.unit_price.toFixed(2)}/uur
                  </span>
                ) : readOnly ? (
                  `€ ${item.unit_price.toFixed(2)}`
                ) : (
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2">€</span>
                    <input
                      type="number"
                      value={item.unit_price}
                      onChange={(e) => handleUnitPriceChange(item.id, e.target.value)}
                      className="input w-full text-right pl-8"
                      min="0"
                      step="0.01"
                    />
                  </div>
                )}
              </td>
              <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                {readOnly || (!item.product_id && item.description?.toLowerCase().includes('arbeid')) ? (
                  item.discount_percentage ? `${item.discount_percentage}%` : '-'
                ) : (
                  <div className="relative">
                    <input
                      type="number"
                      value={item.discount_percentage || ''}
                      onChange={(e) => handleDiscountChange(item.id, e.target.value)}
                      className="input w-full text-right pr-6"
                      min="0"
                      max="100"
                      step="0.1"
                      placeholder="0"
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2">%</span>
                  </div>
                )}
              </td>
              <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-right">
                € {item.total_price.toFixed(2)}
              </td>
              {!readOnly && (
                <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                  {!item.product_id && item.description?.toLowerCase().includes('arbeid') ? (
                    <div className="text-xs text-blue-600 dark:text-blue-400 italic">
                      Automatisch
                    </div>
                  ) : (
                    <button
                      onClick={() => onDeleteItem(item.id)}
                      className="text-red-500 hover:text-red-700"
                      title="Verwijderen"
                    >
                      <FaTrash />
                    </button>
                  )}
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default QuotationItemList;
