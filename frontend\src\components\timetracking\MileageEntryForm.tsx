import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useConfirmation } from '../../context/ConfirmationContext';
import { MileageEntryFormData } from '../../types/mileageEntry';
import { FaCar, FaCalendarAlt, FaUser, FaAlignLeft, FaSave, FaTimes } from 'react-icons/fa';
import { getAllUsers } from '../../services/userService';
import { User } from '../../types/user';
import * as Yup from 'yup';

interface MileageEntryFormProps {
  initialData?: Partial<MileageEntryFormData & { id?: number }>;
  onSubmit: (data: MileageEntryFormData) => void;
  onCancel: () => void;
  isAdmin?: boolean;
}

// Validation schema
const mileageEntrySchema = Yup.object().shape({
  user_id: Yup.number().required('Gebruiker is verplicht'),
  date: Yup.string().required('Datum is verplicht'),
  license_plate: Yup.string().required('Kentek<PERSON> is verplicht'),
  start_odometer: Yup.number()
    .required('Beginstand is verplicht')
    .min(0, 'Beginstand moet 0 of hoger zijn'),
  end_odometer: Yup.number()
    .required('Eindstand is verplicht')
    .min(0, 'Eindstand moet 0 of hoger zijn')
    .test('is-greater', 'Eindstand moet hoger zijn dan beginstand', function(value) {
      const { start_odometer } = this.parent;
      return !start_odometer || !value || value > start_odometer;
    }),
  reason: Yup.string().required('Reden is verplicht'),
  description: Yup.string().nullable()
});

const MileageEntryForm: React.FC<MileageEntryFormProps> = ({
  initialData = {},
  onSubmit,
  onCancel,
  isAdmin = false
}) => {
  const { user } = useAuth();
  const { showConfirmation } = useConfirmation();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Set default date to today if not provided
  const today = new Date().toISOString().split('T')[0];

  const [formData, setFormData] = useState<MileageEntryFormData>({
    user_id: initialData.user_id || (user?.id || 0),
    date: initialData.date || today,
    license_plate: initialData.license_plate || '',
    start_odometer: initialData.start_odometer || 0,
    end_odometer: initialData.end_odometer || 0,
    kilometers: initialData.kilometers || 0,
    reason: initialData.reason || '',
    description: initialData.description || ''
  });

  useEffect(() => {
    // Only load users if admin
    if (isAdmin) {
      const loadUsers = async () => {
        try {
          setLoading(true);
          const response = await getAllUsers();
          setUsers(response.users);
        } catch (error) {
          console.error('Error loading users:', error);
        } finally {
          setLoading(false);
        }
      };

      loadUsers();
    }
  }, [isAdmin]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Create new form data
    let newFormData = { ...formData };

    // Convert numeric fields to numbers
    if (name === 'start_odometer' || name === 'end_odometer') {
      const numValue = parseInt(value) || 0;
      newFormData = { ...newFormData, [name]: numValue };

      // Auto-calculate kilometers when odometer readings change
      if (name === 'start_odometer') {
        const kilometers = Math.max(0, newFormData.end_odometer - numValue);
        newFormData.kilometers = kilometers;
      } else if (name === 'end_odometer') {
        const kilometers = Math.max(0, numValue - newFormData.start_odometer);
        newFormData.kilometers = kilometers;
      }
    } else if (name === 'user_id') {
      newFormData = { ...newFormData, [name]: parseInt(value) || 0 };
    } else {
      newFormData = { ...newFormData, [name]: value };
    }

    setFormData(newFormData);

    // Clear error for this field when user changes it
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = async (): Promise<boolean> => {
    try {
      await mileageEntrySchema.validate(formData, { abortEarly: false });
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof Yup.ValidationError) {
        const newErrors: Record<string, string> = {};
        error.inner.forEach(err => {
          if (err.path) {
            newErrors[err.path] = err.message;
          }
        });
        setErrors(newErrors);

        // Show confirmation dialog for validation errors
        showConfirmation({
          title: 'Validatiefout',
          message: 'Controleer de ingevoerde gegevens en probeer het opnieuw.',
          confirmText: 'OK',
          showCancel: false,
          onConfirm: () => {
            // Do nothing, just close the dialog
          }
        });
      } else {
        // Show confirmation dialog for unexpected errors
        showConfirmation({
          title: 'Onverwachte fout',
          message: 'Er is een onverwachte fout opgetreden. Probeer het opnieuw of neem contact op met de beheerder.',
          confirmText: 'OK',
          showCancel: false,
          onConfirm: () => {
            // Do nothing, just close the dialog
          }
        });
      }
      return false;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const isValid = await validateForm();
    if (!isValid) return;

    // We no longer block multiple mileage entries on the same day
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text mb-4">
          {initialData.user_id ? 'Kilometers bewerken' : 'Kilometers toevoegen'}
        </h2>

        {isAdmin && (
          <div className="mb-4">
            <label htmlFor="user_id" className="block text-sm font-medium text-gray-700 dark:text-dark-text-light mb-1">
              <FaUser className="inline mr-2" /> Gebruiker
            </label>
            <select
              id="user_id"
              name="user_id"
              value={formData.user_id}
              onChange={handleChange}
              className={`input w-full ${errors.user_id ? 'border-red-500' : ''}`}
              disabled={loading}
            >
              <option value="">Selecteer gebruiker</option>
              {users.map(user => (
                <option key={user.id} value={user.id}>
                  {user.name || user.email}
                </option>
              ))}
            </select>
            {errors.user_id && <p className="text-red-500 text-xs mt-1">{errors.user_id}</p>}
          </div>
        )}

        <div className="mb-4">
          <label htmlFor="date" className="block text-sm font-medium text-gray-700 dark:text-dark-text-light mb-1">
            <FaCalendarAlt className="inline mr-2" /> Datum
          </label>
          <input
            type="date"
            id="date"
            name="date"
            value={formData.date}
            onChange={handleChange}
            className={`input w-full ${errors.date ? 'border-red-500' : ''}`}
            max={today}
          />
          {errors.date && <p className="text-red-500 text-xs mt-1">{errors.date}</p>}
        </div>

        <div className="mb-4">
          <label htmlFor="license_plate" className="block text-sm font-medium text-gray-700 dark:text-dark-text-light mb-1">
            <FaCar className="inline mr-2" /> Kenteken <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="license_plate"
            name="license_plate"
            value={formData.license_plate}
            onChange={handleChange}
            className={`input w-full ${errors.license_plate ? 'border-red-500' : ''}`}
            placeholder="Bijv. 12-ABC-3"
            style={{ textTransform: 'uppercase' }}
          />
          {errors.license_plate && <p className="text-red-500 text-xs mt-1">{errors.license_plate}</p>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label htmlFor="start_odometer" className="block text-sm font-medium text-gray-700 dark:text-dark-text-light mb-1">
              <FaCar className="inline mr-2" /> Beginstand (km) <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              id="start_odometer"
              name="start_odometer"
              value={formData.start_odometer}
              onChange={handleChange}
              className={`input w-full ${errors.start_odometer ? 'border-red-500' : ''}`}
              min="0"
              step="1"
              placeholder="Kilometerstand bij vertrek"
            />
            {errors.start_odometer && <p className="text-red-500 text-xs mt-1">{errors.start_odometer}</p>}
          </div>

          <div>
            <label htmlFor="end_odometer" className="block text-sm font-medium text-gray-700 dark:text-dark-text-light mb-1">
              <FaCar className="inline mr-2" /> Eindstand (km) <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              id="end_odometer"
              name="end_odometer"
              value={formData.end_odometer}
              onChange={handleChange}
              className={`input w-full ${errors.end_odometer ? 'border-red-500' : ''}`}
              min="0"
              step="1"
              placeholder="Kilometerstand bij aankomst"
            />
            {errors.end_odometer && <p className="text-red-500 text-xs mt-1">{errors.end_odometer}</p>}
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="kilometers" className="block text-sm font-medium text-gray-700 dark:text-dark-text-light mb-1">
            <FaCar className="inline mr-2" /> Gereden kilometers
          </label>
          <input
            type="number"
            id="kilometers"
            name="kilometers"
            value={formData.kilometers}
            className="input w-full bg-gray-100 dark:bg-gray-700"
            readOnly
            step="0.1"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Wordt automatisch berekend: eindstand - beginstand = {formData.kilometers} km
          </p>
        </div>

        <div className="mb-4">
          <label htmlFor="reason" className="block text-sm font-medium text-gray-700 dark:text-dark-text-light mb-1">
            <FaAlignLeft className="inline mr-2" /> Reden <span className="text-red-500">*</span>
          </label>
          <select
            id="reason"
            name="reason"
            value={formData.reason}
            onChange={handleChange}
            className={`input w-full ${errors.reason ? 'border-red-500' : ''}`}
          >
            <option value="">Selecteer reden</option>
            <option value="Klantbezoek">Klantbezoek</option>
            <option value="Installatie">Installatie</option>
            <option value="Onderhoud">Onderhoud</option>
            <option value="Reparatie">Reparatie</option>
            <option value="Leverantier">Leverantier</option>
            <option value="Kantoor">Naar kantoor</option>
            <option value="Training">Training/Cursus</option>
            <option value="Overig">Overig</option>
          </select>
          {errors.reason && <p className="text-red-500 text-xs mt-1">{errors.reason}</p>}
        </div>



        <div className="mb-4">
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-dark-text-light mb-1">
            <FaAlignLeft className="inline mr-2" /> Aanvullende omschrijving (optioneel)
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            className="input w-full"
            placeholder="Eventuele aanvullende informatie over de rit"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="btn btn-secondary flex items-center"
        >
          <FaTimes className="mr-2" /> Annuleren
        </button>
        <button
          type="submit"
          className="btn btn-primary flex items-center"
        >
          <FaSave className="mr-2" /> Opslaan
        </button>
      </div>
    </form>
  );
};

export default MileageEntryForm;
