import React, { useState, useEffect } from 'react';
import { TimeEntry } from '../../types/timeEntry';
import { MileageEntry } from '../../types/mileageEntry';
import { FaClock, FaCar, FaCalendarAlt, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { getTimeEntriesByMonthYear, getMileageEntriesByMonthYear } from '../../services/timeTrackingService';

interface MonthlyTimesheetProps {
  userId: number;
  onSelectTimeEntry?: (entry: TimeEntry) => void;
  onSelectMileageEntry?: (entry: MileageEntry) => void;
}

const MonthlyTimesheet: React.FC<MonthlyTimesheetProps> = ({
  userId,
  onSelectTimeEntry,
  onSelectMileageEntry
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([]);
  const [mileageEntries, setMileageEntries] = useState<MileageEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalHours, setTotalHours] = useState(0);
  const [approvedHours, setApprovedHours] = useState(0);
  const [pendingHours, setPendingHours] = useState(0);
  const [totalKilometers, setTotalKilometers] = useState(0);
  const [approvedKilometers, setApprovedKilometers] = useState(0);
  const [pendingKilometers, setPendingKilometers] = useState(0);

  const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
  const currentYear = currentDate.getFullYear();

  const monthNames = [
    'Januari', 'Februari', 'Maart', 'April', 'Mei', 'Juni',
    'Juli', 'Augustus', 'September', 'Oktober', 'November', 'December'
  ];

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch time entries
        const timeResponse = await getTimeEntriesByMonthYear(currentMonth, currentYear, userId);
        setTimeEntries(timeResponse.entries);
        setTotalHours(timeResponse.monthly_total_hours || 0);
        setApprovedHours(timeResponse.monthly_approved_hours || 0);
        setPendingHours(timeResponse.monthly_pending_hours || 0);

        // Fetch mileage entries
        const mileageResponse = await getMileageEntriesByMonthYear(currentMonth, currentYear, userId);
        setMileageEntries(mileageResponse.entries);
        setTotalKilometers(mileageResponse.monthly_total_kilometers || 0);
        setApprovedKilometers(mileageResponse.monthly_approved_kilometers || 0);
        setPendingKilometers(mileageResponse.monthly_pending_kilometers || 0);
      } catch (err) {
        console.error('Error fetching timesheet data:', err);
        setError('Er is een fout opgetreden bij het ophalen van de gegevens.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [userId, currentMonth, currentYear]);

  const goToPreviousMonth = () => {
    setCurrentDate(prevDate => {
      const newDate = new Date(prevDate);
      newDate.setMonth(newDate.getMonth() - 1);
      return newDate;
    });
  };

  const goToNextMonth = () => {
    setCurrentDate(prevDate => {
      const newDate = new Date(prevDate);
      newDate.setMonth(newDate.getMonth() + 1);
      return newDate;
    });
  };

  const formatTime = (timeString: string) => {
    return timeString.substring(0, 5); // Extract HH:MM from HH:MM:SS
  };

  const calculateHours = (startTime: string, endTime: string, breakTime: number = 0) => {
    const start = new Date(`2000-01-01T${startTime}`);
    const end = new Date(`2000-01-01T${endTime}`);
    const diffMs = end.getTime() - start.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    // Subtract break time (convert minutes to hours)
    const breakHours = breakTime / 60;
    const totalHours = Math.max(0, diffHours - breakHours);
    return totalHours.toFixed(2);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('nl-NL', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }).format(date);
  };

  const getStatusClass = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved':
        return 'Goedgekeurd';
      case 'rejected':
        return 'Afgekeurd';
      default:
        return 'In behandeling';
    }
  };

  return (
    <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text">
          <FaCalendarAlt className="inline mr-2" />
          Urenstaat {monthNames[currentMonth - 1]} {currentYear}
        </h2>

        <div className="flex items-center space-x-2">
          <button
            onClick={goToPreviousMonth}
            className="btn btn-icon btn-secondary"
            aria-label="Vorige maand"
          >
            <FaChevronLeft />
          </button>
          <button
            onClick={goToNextMonth}
            className="btn btn-icon btn-secondary"
            aria-label="Volgende maand"
          >
            <FaChevronRight />
          </button>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center py-8">
          <div className="loader"></div>
        </div>
      ) : error ? (
        <div className="text-red-500 text-center py-4">{error}</div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Uren sectie */}
            <div className="bg-white dark:bg-dark-card rounded-lg p-4 shadow-sm">
              <h3 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-3 flex items-center">
                <FaClock className="mr-2" /> Uren
              </h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-blue-50 dark:bg-blue-900 rounded-lg p-4 flex items-center">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-blue-700 dark:text-blue-200">Goedgekeurd</h4>
                    <p className="text-2xl font-bold text-blue-800 dark:text-blue-100">{approvedHours.toFixed(2)}</p>
                  </div>
                </div>
                <div className="bg-yellow-50 dark:bg-yellow-900 rounded-lg p-4 flex items-center">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-yellow-700 dark:text-yellow-200">Openstaand</h4>
                    <p className="text-2xl font-bold text-yellow-800 dark:text-yellow-100">{pendingHours.toFixed(2)}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Kilometers sectie */}
            <div className="bg-white dark:bg-dark-card rounded-lg p-4 shadow-sm">
              <h3 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-3 flex items-center">
                <FaCar className="mr-2" /> Kilometers
              </h3>
              <div className="grid grid-cols-2 gap-3">
                <div className="bg-green-50 dark:bg-green-900 rounded-lg p-4 flex items-center">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-green-700 dark:text-green-200">Goedgekeurd</h4>
                    <p className="text-2xl font-bold text-green-800 dark:text-green-100">{approvedKilometers.toFixed(2)}</p>
                  </div>
                </div>
                <div className="bg-orange-50 dark:bg-orange-900 rounded-lg p-4 flex items-center">
                  <div className="flex-1">
                    <h4 className="text-sm font-medium text-orange-700 dark:text-orange-200">Openstaand</h4>
                    <p className="text-2xl font-bold text-orange-800 dark:text-orange-100">{pendingKilometers.toFixed(2)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Time Entries */}
          <div className="mb-8">
            <h3 className="text-lg font-medium text-amspm-text dark:text-dark-text mb-4 flex items-center">
              <FaClock className="mr-2" /> Uren
            </h3>

            {timeEntries.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                Geen uren geregistreerd voor deze maand.
              </p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Datum
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Tijd
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Pauze
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Uren
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Omschrijving
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                    {timeEntries.map(entry => (
                      <tr
                        key={entry.id}
                        className="hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
                        onClick={() => onSelectTimeEntry && onSelectTimeEntry(entry)}
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                          {formatDate(entry.date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                          {formatTime(entry.start_time)} - {formatTime(entry.end_time)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                          {entry.break_time > 0 ? `${entry.break_time} min` : '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                          {calculateHours(entry.start_time, entry.end_time, entry.break_time)}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900 dark:text-dark-text">
                          {entry.description || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(entry.status)}`}>
                            {getStatusText(entry.status)}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Mileage Entries */}
          <div>
            <h3 className="text-lg font-medium text-amspm-text dark:text-dark-text mb-4 flex items-center">
              <FaCar className="mr-2" /> Kilometers
            </h3>

            {mileageEntries.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                Geen kilometers geregistreerd voor deze maand.
              </p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Datum
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Kenteken
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Beginstand
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Eindstand
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Kilometers
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Reden
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Omschrijving
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                    {mileageEntries.map(entry => (
                      <tr
                        key={entry.id}
                        className="hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
                        onClick={() => onSelectMileageEntry && onSelectMileageEntry(entry)}
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                          {formatDate(entry.date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                          {entry.license_plate}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                          {entry.start_odometer.toLocaleString()} km
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                          {entry.end_odometer.toLocaleString()} km
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                          {entry.kilometers.toFixed(2)} km
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                          {entry.reason}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900 dark:text-dark-text">
                          {entry.description || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(entry.status)}`}>
                            {getStatusText(entry.status)}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default MonthlyTimesheet;
