import React, { useState, useEffect } from 'react';
import { TimeEntry } from '../../types/timeEntry';
import { MileageEntry } from '../../types/mileageEntry';
import { User } from '../../types/user';
import {
  getPendingTimeEntries,
  getPendingMileageEntries,
  approveTimeEntry,
  rejectTimeEntry,
  approveMileageEntry,
  rejectMileageEntry
} from '../../services/timeTrackingService';
import { getAllUsers } from '../../services/userService';
import { useConfirmation } from '../../context/ConfirmationContext';
import { FaClock, FaCar, FaCheck, FaTimes, FaFilter, FaUser } from 'react-icons/fa';
import Pagination from '../Pagination';

const TimeApprovalList: React.FC = () => {
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([]);
  const [mileageEntries, setMileageEntries] = useState<MileageEntry[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState<'time' | 'mileage'>('time');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeEntriesPage, setTimeEntriesPage] = useState(1);
  const [mileageEntriesPage, setMileageEntriesPage] = useState(1);
  const [timeEntriesTotal, setTimeEntriesTotal] = useState(0);
  const [mileageEntriesTotal, setMileageEntriesTotal] = useState(0);
  const perPage = 10;

  const { showConfirmation } = useConfirmation();

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await getAllUsers();
        setUsers(response.users);
      } catch (err) {
        console.error('Error fetching users:', err);
        setError('Er is een fout opgetreden bij het ophalen van gebruikers.');
      }
    };

    fetchUsers();
  }, []);

  useEffect(() => {
    const fetchTimeEntries = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await getPendingTimeEntries(timeEntriesPage, perPage);
        setTimeEntries(response.entries);
        setTimeEntriesTotal(response.total);
      } catch (err) {
        console.error('Error fetching time entries:', err);
        setError('Er is een fout opgetreden bij het ophalen van uren.');
      } finally {
        setLoading(false);
      }
    };

    fetchTimeEntries();
  }, [timeEntriesPage]);

  useEffect(() => {
    const fetchMileageEntries = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await getPendingMileageEntries(mileageEntriesPage, perPage);
        setMileageEntries(response.entries);
        setMileageEntriesTotal(response.total);
      } catch (err) {
        console.error('Error fetching mileage entries:', err);
        setError('Er is een fout opgetreden bij het ophalen van kilometers.');
      } finally {
        setLoading(false);
      }
    };

    fetchMileageEntries();
  }, [mileageEntriesPage]);

  const handleApproveTimeEntry = (entryId: number) => {
    showConfirmation({
      title: 'Uren goedkeuren',
      message: 'Weet je zeker dat je deze uren wilt goedkeuren?',
      confirmText: 'Goedkeuren',
      cancelText: 'Annuleren',
      onConfirm: async () => {
        try {
          await approveTimeEntry(entryId);
          // Refresh the list
          const response = await getPendingTimeEntries(timeEntriesPage, perPage);
          setTimeEntries(response.entries);
          setTimeEntriesTotal(response.total);
        } catch (err) {
          console.error('Error approving time entry:', err);
          setError('Er is een fout opgetreden bij het goedkeuren van de uren.');
        }
      }
    });
  };

  const handleRejectTimeEntry = (entryId: number) => {
    showConfirmation({
      title: 'Uren afkeuren',
      message: 'Weet je zeker dat je deze uren wilt afkeuren? Deze actie kan niet ongedaan worden gemaakt.',
      confirmText: 'Afkeuren',
      cancelText: 'Annuleren',
      confirmButtonClass: 'btn-danger',
      onConfirm: async () => {
        try {
          await rejectTimeEntry(entryId);
          // Refresh the list
          const response = await getPendingTimeEntries(timeEntriesPage, perPage);
          setTimeEntries(response.entries);
          setTimeEntriesTotal(response.total);
        } catch (err) {
          console.error('Error rejecting time entry:', err);
          setError('Er is een fout opgetreden bij het afkeuren van de uren.');
        }
      }
    });
  };

  const handleApproveMileageEntry = (entryId: number) => {
    showConfirmation({
      title: 'Kilometers goedkeuren',
      message: 'Weet je zeker dat je deze kilometers wilt goedkeuren?',
      confirmText: 'Goedkeuren',
      cancelText: 'Annuleren',
      onConfirm: async () => {
        try {
          await approveMileageEntry(entryId);
          // Refresh the list
          const response = await getPendingMileageEntries(mileageEntriesPage, perPage);
          setMileageEntries(response.entries);
          setMileageEntriesTotal(response.total);
        } catch (err) {
          console.error('Error approving mileage entry:', err);
          setError('Er is een fout opgetreden bij het goedkeuren van de kilometers.');
        }
      }
    });
  };

  const handleRejectMileageEntry = (entryId: number) => {
    showConfirmation({
      title: 'Kilometers afkeuren',
      message: 'Weet je zeker dat je deze kilometers wilt afkeuren? Deze actie kan niet ongedaan worden gemaakt.',
      confirmText: 'Afkeuren',
      cancelText: 'Annuleren',
      confirmButtonClass: 'btn-danger',
      onConfirm: async () => {
        try {
          await rejectMileageEntry(entryId);
          // Refresh the list
          const response = await getPendingMileageEntries(mileageEntriesPage, perPage);
          setMileageEntries(response.entries);
          setMileageEntriesTotal(response.total);
        } catch (err) {
          console.error('Error rejecting mileage entry:', err);
          setError('Er is een fout opgetreden bij het afkeuren van de kilometers.');
        }
      }
    });
  };

  const formatTime = (timeString: string) => {
    return timeString.substring(0, 5); // Extract HH:MM from HH:MM:SS
  };

  const calculateHours = (startTime: string, endTime: string, breakTime: number = 0) => {
    const start = new Date(`2000-01-01T${startTime}`);
    const end = new Date(`2000-01-01T${endTime}`);
    const diffMs = end.getTime() - start.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    // Subtract break time (convert minutes to hours)
    const breakHours = breakTime / 60;
    const totalHours = Math.max(0, diffHours - breakHours);
    return totalHours.toFixed(2);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('nl-NL', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }).format(date);
  };

  const filteredTimeEntries = selectedUser
    ? timeEntries.filter(entry => entry.user_id === selectedUser)
    : timeEntries;

  const filteredMileageEntries = selectedUser
    ? mileageEntries.filter(entry => entry.user_id === selectedUser)
    : mileageEntries;

  return (
    <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text">
          Goedkeuring uren en kilometers
        </h2>

        <div className="flex items-center space-x-2">
          <div className="relative">
            <select
              value={selectedUser || ''}
              onChange={(e) => setSelectedUser(e.target.value ? parseInt(e.target.value) : null)}
              className="input pr-8"
            >
              <option value="">Alle gebruikers</option>
              {users.map(user => (
                <option key={user.id} value={user.id}>
                  {user.name || user.email}
                </option>
              ))}
            </select>
            <FaUser className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        </div>
      </div>

      <div className="mb-6">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex">
            <button
              className={`py-2 px-4 border-b-2 font-medium text-sm ${
                activeTab === 'time'
                  ? 'border-amspm-primary text-amspm-primary dark:border-dark-accent dark:text-dark-accent'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
              }`}
              onClick={() => setActiveTab('time')}
            >
              <FaClock className="inline mr-2" /> Uren ({timeEntriesTotal})
            </button>
            <button
              className={`ml-8 py-2 px-4 border-b-2 font-medium text-sm ${
                activeTab === 'mileage'
                  ? 'border-amspm-primary text-amspm-primary dark:border-dark-accent dark:text-dark-accent'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
              }`}
              onClick={() => setActiveTab('mileage')}
            >
              <FaCar className="inline mr-2" /> Kilometers ({mileageEntriesTotal})
            </button>
          </nav>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center py-8">
          <div className="loader"></div>
        </div>
      ) : error ? (
        <div className="text-red-500 text-center py-4">{error}</div>
      ) : (
        <>
          {activeTab === 'time' && (
            <>
              {filteredTimeEntries.length === 0 ? (
                <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                  Geen uren ter goedkeuring.
                </p>
              ) : (
                <>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Gebruiker
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Datum
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Tijd
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Pauze
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Uren
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Omschrijving
                          </th>
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Acties
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                        {filteredTimeEntries.map(entry => (
                          <tr key={entry.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {entry.user_name}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {formatDate(entry.date)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {formatTime(entry.start_time)} - {formatTime(entry.end_time)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {entry.break_time > 0 ? `${entry.break_time} min` : '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {calculateHours(entry.start_time, entry.end_time, entry.break_time)}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900 dark:text-dark-text">
                              {entry.description || '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                onClick={() => handleApproveTimeEntry(entry.id)}
                                className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-4"
                              >
                                <FaCheck className="inline" /> Goedkeuren
                              </button>
                              <button
                                onClick={() => handleRejectTimeEntry(entry.id)}
                                className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                              >
                                <FaTimes className="inline" /> Afkeuren
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  <div className="mt-4">
                    <Pagination
                      currentPage={timeEntriesPage}
                      totalItems={timeEntriesTotal}
                      itemsPerPage={perPage}
                      onPageChange={setTimeEntriesPage}
                    />
                  </div>
                </>
              )}
            </>
          )}

          {activeTab === 'mileage' && (
            <>
              {filteredMileageEntries.length === 0 ? (
                <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                  Geen kilometers ter goedkeuring.
                </p>
              ) : (
                <>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Gebruiker
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Datum
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Kenteken
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Beginstand
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Eindstand
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Kilometers
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Reden
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Omschrijving
                          </th>
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Acties
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                        {filteredMileageEntries.map(entry => (
                          <tr key={entry.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {entry.user_name}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {formatDate(entry.date)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {entry.license_plate}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {entry.start_odometer.toLocaleString()} km
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {entry.end_odometer.toLocaleString()} km
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {entry.kilometers.toFixed(2)} km
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {entry.reason}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900 dark:text-dark-text">
                              {entry.description || '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                onClick={() => handleApproveMileageEntry(entry.id)}
                                className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-4"
                              >
                                <FaCheck className="inline" /> Goedkeuren
                              </button>
                              <button
                                onClick={() => handleRejectMileageEntry(entry.id)}
                                className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                              >
                                <FaTimes className="inline" /> Afkeuren
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  <div className="mt-4">
                    <Pagination
                      currentPage={mileageEntriesPage}
                      totalItems={mileageEntriesTotal}
                      itemsPerPage={perPage}
                      onPageChange={setMileageEntriesPage}
                    />
                  </div>
                </>
              )}
            </>
          )}
        </>
      )}
    </div>
  );
};

export default TimeApprovalList;
