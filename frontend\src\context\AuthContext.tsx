import React, { createContext, useContext, useState, useEffect } from "react";
import { auth } from "../firebase";
import { signInWithEmailAndPassword, signOut, onAuthStateChanged } from "firebase/auth";
import * as authService from "../services/authService";
// Socket service removed
import { User } from "../types/user";
import api from "../api";

interface AuthContextType {
  user: User | null;
  setUser: React.Dispatch<React.SetStateAction<User | null>>;
  login: (email: string, password: string) => Promise<User>;
  logout: () => Promise<void>;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch CSRF token when the app starts
  useEffect(() => {
    const fetchCSRFToken = async () => {
      try {
        await authService.fetchCSRFToken();
        console.log('CSRF token fetched successfully');
      } catch (error) {
        console.error('Failed to fetch CSRF token:', error);
      }
    };

    fetchCSRFToken();
  }, []);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        try {
          const token = await firebaseUser.getIdToken();
          const userData = await authService.verifyToken(token);
          setUser(userData);

          // User data is now stored in an HttpOnly cookie on the server
          // We don't need to store it in localStorage anymore

          // WebSocket connection removed
        } catch (error: any) {
          console.error("Failed to verify token:", error);
          // Check if this is a canceled request (timeout) - don't log out the user in this case
          if (error?.name === 'CanceledError' || error?.code === 'ERR_CANCELED') {
            console.log('Token verification request was canceled (timeout). User session maintained.');
            // Try to fetch user data from the server using the HttpOnly cookie
            try {
              const response = await api.get('/auth/current-user');
              if (response.data) {
                setUser(response.data);
                return;
              }
            } catch (e) {
              console.error('Failed to get current user data:', e);
            }
          } else {
            // For other errors, log out the user
            setUser(null);
          }
        }
      } else {
        setUser(null);
      }
      setLoading(false);
    });
    return () => unsubscribe();
  }, []);

  const login = async (email: string, password: string): Promise<User> => {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const token = await userCredential.user.getIdToken();

      try {
        const userData = await authService.verifyToken(token);
        setUser(userData);

        // User data is now stored in an HttpOnly cookie on the server
        // We don't need to store it in localStorage anymore

        // WebSocket connection removed
        return userData;
      } catch (error: any) {
        // Handle token verification errors
        console.error("Failed to verify token during login:", error);

        if (error?.name === 'CanceledError' || error?.code === 'ERR_CANCELED') {
          // If it's a timeout/canceled error, try to use Firebase user data as fallback
          console.log('Token verification request was canceled (timeout). Using Firebase data as fallback.');

          const fallbackUserData: User = {
            id: 0, // We don't know the actual ID
            email: userCredential.user.email || '',
            role: 'unknown', // We don't know the role
            name: userCredential.user.displayName || '',
            firebase_uid: userCredential.user.uid
          };

          setUser(fallbackUserData);
          return fallbackUserData;
        } else {
          // For other errors, propagate the error
          throw error;
        }
      }
    } catch (error) {
      // Handle Firebase authentication errors
      console.error("Failed to login with Firebase:", error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      // Call the backend logout endpoint to invalidate the session
      await authService.logout();
      // WebSocket disconnection removed
      // Then sign out from Firebase
      await signOut(auth);
      setUser(null);
      // No need to remove from localStorage as we're using HttpOnly cookies now
    } catch (error: any) {
      console.error('Error during logout:', error);

      // If it's a timeout/canceled error, still proceed with logout
      if (error?.name === 'CanceledError' || error?.code === 'ERR_CANCELED') {
        console.log('Logout request was canceled (timeout). Proceeding with client-side logout.');
      }

      // WebSocket disconnection removed
      // Still try to sign out from Firebase even if backend logout fails
      await signOut(auth);
      setUser(null);
      // No need to remove from localStorage as we're using HttpOnly cookies now
    }
  };

  return (
    <AuthContext.Provider value={{ user, setUser, login, logout, loading }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
