import React, { createContext, useContext, useState, ReactNode } from 'react';

interface ConfirmationContextType {
  showConfirmation: (options: ConfirmationOptions) => void;
  hideConfirmation: () => void;
  confirmationState: ConfirmationState;
}

export interface ConfirmationOptions {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  confirmButtonClass?: string;
  showCancel?: boolean;
  onConfirm: () => void;
  onCancel?: () => void;
}

interface ConfirmationState extends ConfirmationOptions {
  isOpen: boolean;
}

const defaultConfirmationState: ConfirmationState = {
  isOpen: false,
  title: '',
  message: '',
  confirmText: 'Bevestigen',
  cancelText: 'Annuleren',
  confirmButtonClass: 'bg-blue-600 hover:bg-blue-700',
  showCancel: true,
  onConfirm: () => {},
  onCancel: () => {},
};

const ConfirmationContext = createContext<ConfirmationContextType | undefined>(undefined);

export const ConfirmationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [confirmationState, setConfirmationState] = useState<ConfirmationState>(defaultConfirmationState);

  const showConfirmation = (options: ConfirmationOptions) => {
    // Log voor debugging
    console.log('Showing confirmation with options:', options);

    // Zorg ervoor dat alle opties correct worden overgenomen
    setConfirmationState({
      ...defaultConfirmationState,
      ...options,
      isOpen: true,
      // Zorg ervoor dat deze waarden altijd worden ingesteld, zelfs als ze niet in options zitten
      confirmText: options.confirmText || defaultConfirmationState.confirmText,
      cancelText: options.cancelText || defaultConfirmationState.cancelText,
      confirmButtonClass: options.confirmButtonClass || defaultConfirmationState.confirmButtonClass,
      showCancel: options.showCancel !== undefined ? options.showCancel : defaultConfirmationState.showCancel,
    });
  };

  const hideConfirmation = () => {
    setConfirmationState((prev) => ({
      ...prev,
      isOpen: false,
    }));
  };

  return (
    <ConfirmationContext.Provider
      value={{
        showConfirmation,
        hideConfirmation,
        confirmationState,
      }}
    >
      {children}
    </ConfirmationContext.Provider>
  );
};

export const useConfirmation = (): ConfirmationContextType => {
  const context = useContext(ConfirmationContext);
  if (context === undefined) {
    throw new Error('useConfirmation must be used within a ConfirmationProvider');
  }
  return context;
};
