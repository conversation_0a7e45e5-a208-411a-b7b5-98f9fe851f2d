import { useState, useEffect } from 'react';

// Breakpoints matching Tailwind CSS defaults
const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

type Breakpoint = keyof typeof BREAKPOINTS;

interface MobileState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenWidth: number;
  screenHeight: number;
  orientation: 'portrait' | 'landscape';
  isTouch: boolean;
}

export const useMobile = () => {
  const [mobileState, setMobileState] = useState<MobileState>(() => {
    // Initialize with safe defaults for SSR
    if (typeof window === 'undefined') {
      return {
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        screenWidth: 1024,
        screenHeight: 768,
        orientation: 'landscape' as const,
        isTouch: false,
      };
    }

    const width = window.innerWidth;
    const height = window.innerHeight;
    
    return {
      isMobile: width < BREAKPOINTS.md,
      isTablet: width >= BREAKPOINTS.md && width < BREAKPOINTS.lg,
      isDesktop: width >= BREAKPOINTS.lg,
      screenWidth: width,
      screenHeight: height,
      orientation: width > height ? 'landscape' : 'portrait',
      isTouch: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
    };
  });

  useEffect(() => {
    const updateMobileState = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setMobileState({
        isMobile: width < BREAKPOINTS.md,
        isTablet: width >= BREAKPOINTS.md && width < BREAKPOINTS.lg,
        isDesktop: width >= BREAKPOINTS.lg,
        screenWidth: width,
        screenHeight: height,
        orientation: width > height ? 'landscape' : 'portrait',
        isTouch: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
      });
    };

    // Update on resize
    window.addEventListener('resize', updateMobileState);
    
    // Update on orientation change
    window.addEventListener('orientationchange', () => {
      // Small delay to ensure dimensions are updated
      setTimeout(updateMobileState, 100);
    });

    // Initial update
    updateMobileState();

    return () => {
      window.removeEventListener('resize', updateMobileState);
      window.removeEventListener('orientationchange', updateMobileState);
    };
  }, []);

  // Helper functions
  const isBreakpoint = (breakpoint: Breakpoint): boolean => {
    return mobileState.screenWidth >= BREAKPOINTS[breakpoint];
  };

  const isBreakpointDown = (breakpoint: Breakpoint): boolean => {
    return mobileState.screenWidth < BREAKPOINTS[breakpoint];
  };

  const isBreakpointBetween = (min: Breakpoint, max: Breakpoint): boolean => {
    return mobileState.screenWidth >= BREAKPOINTS[min] && mobileState.screenWidth < BREAKPOINTS[max];
  };

  return {
    ...mobileState,
    isBreakpoint,
    isBreakpointDown,
    isBreakpointBetween,
    // Convenience getters
    get isSmallMobile() {
      return mobileState.screenWidth < 480;
    },
    get isLargeMobile() {
      return mobileState.screenWidth >= 480 && mobileState.screenWidth < BREAKPOINTS.md;
    },
    get isSmallTablet() {
      return mobileState.screenWidth >= BREAKPOINTS.md && mobileState.screenWidth < 900;
    },
    get isLargeTablet() {
      return mobileState.screenWidth >= 900 && mobileState.screenWidth < BREAKPOINTS.lg;
    },
  };
};

// Hook for responsive values based on screen size
export const useResponsiveValue = <T>(values: {
  mobile?: T;
  tablet?: T;
  desktop?: T;
  default: T;
}): T => {
  const { isMobile, isTablet, isDesktop } = useMobile();

  if (isMobile && values.mobile !== undefined) {
    return values.mobile;
  }
  
  if (isTablet && values.tablet !== undefined) {
    return values.tablet;
  }
  
  if (isDesktop && values.desktop !== undefined) {
    return values.desktop;
  }

  return values.default;
};

// Hook for responsive grid columns
export const useResponsiveColumns = (options: {
  mobile?: number;
  tablet?: number;
  desktop?: number;
  default?: number;
} = {}) => {
  const {
    mobile = 1,
    tablet = 2,
    desktop = 3,
    default: defaultCols = 1
  } = options;

  return useResponsiveValue({
    mobile,
    tablet,
    desktop,
    default: defaultCols,
  });
};

// Hook for responsive items per page
export const useResponsiveItemsPerPage = (options: {
  mobile?: number;
  tablet?: number;
  desktop?: number;
  default?: number;
} = {}) => {
  const {
    mobile = 10,
    tablet = 15,
    desktop = 20,
    default: defaultItems = 10
  } = options;

  return useResponsiveValue({
    mobile,
    tablet,
    desktop,
    default: defaultItems,
  });
};

// Hook for detecting if user prefers reduced motion
export const useReducedMotion = (): boolean => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
};

// Hook for safe area insets (for devices with notches, etc.)
export const useSafeAreaInsets = () => {
  const [insets, setInsets] = useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  });

  useEffect(() => {
    const updateInsets = () => {
      const computedStyle = getComputedStyle(document.documentElement);
      
      setInsets({
        top: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-top)') || '0'),
        right: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-right)') || '0'),
        bottom: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
        left: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-left)') || '0'),
      });
    };

    updateInsets();
    window.addEventListener('resize', updateInsets);
    window.addEventListener('orientationchange', updateInsets);

    return () => {
      window.removeEventListener('resize', updateInsets);
      window.removeEventListener('orientationchange', updateInsets);
    };
  }, []);

  return insets;
};
