import { useState, useMemo } from 'react';

// Add 'none' as a possible sort direction
type SortDirection = 'asc' | 'desc' | 'none';

interface SortConfig {
  field: string;
  direction: SortDirection;
}

interface UseSortableDataResult<T> {
  items: T[];
  requestSort: (field: string) => void;
  sortConfig: SortConfig;
}

/**
 * Custom hook for sorting data in tables
 * @param items The array of items to sort
 * @param defaultSortField The default field to sort by
 * @param defaultDirection The default sort direction
 * @param sortFunctions Optional custom sort functions for specific fields
 */
export function useSortableData<T>(
  items: T[],
  defaultSortField: string = 'id',
  defaultDirection: SortDirection = 'desc',
  sortFunctions?: Record<string, (a: T, b: T) => number>
): UseSortableDataResult<T> {
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    field: defaultSortField,
    direction: defaultDirection
  });

  const requestSort = (field: string) => {
    setSortConfig(prevConfig => {
      if (prevConfig.field === field) {
        // Cycle through the three states: asc -> desc -> none
        if (prevConfig.direction === 'asc') {
          return {
            ...prevConfig,
            direction: 'desc'
          };
        } else if (prevConfig.direction === 'desc') {
          return {
            field: defaultSortField, // Reset to default field
            direction: 'none'        // With no sorting
          };
        } else {
          // If it was 'none', start with ascending
          return {
            field,
            direction: 'asc'
          };
        }
      } else {
        // New field, start with ascending
        return {
          field,
          direction: 'asc'
        };
      }
    });
  };

  const sortedItems = useMemo(() => {
    // If direction is 'none', return items without sorting
    if (sortConfig.direction === 'none') {
      return [...items];
    }

    const sortableItems = [...items];

    sortableItems.sort((a, b) => {
      // Use custom sort function if provided for this field
      if (sortFunctions && sortFunctions[sortConfig.field]) {
        return sortConfig.direction === 'asc'
          ? sortFunctions[sortConfig.field](a, b)
          : sortFunctions[sortConfig.field](b, a);
      }

      // Default sorting logic
      const aValue = (a as any)[sortConfig.field];
      const bValue = (b as any)[sortConfig.field];

      if (aValue === undefined || bValue === undefined) {
        return 0;
      }

      // Handle different data types
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortConfig.direction === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortConfig.direction === 'asc'
          ? aValue - bValue
          : bValue - aValue;
      }

      if (aValue instanceof Date && bValue instanceof Date) {
        return sortConfig.direction === 'asc'
          ? aValue.getTime() - bValue.getTime()
          : bValue.getTime() - aValue.getTime();
      }

      // Convert to string as fallback
      const aString = String(aValue);
      const bString = String(bValue);

      return sortConfig.direction === 'asc'
        ? aString.localeCompare(bString)
        : bString.localeCompare(aString);
    });

    return sortableItems;
  }, [items, sortConfig, sortFunctions]);

  return {
    items: sortedItems,
    requestSort,
    sortConfig
  };
}

export default useSortableData;
