import React from 'react';
import AuditLogViewer from '../components/AuditLogViewer';
import { FaHistory } from 'react-icons/fa';
import { MobileContainer, MobilePageHeader } from '../components/common/MobileUtils';
import { useMobile } from '../hooks/useMobile';

const AuditLogs: React.FC = () => {
  const { isMobile } = useMobile();

  return (
    <MobileContainer>
      <MobilePageHeader
        title="Audit Logs"
        subtitle="View a comprehensive history of all actions performed in the system. Audit logs help track changes and maintain accountability."
      />

      <div className="bg-blue-100 border-l-4 border-blue-500 text-blue-700 dark:bg-blue-900/20 dark:border-blue-400 dark:text-blue-200 p-4 mb-6 rounded-r-lg">
        <p className="font-bold">Security Information</p>
        <p>All sensitive operations are logged with details about who performed the action, when it occurred, and what changes were made.</p>
      </div>

      <AuditLogViewer />
    </MobileContainer>
  );
};

export default AuditLogs;
