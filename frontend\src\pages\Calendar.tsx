import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../context/AuthContext';
import { getCalendarEvents, createEvent, updateEvent, completeEvent, getDashboardMetrics, DashboardMetrics } from '../services/eventService';
import { getAllUsers } from '../services/userService';
import { getAllCustomersNoPage } from '../services/customerService';
import { Event } from '../types/event';
import { User } from '../types/user';
import { Customer } from '../types/customer';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import listPlugin from '@fullcalendar/list';
import interactionPlugin from '@fullcalendar/interaction';
import LoadingSpinner from '../components/LoadingSpinner';
import EventModal from '../components/EventModal';
import EventDetailsModal from '../components/EventDetailsModal';
import MetricsCard from '../components/dashboard/MetricsCard';
import {
  FaCalendarAlt,
  FaFilter,
  FaPlus,
  FaClock,
  FaCheckCircle,
  FaExclamationTriangle,
  FaUsers,
  FaChartBar,
  FaEye,
  FaList,
  FaTh,
  FaCalendarWeek,
  FaSearch,
  FaTimes
} from 'react-icons/fa';
import { toast } from 'react-toastify';
import { useConfirmation } from '../context/ConfirmationContext';
import { useLocation, useNavigate } from 'react-router-dom';

const Calendar: React.FC = () => {
  const { user } = useAuth();
  const { showConfirmation } = useConfirmation();
  const location = useLocation();
  const navigate = useNavigate();
  const calendarRef = useRef<FullCalendar>(null);
  const [events, setEvents] = useState<Event[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [editingEvent, setEditingEvent] = useState<Event | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [showEventDetails, setShowEventDetails] = useState(false);
  const [newEvent, setNewEvent] = useState({
    customer_id: null as number | null,
    event_type: '',
    description: '',
    scheduled_date: '',
    user_ids: [] as number[]
  });
  const [selectedUserId, setSelectedUserId] = useState<number | null>(
    user?.role !== 'administrator' ? user?.id : null
  );
  const [selectedCustomerId, setSelectedCustomerId] = useState<number | null>(null);
  const [submitting, setSubmitting] = useState(false);

  // Enhanced calendar features
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [calendarView, setCalendarView] = useState('dayGridMonth');
  const [eventTypeFilter, setEventTypeFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState<string>('');

  // Get customerId from URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const customerIdParam = urlParams.get('customerId');
    if (customerIdParam) {
      setSelectedCustomerId(parseInt(customerIdParam));
    }
  }, [location.search]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch events for calendar
        const eventsResponse = await getCalendarEvents(
          undefined,
          undefined,
          selectedUserId || undefined,
          selectedCustomerId || undefined
        );
        setEvents(eventsResponse.events);

        // Fetch users for filtering (admin only)
        if (user?.role === 'administrator') {
          const usersResponse = await getAllUsers();
          setUsers(usersResponse.users);
        }

        // Fetch customers for event creation and filtering
        const customersResponse = await getAllCustomersNoPage();
        setCustomers(customersResponse.customers);

        // Fetch dashboard metrics for analytics (admin only)
        if (user?.role === 'administrator') {
          try {
            const metricsResponse = await getDashboardMetrics();
            setMetrics(metricsResponse);
          } catch (metricsErr) {
            console.error('Error fetching metrics:', metricsErr);
            // Don't fail the whole page if metrics fail
          }
        }

      } catch (err) {
        console.error('Error fetching calendar data:', err);
        setError('Failed to load calendar data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user, selectedUserId, selectedCustomerId]);

  const handleDateClick = (info: any) => {
    if (user?.role === 'administrator') {
      const clickedDate = new Date(info.dateStr);
      // Set time to noon
      clickedDate.setHours(12, 0, 0);

      setEditingEvent(null); // Clear editing state for new event
      setNewEvent({
        customer_id: null,
        event_type: '',
        description: '',
        scheduled_date: clickedDate.toISOString().slice(0, 16),
        user_ids: []
      });

      setShowModal(true);
    }
  };

  const handleEventClick = (info: any) => {
    const eventId = parseInt(info.event.id);
    const clickedEvent = events.find(e => e.id === eventId);

    if (clickedEvent) {
      setSelectedEvent(clickedEvent);
      setShowEventDetails(true);
    }
  };

  const handleEventEdit = () => {
    if (selectedEvent) {
      setEditingEvent(selectedEvent);
      setNewEvent({
        customer_id: selectedEvent.customer_id,
        event_type: selectedEvent.event_type,
        description: selectedEvent.description,
        scheduled_date: selectedEvent.scheduled_date.slice(0, 16),
        user_ids: selectedEvent.user_ids || []
      });
      setShowEventDetails(false);
      setShowModal(true);
    }
  };

  const handleEventComplete = () => {
    if (selectedEvent) {
      setShowEventDetails(false);
      handleCompleteEvent(selectedEvent.id);
    }
  };

  const handleCompleteEvent = async (eventId: number) => {
    try {
      await completeEvent(eventId);

      // Update the local events state
      setEvents(prevEvents =>
        prevEvents.map(event =>
          event.id === eventId
            ? { ...event, status: 'completed', completed_at: new Date().toISOString() }
            : event
        )
      );

      toast.success('Event marked as completed');
    } catch (err) {
      console.error('Error completing event:', err);
      toast.error('Failed to complete event');
    }
  };

  const handleCreateEvent = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const formattedDate = new Date(newEvent.scheduled_date).toISOString();
      const createdEvent = await createEvent(
        newEvent.customer_id,
        newEvent.event_type,
        newEvent.description,
        formattedDate,
        newEvent.user_ids.length > 0 ? newEvent.user_ids : null
      );

      setEvents([...events, createdEvent]);
      setShowModal(false);
      setEditingEvent(null);
      toast.success('Event created successfully');
    } catch (err) {
      console.error('Error creating event:', err);
      setError('Failed to create event');
      toast.error('Failed to create event');
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdateEvent = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingEvent) return;
    setSubmitting(true);

    try {
      const formattedDate = new Date(newEvent.scheduled_date).toISOString();
      const updatedEvent = await updateEvent(
        editingEvent.id,
        newEvent.customer_id !== null ? newEvent.customer_id : 0,
        newEvent.event_type,
        newEvent.description,
        formattedDate,
        newEvent.user_ids.length > 0 ? newEvent.user_ids : undefined
      );

      setEvents(events.map((e) => (e.id === editingEvent.id ? updatedEvent : e)));
      setShowModal(false);
      setEditingEvent(null);
      setNewEvent({
        customer_id: null,
        event_type: '',
        description: '',
        scheduled_date: '',
        user_ids: []
      });
      toast.success('Event updated successfully');
    } catch (err) {
      console.error('Error updating event:', err);
      setError('Failed to update event');
      toast.error('Failed to update event');
    } finally {
      setSubmitting(false);
    }
  };

  const handleUserFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setSelectedUserId(value === '' ? null : parseInt(value));
  };

  const handleCustomerFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    const customerId = value === '' ? null : parseInt(value);
    setSelectedCustomerId(customerId);

    // Update URL parameters
    const urlParams = new URLSearchParams(location.search);
    if (customerId) {
      urlParams.set('customerId', customerId.toString());
    } else {
      urlParams.delete('customerId');
    }

    const newSearch = urlParams.toString();
    const newUrl = newSearch ? `${location.pathname}?${newSearch}` : location.pathname;
    navigate(newUrl, { replace: true });
  };

  const getEventColor = (event: Event) => {
    if (event.status === 'completed') {
      return '#10b981'; // Green for completed events
    }

    // Check if document has expiry status
    if (event.document?.expiry_status) {
      switch (event.document.expiry_status) {
        case 'red':
          return '#ef4444'; // Red for urgent
        case 'orange':
          return '#f97316'; // Orange for warning
        default:
          return '#3b82f6'; // Blue for normal
      }
    }

    return '#3b82f6'; // Default blue
  };

  // Filter events based on current filters
  const getFilteredEvents = () => {
    return events.filter(event => {
      // Event type filter
      if (eventTypeFilter && event.event_type !== eventTypeFilter) {
        return false;
      }

      // Status filter
      if (statusFilter && event.status !== statusFilter) {
        return false;
      }

      // Search term filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        return (
          event.description.toLowerCase().includes(searchLower) ||
          event.event_type.toLowerCase().includes(searchLower) ||
          (event.customer_name && event.customer_name.toLowerCase().includes(searchLower))
        );
      }

      return true;
    });
  };

  // Get calendar analytics
  const getCalendarAnalytics = () => {
    const filteredEvents = getFilteredEvents();
    const now = new Date();
    const oneWeekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

    const pending = filteredEvents.filter(e => e.status === 'pending').length;
    const completed = filteredEvents.filter(e => e.status === 'completed').length;
    const upcoming = filteredEvents.filter(e =>
      e.status === 'pending' &&
      new Date(e.scheduled_date) <= oneWeekFromNow &&
      new Date(e.scheduled_date) >= now
    ).length;
    const overdue = filteredEvents.filter(e =>
      e.status === 'pending' &&
      new Date(e.scheduled_date) < now
    ).length;

    return { pending, completed, upcoming, overdue, total: filteredEvents.length };
  };

  if (loading) {
    return <LoadingSpinner message="Loading calendar..." />;
  }

  const analytics = getCalendarAnalytics();

  return (
    <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 space-y-4 lg:space-y-0">
        <div className="flex items-center">
          <FaCalendarAlt className="text-amspm-primary mr-3" size={30} />
          <div>
            <h1 className="text-3xl font-bold text-amspm-text">Events Calendar</h1>
            <p className="text-sm text-gray-600 mt-1">
              {selectedCustomerId
                ? `Showing events for: ${customers.find(c => c.id === selectedCustomerId)?.name}`
                : `${analytics.total} events • ${analytics.pending} pending • ${analytics.completed} completed`
              }
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowAnalytics(!showAnalytics)}
            className={`btn ${showAnalytics ? 'btn-primary' : 'btn-outline'} flex items-center text-sm`}
          >
            <FaChartBar className="mr-2" />
            Analytics
          </button>

          {user?.role === 'administrator' && (
            <button
              onClick={() => {
                setEditingEvent(null);
                setNewEvent({
                  customer_id: selectedCustomerId,
                  event_type: '',
                  description: '',
                  scheduled_date: new Date().toISOString().slice(0, 16),
                  user_ids: []
                });
                setShowModal(true);
              }}
              className="btn btn-secondary flex items-center text-sm"
            >
              <FaPlus className="mr-2" /> Create Event
            </button>
          )}
        </div>
      </div>

      {/* Analytics Panel */}
      {showAnalytics && user?.role === 'administrator' && (
        <div className="mb-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <MetricsCard
              title="Total Events"
              value={analytics.total}
              icon={<FaCalendarAlt size={20} />}
              subtext="All events in view"
            />
            <MetricsCard
              title="Pending"
              value={analytics.pending}
              icon={<FaClock size={20} />}
              color="text-orange-500"
              subtext="Awaiting completion"
            />
            <MetricsCard
              title="Completed"
              value={analytics.completed}
              icon={<FaCheckCircle size={20} />}
              color="text-green-500"
              subtext="Successfully finished"
            />
            <MetricsCard
              title="Overdue"
              value={analytics.overdue}
              icon={<FaExclamationTriangle size={20} />}
              color="text-red-500"
              subtext="Past due date"
            />
          </div>
        </div>
      )}

      {/* Enhanced Filters */}
      <div className="card bg-white shadow-sm mb-6">
        <div className="card-content">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Left side filters */}
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
              {/* Search */}
              <div className="flex items-center space-x-2">
                <FaSearch className="text-gray-400" />
                <input
                  type="text"
                  placeholder="Search events..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input py-1 px-3 text-sm w-full sm:w-48"
                />
                {searchTerm && (
                  <button
                    onClick={() => setSearchTerm('')}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FaTimes />
                  </button>
                )}
              </div>

              {/* Event Type Filter */}
              <div className="flex items-center space-x-2">
                <FaFilter className="text-gray-400" />
                <select
                  value={eventTypeFilter}
                  onChange={(e) => setEventTypeFilter(e.target.value)}
                  className="input py-1 px-2 text-sm"
                >
                  <option value="">All Types</option>
                  <option value="offerte">Offerte</option>
                  <option value="werkbon">Werkbon</option>
                  <option value="onderhoudsbon">Onderhoudsbon</option>
                  <option value="onderhoudscontract">Onderhoudscontract</option>
                  <option value="meldkamercontract">Meldkamercontract</option>
                  <option value="beveiligingscertificaat">Beveiligingscertificaat</option>
                  <option value="intakedocument">Intakedocument</option>
                  <option value="projectietekening">Projectietekening</option>
                  <option value="beveiligingsplan">Beveiligingsplan</option>
                  <option value="kabeltekeningen">Kabeltekeningen</option>
                  <option value="checklist oplevering installatie">Checklist Oplevering</option>
                </select>
              </div>

              {/* Status Filter */}
              <div className="flex items-center space-x-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="input py-1 px-2 text-sm"
                >
                  <option value="">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="completed">Completed</option>
                </select>
              </div>
            </div>

            {/* Right side filters */}
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
              {/* User Filter (Admin only) */}
              {user?.role === 'administrator' && (
                <div className="flex items-center space-x-2">
                  <FaUsers className="text-gray-400" />
                  <select
                    value={selectedUserId?.toString() || ''}
                    onChange={handleUserFilterChange}
                    className="input py-1 px-2 text-sm"
                  >
                    <option value="">All Users</option>
                    {users.map(u => (
                      <option key={u.id} value={u.id.toString()}>
                        {u.name || u.email}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Customer Filter */}
              <div className="flex items-center space-x-2">
                <select
                  value={selectedCustomerId?.toString() || ''}
                  onChange={handleCustomerFilterChange}
                  className="input py-1 px-2 text-sm"
                >
                  <option value="">All Customers</option>
                  {customers.map(c => (
                    <option key={c.id} value={c.id.toString()}>
                      {c.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* View Toggle */}
              <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setCalendarView('dayGridMonth')}
                  className={`p-2 rounded ${calendarView === 'dayGridMonth' ? 'bg-white shadow-sm' : ''}`}
                  title="Month View"
                >
                  <FaTh size={14} />
                </button>
                <button
                  onClick={() => setCalendarView('timeGridWeek')}
                  className={`p-2 rounded ${calendarView === 'timeGridWeek' ? 'bg-white shadow-sm' : ''}`}
                  title="Week View"
                >
                  <FaCalendarWeek size={14} />
                </button>
                <button
                  onClick={() => setCalendarView('listWeek')}
                  className={`p-2 rounded ${calendarView === 'listWeek' ? 'bg-white shadow-sm' : ''}`}
                  title="List View"
                >
                  <FaList size={14} />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {error && <p className="text-red-500 mb-4">{error}</p>}

      <div className="card bg-white shadow-sm p-0 overflow-hidden">
        <div className="p-4">
          <FullCalendar
            ref={calendarRef}
            plugins={[dayGridPlugin, timeGridPlugin, listPlugin, interactionPlugin]}
            initialView={calendarView}
            headerToolbar={{
              left: 'prev,next today',
              center: 'title',
              right: 'dayGridMonth,timeGridWeek,listWeek'
            }}
            events={getFilteredEvents().map(event => ({
              id: event.id.toString(),
              title: `${event.event_type}${event.customer_name ? ': ' + event.customer_name : ''}`,
              start: event.scheduled_date,
              end: event.scheduled_date,
              backgroundColor: getEventColor(event),
              borderColor: getEventColor(event),
              classNames: [event.status === 'completed' ? 'completed-event' : 'pending-event'],
              extendedProps: {
                description: event.description,
                status: event.status,
                customer: event.customer_name,
                type: event.event_type
              }
            }))}
            eventTimeFormat={{
              hour: '2-digit',
              minute: '2-digit',
              meridiem: false
            }}
            eventContent={(eventInfo) => {
              const event = events.find(e => e.id.toString() === eventInfo.event.id);
              const isListView = calendarView === 'listWeek';

              return (
                <div className="fc-event-main-frame" style={{ padding: '2px' }}>
                  <div className="fc-event-title-container">
                    <div className="fc-event-title font-medium">
                      {eventInfo.event.extendedProps.type}
                    </div>
                    {eventInfo.event.extendedProps.customer && (
                      <div className="text-xs overflow-hidden text-ellipsis whitespace-nowrap opacity-90">
                        {eventInfo.event.extendedProps.customer}
                      </div>
                    )}
                    {isListView && event && (
                      <div className="text-xs mt-1 space-y-1">
                        {event.user_names && event.user_names.length > 0 && (
                          <div className="flex items-center">
                            <FaUsers className="mr-1" size={10} />
                            <span>{event.user_names.join(', ')}</span>
                          </div>
                        )}
                        {event.status === 'pending' && (
                          <div className="flex items-center text-orange-600">
                            <FaClock className="mr-1" size={10} />
                            <span>Pending</span>
                          </div>
                        )}
                        {event.status === 'completed' && (
                          <div className="flex items-center text-green-600">
                            <FaCheckCircle className="mr-1" size={10} />
                            <span>Completed</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              );
            }}
            dateClick={handleDateClick}
            eventClick={handleEventClick}
            height="auto"
            dayMaxEvents={4}
            eventMaxStack={3}
            moreLinkClick="popover"
            fixedWeekCount={false}
            aspectRatio={calendarView === 'listWeek' ? 1.2 : 1.8}
            viewDidMount={(info) => setCalendarView(info.view.type)}
            slotMinTime="06:00:00"
            slotMaxTime="22:00:00"
            allDaySlot={false}
            nowIndicator={true}
          />
        </div>
      </div>

      {/* Enhanced Legend and Summary */}
      <div className="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Color Legend */}
        <div className="card bg-white shadow-sm">
          <div className="card-content">
            <h3 className="text-lg font-semibold text-amspm-text mb-3">Event Status Legend</h3>
            <div className="grid grid-cols-2 gap-2">
              <div className="flex items-center">
                <span className="inline-block w-4 h-4 rounded-full bg-blue-500 mr-2"></span>
                <span className="text-sm">Normal Event</span>
              </div>
              <div className="flex items-center">
                <span className="inline-block w-4 h-4 rounded-full bg-orange-500 mr-2"></span>
                <span className="text-sm">Warning (Expiring Soon)</span>
              </div>
              <div className="flex items-center">
                <span className="inline-block w-4 h-4 rounded-full bg-red-500 mr-2"></span>
                <span className="text-sm">Urgent (Expired)</span>
              </div>
              <div className="flex items-center">
                <span className="inline-block w-4 h-4 rounded-full bg-green-500 mr-2"></span>
                <span className="text-sm">Completed</span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Insights */}
        <div className="card bg-white shadow-sm">
          <div className="card-content">
            <h3 className="text-lg font-semibold text-amspm-text mb-3">Quick Insights</h3>
            <div className="space-y-2 text-sm">
              {analytics.overdue > 0 && (
                <div className="flex items-center text-red-600">
                  <FaExclamationTriangle className="mr-2" size={14} />
                  <span>{analytics.overdue} overdue events need attention</span>
                </div>
              )}
              {analytics.upcoming > 0 && (
                <div className="flex items-center text-orange-600">
                  <FaClock className="mr-2" size={14} />
                  <span>{analytics.upcoming} events due this week</span>
                </div>
              )}
              {analytics.completed > 0 && (
                <div className="flex items-center text-green-600">
                  <FaCheckCircle className="mr-2" size={14} />
                  <span>{analytics.completed} events completed</span>
                </div>
              )}
              {analytics.total === 0 && (
                <div className="flex items-center text-gray-500">
                  <FaCalendarAlt className="mr-2" size={14} />
                  <span>No events match current filters</span>
                </div>
              )}
              {searchTerm && (
                <div className="flex items-center text-blue-600">
                  <FaSearch className="mr-2" size={14} />
                  <span>Showing results for "{searchTerm}"</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {showModal && (
        <EventModal
          event={newEvent}
          onClose={() => {
            setShowModal(false);
            setEditingEvent(null);
          }}
          onSubmit={editingEvent ? handleUpdateEvent : handleCreateEvent}
          setEvent={setNewEvent}
          isEditing={!!editingEvent}
          submitting={submitting}
          customers={customers}
          users={users}
        />
      )}

      {showEventDetails && selectedEvent && (
        <EventDetailsModal
          event={selectedEvent}
          onClose={() => {
            setShowEventDetails(false);
            setSelectedEvent(null);
          }}
          onEdit={handleEventEdit}
          onComplete={handleEventComplete}
          canEdit={user?.role === 'administrator' && selectedEvent.status === 'pending'}
          canComplete={selectedEvent.status === 'pending'}
        />
      )}
    </div>
  );
};

export default Calendar;
