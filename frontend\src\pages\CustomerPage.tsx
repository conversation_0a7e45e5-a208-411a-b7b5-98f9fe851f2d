import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { getCustomerById, updateCustomer } from '../services/customerService';
import { Customer } from '../types/customer';
import { useAuth } from '../context/AuthContext';
import { useConfirmation } from '../context/ConfirmationContext';
import LoadingSpinner from '../components/LoadingSpinner';
import Breadcrumbs from '../components/Breadcrumbs';
import CustomerNotes from '../components/CustomerNotes';
import { FaUser, FaFileAlt, FaEdit, FaCalendarAlt, FaHistory, FaChartBar } from 'react-icons/fa';
import { useTheme } from '../context/ThemeContext';
import { ThemeProvider

 } from '../context/ThemeContext';
const CustomerPage: React.FC = () => {
  const { customerId } = useParams<{ customerId: string }>();
  const { user } = useAuth();
  const navigate = useNavigate();
  const { showConfirmation } = useConfirmation();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCustomer = async () => {
      if (!customerId) return;

      try {
        setLoading(true);
        const data = await getCustomerById(parseInt(customerId));
        setCustomer(data);
        setError(null);
      } catch (err: any) {
        setError(err.response?.data?.error || err.message || 'Failed to fetch customer data');
      } finally {
        setLoading(false);
      }
    };

    fetchCustomer();
  }, [customerId]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <LoadingSpinner message="Loading customer data..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
        <button
          className="btn btn-primary"
          onClick={() => navigate('/customers')}
        >
          Back to Customers
        </button>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
          Customer not found
        </div>
        <button
          className="btn btn-primary"
          onClick={() => navigate('/customers')}
        >
          Back to Customers
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4">
      {/* Breadcrumbs */}
      <Breadcrumbs
        customerName={customer.name}
        customerId={customer.id}
      />

      {/* Customer Header */}
      <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div className="mb-4 md:mb-0">
            <h1 className="text-3xl font-bold text-amspm-text">
              {customer.name}
            </h1>
            <p className="text-gray-600 dark:text-dark-text-light mt-1">
              Customer ID: {customer.id} | KvK: {customer.kvk_number || 'N/A'}
            </p>
          </div>
          <div className="flex flex-wrap gap-2">
            <Link
              to={`/customers/${customer.id}/edit`}
              className="btn btn-outline"
            >
              <FaEdit className="mr-2" /> Edit Customer
            </Link>
          </div>
        </div>
      </div>

      {/* Customer Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
          <h2 className="text-xl font-semibold text-amspm-text mb-4 flex items-center">
            <FaUser className="mr-2 text-amspm-primary" /> Contact Information
          </h2>
          <div className="space-y-3">
            <div>
              <p className="text-sm text-gray-500 dark:text-dark-text-light">Contact Person</p>
              <p className="text-amspm-text dark:text-dark-text">{customer.contact_person || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-dark-text-light">Email</p>
              <p className="text-amspm-text dark:text-dark-text">{customer.email || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-dark-text-light">Phone</p>
              <p className="text-amspm-text dark:text-dark-text">{customer.phone || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-dark-text-light">Address</p>
              <p className="text-amspm-text dark:text-dark-text">
                {customer.address || 'N/A'}{customer.address && customer.city ? ', ' : ''}{customer.city || ''}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
          <h2 className="text-xl font-semibold text-amspm-text mb-4 flex items-center">
            <FaChartBar className="mr-2 text-amspm-primary" /> Customer Details
          </h2>
          <div className="space-y-3">
            <div>
              <p className="text-sm text-gray-500 dark:text-dark-text-light">Customer Code</p>
              <p className="text-amspm-text dark:text-dark-text">{customer.code || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-dark-text-light">Created At</p>
              <p className="text-amspm-text dark:text-dark-text">
                {customer.created_at ? new Date(customer.created_at).toLocaleDateString() : 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-dark-text-light">Last Updated</p>
              <p className="text-amspm-text dark:text-dark-text">
                {customer.updated_at ? new Date(customer.updated_at).toLocaleDateString() : 'N/A'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <h2 className="text-2xl font-semibold text-amspm-text mb-4">Quick Actions</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <Link
          to={`/customers/${customer.id}/documents`}
          className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6 hover:shadow-lg transition-shadow flex flex-col items-center text-center"
        >
          <FaFileAlt className="text-4xl text-amspm-primary mb-3" />
          <h3 className="text-lg font-semibold text-amspm-text">Documents</h3>
          <p className="text-gray-600 dark:text-dark-text-light mt-2">
            View and manage customer documents
          </p>
        </Link>

        <Link
          to={`/calendar?customerId=${customer.id}`}
          className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6 hover:shadow-lg transition-shadow flex flex-col items-center text-center"
        >
          <FaCalendarAlt className="text-4xl text-amspm-primary mb-3" />
          <h3 className="text-lg font-semibold text-amspm-text">Events</h3>
          <p className="text-gray-600 dark:text-dark-text-light mt-2">
            View and schedule events for this customer
          </p>
        </Link>

        <Link
          to={`/customers/${customer.id}/history`}
          className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6 hover:shadow-lg transition-shadow flex flex-col items-center text-center"
        >
          <FaHistory className="text-4xl text-amspm-primary mb-3" />
          <h3 className="text-lg font-semibold text-amspm-text">History</h3>
          <p className="text-gray-600 dark:text-dark-text-light mt-2">
            View customer activity history
          </p>
        </Link>
      </div>

      {/* Notes Section */}
      {customer && <CustomerNotes customerId={customer.id} />}
    </div>
  );
};

export default CustomerPage;
