import React from 'react';
import DocumentTemplateManager from '../components/DocumentTemplateManager';
import Breadcrumbs from '../components/Breadcrumbs';
import { FaFileAlt } from 'react-icons/fa';

const DocumentTemplatesPage: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <Breadcrumbs
        items={[
          { label: 'Dashboard', path: '/dashboard' },
          { label: 'Document Templates', path: '/document-templates' },
        ]}
      />
      
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-amspm-text flex items-center">
          <FaFileAlt className="mr-2" /> Document Templates
        </h1>
        <p className="text-gray-600 dark:text-dark-text-light mt-2">
          Manage document templates that can be filled in and uploaded to customers.
        </p>
      </div>
      
      <DocumentTemplateManager />
    </div>
  );
};

export default DocumentTemplatesPage;
