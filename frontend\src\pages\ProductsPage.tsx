import React, { useState } from 'react';
import { FaBoxOpen, FaFileExcel } from 'react-icons/fa';
import Breadcrumbs from '../components/Breadcrumbs';
import ProductList from '../components/products/ProductList';
import ProductImport from '../components/products/ProductImport';
import { useAuth } from '../context/AuthContext';
import { MobileContainer, MobileButtonGroup, MobilePageHeader } from '../components/common/MobileUtils';
import { useMobile } from '../hooks/useMobile';

const ProductsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'list' | 'import'>('list');
  const { user } = useAuth();
  const { isMobile } = useMobile();
  const isAdmin = user?.role === 'administrator';

  return (
    <MobileContainer>
      <Breadcrumbs
        items={[
          { label: 'Dashboard', path: '/dashboard' },
          { label: 'Producten', path: '/products' },
        ]}
      />

      <MobilePageHeader
        title="Producten"
        subtitle="Beheer producten voor offertes en prijslijsten."
      />

      <div className="mb-4 sm:mb-6">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex flex-col sm:flex-row sm:space-x-8 space-y-2 sm:space-y-0">
            <button
              onClick={() => setActiveTab('list')}
              className={`mobile-touch-target py-3 sm:py-4 px-3 sm:px-1 border-b-2 font-medium text-sm rounded-t-lg sm:rounded-none ${
                activeTab === 'list'
                  ? 'border-amspm-primary text-amspm-primary dark:border-dark-accent dark:text-dark-accent bg-amspm-primary/10 dark:bg-dark-accent/10'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <FaBoxOpen className="inline-block mr-2" /> Productenlijst
            </button>

            {isAdmin && (
              <button
                onClick={() => setActiveTab('import')}
                className={`mobile-touch-target py-3 sm:py-4 px-3 sm:px-1 border-b-2 font-medium text-sm rounded-t-lg sm:rounded-none ${
                  activeTab === 'import'
                    ? 'border-amspm-primary text-amspm-primary dark:border-dark-accent dark:text-dark-accent bg-amspm-primary/10 dark:bg-dark-accent/10'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <FaFileExcel className="inline-block mr-2" /> OSEC Prijslijst Importeren
              </button>
            )}
          </nav>
        </div>
      </div>

      {activeTab === 'list' ? (
        <ProductList />
      ) : (
        <ProductImport onImportSuccess={() => setActiveTab('list')} />
      )}
    </MobileContainer>
  );
};

export default ProductsPage;
