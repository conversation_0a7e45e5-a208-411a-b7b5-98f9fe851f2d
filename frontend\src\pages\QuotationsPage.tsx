import React from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import { FaFileInvoiceDollar } from 'react-icons/fa';
import Breadcrumbs from '../components/Breadcrumbs';
import QuotationList from '../components/quotations/QuotationList';
import QuotationForm from '../components/quotations/QuotationForm';
import QuotationDetail from '../components/quotations/QuotationDetail';
import { MobileContainer, MobilePageHeader } from '../components/common/MobileUtils';
import { useMobile } from '../hooks/useMobile';

const QuotationsPage: React.FC = () => {
  const location = useLocation();
  const { isMobile } = useMobile();
  const isListView = location.pathname === '/quotations';

  return (
    <MobileContainer>
      <Breadcrumbs />

      {isListView && (
        <MobilePageHeader
          title="Offertes"
          subtitle="Beheer offertes voor klanten."
        />
      )}

      <Routes>
        <Route path="/" element={<QuotationList />} />
        <Route path="/new" element={<QuotationForm />} />
        <Route path="/:id" element={<QuotationDetail />} />
        <Route path="/:id/edit" element={<QuotationForm />} />
      </Routes>
    </MobileContainer>
  );
};

export default QuotationsPage;
