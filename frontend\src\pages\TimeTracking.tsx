import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useConfirmation } from '../context/ConfirmationContext';
import { TimeEntry } from '../types/timeEntry';
import { MileageEntry } from '../types/mileageEntry';
import {
  getTimeEntryById,
  getMileageEntryById,
  createTimeEntry,
  createMileageEntry,
  updateTimeEntry,
  updateMileageEntry,
  deleteTimeEntry,
  deleteMileageEntry
} from '../services/timeTrackingService';
import TimeEntryForm from '../components/timetracking/TimeEntryForm';
import MileageEntryForm from '../components/timetracking/MileageEntryForm';
import MonthlyTimesheet from '../components/timetracking/MonthlyTimesheet';
import LoadingSpinner from '../components/LoadingSpinner';
import { FaClock, FaCar, FaPlus, FaEdit, FaTrash, FaTimes } from 'react-icons/fa';
import { MobileContainer, MobileButtonGroup, MobilePageHeader } from '../components/common/MobileUtils';
import { useMobile } from '../hooks/useMobile';

const TimeTracking: React.FC = () => {
  const { user } = useAuth();
  const { showConfirmation } = useConfirmation();
  const { isMobile } = useMobile();

  const [activeTab, setActiveTab] = useState<'timesheet' | 'addTime' | 'addMileage'>('timesheet');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeEntry, setSelectedTimeEntry] = useState<TimeEntry | null>(null);
  const [selectedMileageEntry, setSelectedMileageEntry] = useState<MileageEntry | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  const handleAddTimeClick = () => {
    setSelectedTimeEntry(null);
    setSelectedMileageEntry(null);
    setIsEditing(false);
    setActiveTab('addTime');
  };

  const handleAddMileageClick = () => {
    setSelectedTimeEntry(null);
    setSelectedMileageEntry(null);
    setIsEditing(false);
    setActiveTab('addMileage');
  };

  const handleTimeEntrySelect = async (entry: TimeEntry) => {
    try {
      setLoading(true);
      setError(null);

      // Get the full entry details
      const fullEntry = await getTimeEntryById(entry.id);
      setSelectedTimeEntry(fullEntry);
      setSelectedMileageEntry(null);
      setIsEditing(false);

      // Show the entry details modal or section
      // For now, we'll just switch to the edit tab
      setActiveTab('addTime');
    } catch (err) {
      console.error('Error fetching time entry details:', err);
      setError('Er is een fout opgetreden bij het ophalen van de urendetails.');
    } finally {
      setLoading(false);
    }
  };

  const handleMileageEntrySelect = async (entry: MileageEntry) => {
    try {
      setLoading(true);
      setError(null);

      // Get the full entry details
      const fullEntry = await getMileageEntryById(entry.id);
      setSelectedMileageEntry(fullEntry);
      setSelectedTimeEntry(null);
      setIsEditing(false);

      // Show the entry details modal or section
      // For now, we'll just switch to the edit tab
      setActiveTab('addMileage');
    } catch (err) {
      console.error('Error fetching mileage entry details:', err);
      setError('Er is een fout opgetreden bij het ophalen van de kilometerdetails.');
    } finally {
      setLoading(false);
    }
  };

  const handleTimeEntrySubmit = async (formData: any) => {
    try {
      setLoading(true);
      setError(null);

      if (isEditing && selectedTimeEntry) {
        // Update existing entry
        await updateTimeEntry(selectedTimeEntry.id, formData);
      } else {
        // Create new entry
        await createTimeEntry(formData);
      }

      // Reset form and go back to timesheet
      setSelectedTimeEntry(null);
      setIsEditing(false);
      setActiveTab('timesheet');
    } catch (err) {
      console.error('Error saving time entry:', err);
      setError('Er is een fout opgetreden bij het opslaan van de uren.');
    } finally {
      setLoading(false);
    }
  };

  const handleMileageEntrySubmit = async (formData: any) => {
    try {
      setLoading(true);
      setError(null);

      if (isEditing && selectedMileageEntry) {
        // Update existing entry
        await updateMileageEntry(selectedMileageEntry.id, formData);
      } else {
        // Create new entry
        await createMileageEntry(formData);
      }

      // Reset form and go back to timesheet
      setSelectedMileageEntry(null);
      setIsEditing(false);
      setActiveTab('timesheet');
    } catch (err) {
      console.error('Error saving mileage entry:', err);
      setError('Er is een fout opgetreden bij het opslaan van de kilometers.');
    } finally {
      setLoading(false);
    }
  };

  const handleEditTimeEntry = () => {
    setIsEditing(true);
  };

  const handleEditMileageEntry = () => {
    setIsEditing(true);
  };

  const handleDeleteTimeEntry = () => {
    if (!selectedTimeEntry) return;

    showConfirmation({
      title: 'Uren verwijderen',
      message: 'Weet je zeker dat je deze uren wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.',
      confirmText: 'Verwijderen',
      cancelText: 'Annuleren',
      confirmButtonClass: 'btn-danger',
      onConfirm: async () => {
        try {
          setLoading(true);
          await deleteTimeEntry(selectedTimeEntry.id);

          // Reset and go back to timesheet
          setSelectedTimeEntry(null);
          setIsEditing(false);
          setActiveTab('timesheet');
        } catch (err) {
          console.error('Error deleting time entry:', err);
          setError('Er is een fout opgetreden bij het verwijderen van de uren.');
        } finally {
          setLoading(false);
        }
      }
    });
  };

  const handleDeleteMileageEntry = () => {
    if (!selectedMileageEntry) return;

    showConfirmation({
      title: 'Kilometers verwijderen',
      message: 'Weet je zeker dat je deze kilometers wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.',
      confirmText: 'Verwijderen',
      cancelText: 'Annuleren',
      confirmButtonClass: 'btn-danger',
      onConfirm: async () => {
        try {
          setLoading(true);
          await deleteMileageEntry(selectedMileageEntry.id);

          // Reset and go back to timesheet
          setSelectedMileageEntry(null);
          setIsEditing(false);
          setActiveTab('timesheet');
        } catch (err) {
          console.error('Error deleting mileage entry:', err);
          setError('Er is een fout opgetreden bij het verwijderen van de kilometers.');
        } finally {
          setLoading(false);
        }
      }
    });
  };

  const handleCancel = () => {
    setSelectedTimeEntry(null);
    setSelectedMileageEntry(null);
    setIsEditing(false);
    setActiveTab('timesheet');
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('nl-NL', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }).format(date);
  };

  const formatTime = (timeString: string) => {
    return timeString.substring(0, 5); // Extract HH:MM from HH:MM:SS
  };

  const calculateHours = (startTime: string, endTime: string, breakTime: number = 0) => {
    const start = new Date(`2000-01-01T${startTime}`);
    const end = new Date(`2000-01-01T${endTime}`);
    const diffMs = end.getTime() - start.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    // Subtract break time (convert minutes to hours)
    const breakHours = breakTime / 60;
    const totalHours = Math.max(0, diffHours - breakHours);
    return totalHours.toFixed(2);
  };

  const getStatusClass = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved':
        return 'Goedgekeurd';
      case 'rejected':
        return 'Afgekeurd';
      default:
        return 'In behandeling';
    }
  };

  if (!user) {
    return <LoadingSpinner />;
  }

  return (
    <MobileContainer>
      <MobilePageHeader
        title="Uren en kilometers registratie"
        actions={
          <MobileButtonGroup direction="responsive">
            <button
              onClick={handleAddTimeClick}
              className="btn btn-primary mobile-touch-target"
            >
              <FaClock className="mr-2" /> Uren toevoegen
            </button>
            <button
              onClick={handleAddMileageClick}
              className="btn btn-secondary mobile-touch-target"
            >
              <FaCar className="mr-2" /> Kilometers toevoegen
            </button>
          </MobileButtonGroup>
        }
      />

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      {loading ? (
        <LoadingSpinner />
      ) : (
        <>
          {activeTab === 'timesheet' && (
            <MonthlyTimesheet
              userId={user.id}
              onSelectTimeEntry={handleTimeEntrySelect}
              onSelectMileageEntry={handleMileageEntrySelect}
            />
          )}

          {activeTab === 'addTime' && (
            <>
              {selectedTimeEntry && !isEditing ? (
                <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
                  <div className="flex justify-between items-start mb-6">
                    <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text">
                      <FaClock className="inline mr-2" /> Urendetails
                    </h2>

                    <div className="flex space-x-2">
                      {selectedTimeEntry.status === 'pending' && (
                        <>
                          <button
                            onClick={handleEditTimeEntry}
                            className="btn btn-sm btn-secondary flex items-center"
                          >
                            <FaEdit className="mr-1" /> Bewerken
                          </button>
                          <button
                            onClick={handleDeleteTimeEntry}
                            className="btn btn-sm btn-danger flex items-center"
                          >
                            <FaTrash className="mr-1" /> Verwijderen
                          </button>
                        </>
                      )}
                      <button
                        onClick={handleCancel}
                        className="btn btn-sm btn-light flex items-center"
                      >
                        <FaTimes className="mr-1" /> Sluiten
                      </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Datum</p>
                      <p className="text-lg font-medium">{formatDate(selectedTimeEntry.date)}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Status</p>
                      <p className="text-lg">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(selectedTimeEntry.status)}`}>
                          {getStatusText(selectedTimeEntry.status)}
                        </span>
                      </p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Starttijd</p>
                      <p className="text-lg font-medium">{formatTime(selectedTimeEntry.start_time)}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Eindtijd</p>
                      <p className="text-lg font-medium">{formatTime(selectedTimeEntry.end_time)}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Pauze tijd</p>
                      <p className="text-lg font-medium">{selectedTimeEntry.break_time} minuten</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Totale uren</p>
                      <p className="text-lg font-medium">{calculateHours(selectedTimeEntry.start_time, selectedTimeEntry.end_time, selectedTimeEntry.break_time)} uur</p>
                    </div>
                  </div>

                  <div className="mb-6">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Omschrijving</p>
                    <p className="text-lg">{selectedTimeEntry.description || '-'}</p>
                  </div>

                  {selectedTimeEntry.approved_by && (
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {selectedTimeEntry.status === 'approved' ? 'Goedgekeurd' : 'Afgekeurd'} door {selectedTimeEntry.approver_name} op {formatDate(selectedTimeEntry.approved_at || '')}
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <TimeEntryForm
                  initialData={isEditing && selectedTimeEntry ? {
                    user_id: selectedTimeEntry.user_id,
                    date: selectedTimeEntry.date,
                    start_time: formatTime(selectedTimeEntry.start_time),
                    end_time: formatTime(selectedTimeEntry.end_time),
                    break_time: selectedTimeEntry.break_time,
                    description: selectedTimeEntry.description || ''
                  } : undefined}
                  onSubmit={handleTimeEntrySubmit}
                  onCancel={handleCancel}
                />
              )}
            </>
          )}

          {activeTab === 'addMileage' && (
            <>
              {selectedMileageEntry && !isEditing ? (
                <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
                  <div className="flex justify-between items-start mb-6">
                    <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text">
                      <FaCar className="inline mr-2" /> Kilometerdetails
                    </h2>

                    <div className="flex space-x-2">
                      {selectedMileageEntry.status === 'pending' && (
                        <>
                          <button
                            onClick={handleEditMileageEntry}
                            className="btn btn-sm btn-secondary flex items-center"
                          >
                            <FaEdit className="mr-1" /> Bewerken
                          </button>
                          <button
                            onClick={handleDeleteMileageEntry}
                            className="btn btn-sm btn-danger flex items-center"
                          >
                            <FaTrash className="mr-1" /> Verwijderen
                          </button>
                        </>
                      )}
                      <button
                        onClick={handleCancel}
                        className="btn btn-sm btn-light flex items-center"
                      >
                        <FaTimes className="mr-1" /> Sluiten
                      </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Datum</p>
                      <p className="text-lg font-medium">{formatDate(selectedMileageEntry.date)}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Status</p>
                      <p className="text-lg">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClass(selectedMileageEntry.status)}`}>
                          {getStatusText(selectedMileageEntry.status)}
                        </span>
                      </p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Kenteken</p>
                      <p className="text-lg font-medium">{selectedMileageEntry.license_plate}</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Beginstand</p>
                      <p className="text-lg font-medium">{selectedMileageEntry.start_odometer.toLocaleString()} km</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Eindstand</p>
                      <p className="text-lg font-medium">{selectedMileageEntry.end_odometer.toLocaleString()} km</p>
                    </div>

                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Kilometers</p>
                      <p className="text-lg font-medium">{selectedMileageEntry.kilometers.toFixed(2)} km</p>
                    </div>
                  </div>

                  <div className="mb-6">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Reden</p>
                    <p className="text-lg font-medium">{selectedMileageEntry.reason}</p>
                  </div>

                  <div className="mb-6">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Omschrijving</p>
                    <p className="text-lg">{selectedMileageEntry.description || '-'}</p>
                  </div>

                  {selectedMileageEntry.approved_by && (
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {selectedMileageEntry.status === 'approved' ? 'Goedgekeurd' : 'Afgekeurd'} door {selectedMileageEntry.approver_name} op {formatDate(selectedMileageEntry.approved_at || '')}
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <MileageEntryForm
                  initialData={isEditing && selectedMileageEntry ? {
                    user_id: selectedMileageEntry.user_id,
                    date: selectedMileageEntry.date,
                    license_plate: selectedMileageEntry.license_plate,
                    start_odometer: selectedMileageEntry.start_odometer,
                    end_odometer: selectedMileageEntry.end_odometer,
                    kilometers: selectedMileageEntry.kilometers,
                    reason: selectedMileageEntry.reason,
                    description: selectedMileageEntry.description || ''
                  } : undefined}
                  onSubmit={handleMileageEntrySubmit}
                  onCancel={handleCancel}
                />
              )}
            </>
          )}
        </>
      )}
    </MobileContainer>
  );
};

export default TimeTracking;
