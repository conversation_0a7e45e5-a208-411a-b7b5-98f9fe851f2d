import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { getUserById } from '../services/userService';
import {
  getTimeEntriesByMonthYear,
  getMileageEntriesByMonthYear,
  getPendingTimeEntries,
  getPendingMileageEntries,
  approveTimeEntry,
  rejectTimeEntry,
  approveMileageEntry,
  rejectMileageEntry
} from '../services/timeTrackingService';
import { User } from '../types/user';
import { TimeEntry } from '../types/timeEntry';
import { MileageEntry } from '../types/mileageEntry';
import LoadingSpinner from '../components/LoadingSpinner';
import MonthlyTimesheet from '../components/timetracking/MonthlyTimesheet';
import { useConfirmation } from '../context/ConfirmationContext';
import {
  FaUser<PERSON>lock,
  FaArrowLeft,
  FaCalendarDay,
  FaCalendarWeek,
  FaCalendarAlt,
  FaChevronLeft,
  FaChevronRight,
  FaCheck,
  FaTimes,
  FaClock,
  FaCar
} from 'react-icons/fa';

const TimeTrackingUserDetail: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const { user: currentUser } = useAuth();
  const { showConfirmation } = useConfirmation();

  // User data
  const [user, setUser] = useState<User | null>(null);

  // UI state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Time period filter
  const [viewMode, setViewMode] = useState<'day' | 'week' | 'month'>('month');
  const [currentDate, setCurrentDate] = useState(new Date());

  // Time entries
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([]);
  const [mileageEntries, setMileageEntries] = useState<MileageEntry[]>([]);
  const [pendingTimeEntries, setPendingTimeEntries] = useState<TimeEntry[]>([]);
  const [pendingMileageEntries, setPendingMileageEntries] = useState<MileageEntry[]>([]);

  // Totals
  const [approvedHours, setApprovedHours] = useState(0);
  const [pendingHours, setPendingHours] = useState(0);
  const [approvedKilometers, setApprovedKilometers] = useState(0);
  const [pendingKilometers, setPendingKilometers] = useState(0);

  // Get current period values - recalculated whenever currentDate changes
  const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed
  const currentYear = currentDate.getFullYear();
  const currentWeek = getWeekNumber(currentDate);

  // Load user data
  useEffect(() => {
    const fetchUser = async () => {
      if (!userId) return;

      try {
        setLoading(true);
        const userData = await getUserById(parseInt(userId));
        setUser(userData);
      } catch (err) {
        console.error('Error fetching user:', err);
        setError('Er is een fout opgetreden bij het ophalen van de gebruiker.');
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [userId]);

  // Load time entries based on current view mode
  useEffect(() => {
    const fetchTimeEntries = async () => {
      if (!userId) return;

      try {
        setLoading(true);

        // Always fetch monthly data for now
        // In a real implementation, we would fetch data based on viewMode
        const timeResponse = await getTimeEntriesByMonthYear(
          currentMonth,
          currentYear,
          parseInt(userId)
        );

        setTimeEntries(timeResponse.entries);
        setApprovedHours(timeResponse.monthly_approved_hours || 0);
        setPendingHours(timeResponse.monthly_pending_hours || 0);

        const mileageResponse = await getMileageEntriesByMonthYear(
          currentMonth,
          currentYear,
          parseInt(userId)
        );

        setMileageEntries(mileageResponse.entries);
        setApprovedKilometers(mileageResponse.monthly_approved_kilometers || 0);
        setPendingKilometers(mileageResponse.monthly_pending_kilometers || 0);

        // Fetch pending entries
        const pendingTimeResponse = await getPendingTimeEntries();
        setPendingTimeEntries(
          pendingTimeResponse.entries.filter(
            (entry: TimeEntry) => entry.user_id === parseInt(userId)
          )
        );

        const pendingMileageResponse = await getPendingMileageEntries();
        setPendingMileageEntries(
          pendingMileageResponse.entries.filter(
            (entry: MileageEntry) => entry.user_id === parseInt(userId)
          )
        );
      } catch (err) {
        console.error('Error fetching time entries:', err);
        setError('Er is een fout opgetreden bij het ophalen van de urenregistraties.');
      } finally {
        setLoading(false);
      }
    };

    fetchTimeEntries();
  }, [userId, currentDate, viewMode]);

  // Helper function to get week number
  function getWeekNumber(date: Date): number {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }

  // Navigation functions
  const goToPreviousPeriod = () => {
    const newDate = new Date(currentDate);

    if (viewMode === 'day') {
      newDate.setDate(newDate.getDate() - 1);
    } else if (viewMode === 'week') {
      newDate.setDate(newDate.getDate() - 7);
    } else {
      newDate.setMonth(newDate.getMonth() - 1);
    }

    setCurrentDate(newDate);
  };

  const goToNextPeriod = () => {
    const newDate = new Date(currentDate);

    if (viewMode === 'day') {
      newDate.setDate(newDate.getDate() + 1);
    } else if (viewMode === 'week') {
      newDate.setDate(newDate.getDate() + 7);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }

    setCurrentDate(newDate);
  };

  // Format functions
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('nl-NL', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }).format(date);
  };

  const formatTime = (timeString: string) => {
    return timeString.substring(0, 5); // Extract HH:MM from HH:MM:SS
  };

  const calculateHours = (startTime: string, endTime: string, breakTime: number = 0) => {
    const start = new Date(`2000-01-01T${startTime}`);
    const end = new Date(`2000-01-01T${endTime}`);
    const diffMs = end.getTime() - start.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    // Subtract break time (convert minutes to hours)
    const breakHours = breakTime / 60;
    const totalHours = Math.max(0, diffHours - breakHours);
    return totalHours.toFixed(2);
  };

  // Approval functions
  const handleApproveTimeEntry = async (entryId: number) => {
    showConfirmation({
      title: 'Uren goedkeuren',
      message: 'Weet je zeker dat je deze uren wilt goedkeuren?',
      confirmText: 'Goedkeuren',
      cancelText: 'Annuleren',
      onConfirm: async () => {
        try {
          // Find the entry being approved
          const approvedEntry = pendingTimeEntries.find(entry => entry.id === entryId);

          if (approvedEntry) {
            // Calculate hours for this entry (including break time)
            const hours = parseFloat(calculateHours(approvedEntry.start_time, approvedEntry.end_time, approvedEntry.break_time));

            // Update totals
            setApprovedHours(prev => Math.round((prev + hours) * 100) / 100);
            setPendingHours(prev => Math.round((prev - hours) * 100) / 100);
          }

          await approveTimeEntry(entryId);
          // Update the pending entries list
          setPendingTimeEntries(pendingTimeEntries.filter(entry => entry.id !== entryId));
        } catch (err) {
          console.error('Error approving time entry:', err);
          setError('Er is een fout opgetreden bij het goedkeuren van de uren.');
        }
      }
    });
  };

  const handleRejectTimeEntry = async (entryId: number) => {
    showConfirmation({
      title: 'Uren afkeuren',
      message: 'Weet je zeker dat je deze uren wilt afkeuren?',
      confirmText: 'Afkeuren',
      cancelText: 'Annuleren',
      onConfirm: async () => {
        try {
          // Find the entry being rejected
          const rejectedEntry = pendingTimeEntries.find(entry => entry.id === entryId);

          if (rejectedEntry) {
            // Calculate hours for this entry (including break time)
            const hours = parseFloat(calculateHours(rejectedEntry.start_time, rejectedEntry.end_time, rejectedEntry.break_time));

            // Update totals - only need to reduce pending hours
            setPendingHours(prev => Math.round((prev - hours) * 100) / 100);
          }

          await rejectTimeEntry(entryId);
          // Update the pending entries list
          setPendingTimeEntries(pendingTimeEntries.filter(entry => entry.id !== entryId));
        } catch (err) {
          console.error('Error rejecting time entry:', err);
          setError('Er is een fout opgetreden bij het afkeuren van de uren.');
        }
      }
    });
  };

  const handleApproveMileageEntry = async (entryId: number) => {
    showConfirmation({
      title: 'Kilometers goedkeuren',
      message: 'Weet je zeker dat je deze kilometers wilt goedkeuren?',
      confirmText: 'Goedkeuren',
      cancelText: 'Annuleren',
      onConfirm: async () => {
        try {
          // Find the entry being approved
          const approvedEntry = pendingMileageEntries.find(entry => entry.id === entryId);

          if (approvedEntry) {
            // Get kilometers for this entry
            const kilometers = approvedEntry.kilometers;

            // Update totals
            setApprovedKilometers(prev => Math.round((prev + kilometers) * 100) / 100);
            setPendingKilometers(prev => Math.round((prev - kilometers) * 100) / 100);
          }

          await approveMileageEntry(entryId);
          // Update the pending entries list
          setPendingMileageEntries(pendingMileageEntries.filter(entry => entry.id !== entryId));
        } catch (err) {
          console.error('Error approving mileage entry:', err);
          setError('Er is een fout opgetreden bij het goedkeuren van de kilometers.');
        }
      }
    });
  };

  const handleRejectMileageEntry = async (entryId: number) => {
    showConfirmation({
      title: 'Kilometers afkeuren',
      message: 'Weet je zeker dat je deze kilometers wilt afkeuren?',
      confirmText: 'Afkeuren',
      cancelText: 'Annuleren',
      onConfirm: async () => {
        try {
          // Find the entry being rejected
          const rejectedEntry = pendingMileageEntries.find(entry => entry.id === entryId);

          if (rejectedEntry) {
            // Get kilometers for this entry
            const kilometers = rejectedEntry.kilometers;

            // Update totals - only need to reduce pending kilometers
            setPendingKilometers(prev => Math.round((prev - kilometers) * 100) / 100);
          }

          await rejectMileageEntry(entryId);
          // Update the pending entries list
          setPendingMileageEntries(pendingMileageEntries.filter(entry => entry.id !== entryId));
        } catch (err) {
          console.error('Error rejecting mileage entry:', err);
          setError('Er is een fout opgetreden bij het afkeuren van de kilometers.');
        }
      }
    });
  };

  // Get period label based on view mode
  const getPeriodLabel = () => {
    const monthNames = [
      'Januari', 'Februari', 'Maart', 'April', 'Mei', 'Juni',
      'Juli', 'Augustus', 'September', 'Oktober', 'November', 'December'
    ];

    if (viewMode === 'day') {
      return formatDate(currentDate.toISOString().split('T')[0]);
    } else if (viewMode === 'week') {
      return `Week ${currentWeek}, ${currentYear}`;
    } else {
      return `${monthNames[currentMonth - 1]} ${currentYear}`;
    }
  };

  // Check if the user is authorized
  if (!currentUser || currentUser.role !== 'administrator') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          Je hebt geen toegang tot deze pagina. Alleen administrators kunnen deze pagina bekijken.
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header with back button and user info */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div className="flex items-center mb-4 md:mb-0">
          <button
            onClick={() => navigate('/time-tracking-admin')}
            className="mr-4 text-amspm-text dark:text-dark-text hover:text-amspm-primary dark:hover:text-amspm-primary"
            aria-label="Terug naar overzicht"
          >
            <FaArrowLeft size={20} />
          </button>
          <h1 className="text-2xl font-bold text-amspm-text dark:text-dark-text">
            <FaUserClock className="inline mr-2" />
            {loading ? 'Gebruiker laden...' : `Uren beheer: ${user?.name || user?.email || 'Onbekende gebruiker'}`}
          </h1>
        </div>

        {/* View mode selector */}
        <div className="flex space-x-2">
          <button
            onClick={() => setViewMode('day')}
            className={`btn btn-sm ${viewMode === 'day' ? 'btn-primary' : 'btn-secondary'}`}
          >
            <FaCalendarDay className="mr-1" /> Dag
          </button>
          <button
            onClick={() => setViewMode('week')}
            className={`btn btn-sm ${viewMode === 'week' ? 'btn-primary' : 'btn-secondary'}`}
          >
            <FaCalendarWeek className="mr-1" /> Week
          </button>
          <button
            onClick={() => setViewMode('month')}
            className={`btn btn-sm ${viewMode === 'month' ? 'btn-primary' : 'btn-secondary'}`}
          >
            <FaCalendarAlt className="mr-1" /> Maand
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      {loading ? (
        <LoadingSpinner />
      ) : (
        <>
          {/* Period navigation */}
          <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6 mb-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text">
                {getPeriodLabel()}
              </h2>

              <div className="flex items-center space-x-2">
                <button
                  onClick={goToPreviousPeriod}
                  className="btn btn-icon btn-secondary"
                  aria-label="Vorige periode"
                >
                  <FaChevronLeft />
                </button>
                <button
                  onClick={goToNextPeriod}
                  className="btn btn-icon btn-secondary"
                  aria-label="Volgende periode"
                >
                  <FaChevronRight />
                </button>
              </div>
            </div>

            {/* Summary cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              {/* Uren sectie */}
              <div className="bg-white dark:bg-dark-card rounded-lg p-4 shadow-sm">
                <h3 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-3 flex items-center">
                  <FaClock className="mr-2" /> Uren
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  <div className="bg-blue-50 dark:bg-blue-900 rounded-lg p-4 flex items-center">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-blue-700 dark:text-blue-200">Goedgekeurd</h4>
                      <p className="text-2xl font-bold text-blue-800 dark:text-blue-100">{approvedHours.toFixed(2)}</p>
                    </div>
                  </div>
                  <div className="bg-yellow-50 dark:bg-yellow-900 rounded-lg p-4 flex items-center">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-yellow-700 dark:text-yellow-200">Openstaand</h4>
                      <p className="text-2xl font-bold text-yellow-800 dark:text-yellow-100">{pendingHours.toFixed(2)}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Kilometers sectie */}
              <div className="bg-white dark:bg-dark-card rounded-lg p-4 shadow-sm">
                <h3 className="text-lg font-semibold text-amspm-text dark:text-dark-text mb-3 flex items-center">
                  <FaCar className="mr-2" /> Kilometers
                </h3>
                <div className="grid grid-cols-2 gap-3">
                  <div className="bg-green-50 dark:bg-green-900 rounded-lg p-4 flex items-center">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-green-700 dark:text-green-200">Goedgekeurd</h4>
                      <p className="text-2xl font-bold text-green-800 dark:text-green-100">{approvedKilometers.toFixed(2)}</p>
                    </div>
                  </div>
                  <div className="bg-orange-50 dark:bg-orange-900 rounded-lg p-4 flex items-center">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-orange-700 dark:text-orange-200">Openstaand</h4>
                      <p className="text-2xl font-bold text-orange-800 dark:text-orange-100">{pendingKilometers.toFixed(2)}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Pending approvals section */}
          {(pendingTimeEntries.length > 0 || pendingMileageEntries.length > 0) && (
            <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6 mb-6">
              <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text mb-4">
                Wachtend op goedkeuring
              </h2>

              {pendingTimeEntries.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-amspm-text dark:text-dark-text mb-4 flex items-center">
                    <FaClock className="mr-2" /> Uren
                  </h3>

                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Datum
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Tijd
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Pauze
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Uren
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Omschrijving
                          </th>
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Acties
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                        {pendingTimeEntries.map(entry => (
                          <tr key={entry.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {formatDate(entry.date)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {formatTime(entry.start_time)} - {formatTime(entry.end_time)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {entry.break_time > 0 ? `${entry.break_time} min` : '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {calculateHours(entry.start_time, entry.end_time, entry.break_time)}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900 dark:text-dark-text">
                              {entry.description || <span className="text-gray-500 italic">Geen omschrijving</span>}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                              <div className="flex justify-end space-x-2">
                                <button
                                  onClick={() => handleApproveTimeEntry(entry.id)}
                                  className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                                  title="Goedkeuren"
                                >
                                  <FaCheck />
                                </button>
                                <button
                                  onClick={() => handleRejectTimeEntry(entry.id)}
                                  className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                  title="Afkeuren"
                                >
                                  <FaTimes />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {pendingMileageEntries.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-amspm-text dark:text-dark-text mb-4 flex items-center">
                    <FaCar className="mr-2" /> Kilometers
                  </h3>

                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Datum
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Kenteken
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Beginstand
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Eindstand
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Kilometers
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Reden
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Omschrijving
                          </th>
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Acties
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                        {pendingMileageEntries.map(entry => (
                          <tr key={entry.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {formatDate(entry.date)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {entry.license_plate}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {entry.start_odometer.toLocaleString()} km
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {entry.end_odometer.toLocaleString()} km
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {entry.kilometers.toFixed(2)} km
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                              {entry.reason}
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-900 dark:text-dark-text">
                              {entry.description || <span className="text-gray-500 italic">Geen omschrijving</span>}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                              <div className="flex justify-end space-x-2">
                                <button
                                  onClick={() => handleApproveMileageEntry(entry.id)}
                                  className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                                  title="Goedkeuren"
                                >
                                  <FaCheck />
                                </button>
                                <button
                                  onClick={() => handleRejectMileageEntry(entry.id)}
                                  className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                  title="Afkeuren"
                                >
                                  <FaTimes />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Time entries section */}
          <div className="bg-white dark:bg-dark-card shadow-md rounded-lg p-6">
            <h2 className="text-xl font-semibold text-amspm-text dark:text-dark-text mb-6">
              Geregistreerde uren en kilometers
            </h2>

            {/* Time Entries */}
            <div className="mb-8">
              <h3 className="text-lg font-medium text-amspm-text dark:text-dark-text mb-4 flex items-center">
                <FaClock className="mr-2" /> Uren
              </h3>

              {timeEntries.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Datum
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Tijd
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Pauze
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Uren
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Omschrijving
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                      {timeEntries.map(entry => (
                        <tr key={entry.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                            {formatDate(entry.date)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                            {formatTime(entry.start_time)} - {formatTime(entry.end_time)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                            {entry.break_time > 0 ? `${entry.break_time} min` : '-'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                            {calculateHours(entry.start_time, entry.end_time, entry.break_time)}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-900 dark:text-dark-text">
                            {entry.description || <span className="text-gray-500 italic">Geen omschrijving</span>}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                              ${entry.status === 'approved'
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                : entry.status === 'rejected'
                                  ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                  : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                              }`}
                            >
                              {entry.status === 'approved'
                                ? 'Goedgekeurd'
                                : entry.status === 'rejected'
                                  ? 'Afgekeurd'
                                  : 'In behandeling'
                              }
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                  Geen urenregistraties gevonden voor deze periode.
                </p>
              )}
            </div>

            {/* Mileage Entries */}
            <div>
              <h3 className="text-lg font-medium text-amspm-text dark:text-dark-text mb-4 flex items-center">
                <FaCar className="mr-2" /> Kilometers
              </h3>

              {mileageEntries.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Datum
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Kenteken
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Beginstand
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Eindstand
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Kilometers
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Reden
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Omschrijving
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-dark-card divide-y divide-gray-200 dark:divide-gray-700">
                      {mileageEntries.map(entry => (
                        <tr key={entry.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                            {formatDate(entry.date)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                            {entry.license_plate}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                            {entry.start_odometer.toLocaleString()} km
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                            {entry.end_odometer.toLocaleString()} km
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                            {entry.kilometers.toFixed(2)} km
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                            {entry.reason}
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-900 dark:text-dark-text">
                            {entry.description || <span className="text-gray-500 italic">Geen omschrijving</span>}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-dark-text">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                              ${entry.status === 'approved'
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                : entry.status === 'rejected'
                                  ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                  : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                              }`}
                            >
                              {entry.status === 'approved'
                                ? 'Goedgekeurd'
                                : entry.status === 'rejected'
                                  ? 'Afgekeurd'
                                  : 'In behandeling'
                              }
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                  Geen kilometerregistraties gevonden voor deze periode.
                </p>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default TimeTrackingUserDetail;