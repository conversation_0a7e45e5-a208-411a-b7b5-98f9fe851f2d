import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { getDocumentsByCustomer } from "../services/documentService";
import { getCustomerWithEventAccess } from "../services/customerService";
import { Document } from "../types/document";
import { Customer } from "../types/customer";
import { useAuth } from "../context/AuthContext";
import { auth } from "../firebase";
import api from "../api";

import { getMyEvents } from "../services/eventService";
import { Event } from "../types/event";
import LoadingSpinner from "../components/LoadingSpinner";
import DocumentPreview from "../components/DocumentPreview";
import { FaFileAlt, FaHistory, FaArrowLeft, FaHome, FaChevronRight } from "react-icons/fa";

// Component to display a document card
const DocumentCard: React.FC<{
  document: Document;
  onPreview: (doc: Document) => void;
  onDownload: (doc: Document) => void;
}> = ({ document, onPreview, onDownload }) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('nl-NL', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="card">
      <div className="card-content">
        <h4 className="font-medium text-amspm-text mb-2">{document.document_type.replace(/_/g, " ")}</h4>
        <p className="text-sm text-gray-600 mb-1">
          <span className="font-medium">Filename:</span> {document.name}
        </p>
        <p className="text-sm text-gray-600 mb-1">
          <span className="font-medium">Status:</span>{" "}
          <span className={`${
            document.status === "active" || document.status === "not_applicable"
              ? "text-green-600"
              : "text-gray-500"
          }`}>
            {document.status === "active" ? "Active" :
             document.status === "not_applicable" ? "No Version Status" : "Inactive"}
          </span>
        </p>
        {document.expiry_date && (
          <p className="text-sm text-gray-600 mb-1">
            <span className="font-medium">Expiry Date:</span>{" "}
            {formatDate(document.expiry_date)}
          </p>
        )}
        <p className="text-sm text-gray-600 mb-3">
          <span className="font-medium">Upload Date:</span>{" "}
          {formatDate(document.created_at)}
        </p>

        {/* Sub-documents section */}
        {document.document_type === "beveiligingscertificaat" && document.sub_documents && document.sub_documents.length > 0 && (
          <div className="mb-3">
            <h5 className="text-sm font-semibold text-amspm-text mb-2">Sub-Documents:</h5>
            <div className="space-y-2">
              {document.sub_documents.map((subDoc) => (
                <div key={subDoc.id} className="bg-gray-50 p-2 rounded border-l-2 border-amspm-primary">
                  <div className="flex justify-between items-center">
                    <span className="text-xs font-medium text-amspm-text capitalize">
                      {subDoc.document_type.replace(/_/g, " ")}
                    </span>
                    <button
                      onClick={() => onPreview(subDoc)}
                      className="text-xs text-amspm-primary hover:text-amspm-primary-dark"
                    >
                      Preview
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    {formatDate(subDoc.created_at)}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={() => onPreview(document)}
            className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm font-medium transition-colors"
          >
            Preview
          </button>
          <button
            onClick={() => onDownload(document)}
            className="bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded text-sm font-medium transition-colors"
          >
            Download
          </button>
        </div>
      </div>
    </div>
  );
};

const UserCustomerDocuments: React.FC = () => {
  const { customerId } = useParams<{ customerId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [customer, setCustomer] = useState<Customer | null>(null);

  const [pendingEvents, setPendingEvents] = useState<Event[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!customerId || !user) return;
      setInitialLoading(true);
      try {

        // Fetch pending events to check if user has access
        const eventsResponse = await getMyEvents();
        const pendingEventsForCustomer = eventsResponse.events.filter(
          (event: Event) =>
            event.status === "pending" &&
            event.customer_id === parseInt(customerId)
        );
        setPendingEvents(pendingEventsForCustomer);

        // If no pending events for this customer, redirect to dashboard
        if (pendingEventsForCustomer.length === 0) {
          setError("You don't have any pending events for this customer.");
          setInitialLoading(false);
          return;
        }

        // Fetch documents
        const documentsResponse = await getDocumentsByCustomer(parseInt(customerId));
        setDocuments(documentsResponse || []);

        // Fetch customer details using the event-access endpoint
        const customerResponse = await getCustomerWithEventAccess(parseInt(customerId));
        setCustomer(customerResponse);
      } catch (err: any) {
        setError(err.response?.data?.error || err.message || "Failed to fetch data.");
      } finally {
        setInitialLoading(false);
      }
    };
    fetchData();
  }, [customerId, user]);

  // Group documents by type and separate active from inactive
  const groupAndSortDocuments = (docs: Document[]) => {
    const result: {
      active: { [key: string]: Document[] };
      inactive: { [key: string]: Document[] };
    } = {
      active: {},
      inactive: {}
    };

    // Check if user has any view permissions
    const hasAnyViewPermissions = user?.role === "administrator" ||
      Object.values(userPermissions).some(permission => permission.can_view === true);

    // If user has no view permissions, return empty result
    if (!hasAnyViewPermissions) {
      return result;
    }

    docs.forEach(doc => {
      // Check if user has explicit view permission for this document type
      // Only consider can_view=true, not upload permission
      const hasPermission = userPermissions[doc.document_type]?.can_view === true;
      if (!hasPermission && user?.role !== "administrator") return;

      const status = (doc.status === "active" || doc.status === "not_applicable") ? "active" : "inactive";
      if (!result[status][doc.document_type]) {
        result[status][doc.document_type] = [];
      }
      result[status][doc.document_type].push(doc);
    });

    // Sort each group by created_at date (newest first)
    Object.keys(result.active).forEach(type => {
      result.active[type].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
    });
    Object.keys(result.inactive).forEach(type => {
      result.inactive[type].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
    });

    return result;
  };

  const handlePreview = (document: Document) => {
    // Validate document data before opening preview
    if (!document.id || (!document.file_url || !document.file_url.trim())) {
      console.error('Invalid document data for preview:', document);
      alert('Cannot preview document: Missing document ID or file URL');
      return;
    }

    setSelectedDocument(document);
  };

  const handleDownload = async (document: Document) => {
    try {
      console.log('Starting download for document:', document.id, document.name);

      // Use the API service with proper blob handling
      const response = await api.get(`/documents/${document.id}/file`, {
        responseType: 'blob',
      });

      // Create a blob URL and trigger download
      const blob = new Blob([response.data], {
        type: response.headers['content-type'] || 'application/octet-stream'
      });

      const url = URL.createObjectURL(blob);
      const link = window.document.createElement('a');
      link.href = url;
      link.download = document.name || `document_${document.id}.pdf`;

      // Add to DOM, click, and remove
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);

      // Clean up the blob URL
      URL.revokeObjectURL(url);

      console.log('Download completed successfully');
    } catch (error) {
      console.error('Error downloading document:', error);
      alert(`Failed to download document: ${error.message}`);
    }
  };

  const handleClosePreview = () => {
    setSelectedDocument(null);
  };

  const handleBack = () => {
    navigate("/dashboard");
  };

  if (initialLoading) {
    return <LoadingSpinner message="Loading customer documents..." />;
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
        <button
          onClick={handleBack}
          className="btn btn-primary flex items-center"
        >
          <FaArrowLeft className="mr-2" /> Back to Dashboard
        </button>
      </div>
    );
  }

  // Check if user has any document types they can view
  const hasViewableDocumentTypes = user?.role === "administrator" ||
    Object.values(userPermissions).some((permission: any) => permission.can_view === true);

  if (!hasViewableDocumentTypes) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
          <p className="font-bold mb-2">No View Permissions</p>
          <p>You don't have permission to view any document types for this customer.</p>
          <p className="mt-2">To view customer documents, you need explicit "View" permission for at least one document type.</p>
          <p className="mt-2">Having only "Upload" permission is not sufficient to view documents.</p>
        </div>
        <button
          onClick={handleBack}
          className="btn btn-primary flex items-center"
        >
          <FaArrowLeft className="mr-2" /> Back to Dashboard
        </button>
      </div>
    );
  }

  const groupedDocuments = groupAndSortDocuments(documents);

  return (
    <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
      {/* Simple breadcrumbs without customer link */}
      <div className="flex items-center text-sm mb-4">
        <Link to="/user-dashboard" className="text-amspm-primary hover:text-amspm-primary-dark flex items-center">
          <FaHome className="mr-1" /> Dashboard
        </Link>
        <FaChevronRight className="mx-2 text-gray-400" size={12} />
        <span className="text-amspm-text font-medium">
          {customer?.name || `Customer Documents`}
        </span>
      </div>

      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-8 space-y-3 sm:space-y-0">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-amspm-text">
            {customer?.name || "Customer Documents"}
          </h1>
          {customer?.address && (
            <p className="text-gray-600 mt-1">{customer.address}</p>
          )}
        </div>
        <button
          onClick={handleBack}
          className="btn btn-outline flex items-center"
        >
          <FaArrowLeft className="mr-2" /> Back to Dashboard
        </button>
      </div>

      <div className="mb-8">
        <div className="bg-white rounded-lg shadow p-4 mb-4">
          <h2 className="text-xl font-semibold text-amspm-text mb-4 flex items-center">
            <FaFileAlt className="mr-2 text-amspm-primary" /> Active Documents
          </h2>

          {Object.keys(groupedDocuments.active).length === 0 ? (
            <div className="bg-gray-50 rounded-lg p-8 text-center">
              <p className="text-gray-600">No active documents found for this customer.</p>
            </div>
          ) : (
            <div className="space-y-8">
              {Object.entries(groupedDocuments.active).map(([type, docs]) => (
                <div key={type} className="card">
                  <div className="card-header">
                    <h3 className="text-xl font-medium text-amspm-text capitalize">
                      {type.replace(/_/g, " ")} Documents
                    </h3>
                  </div>
                  <div className="card-content">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {docs.map((doc) => (
                        <DocumentCard
                          key={doc.id}
                          document={doc}
                          onPreview={handlePreview}
                          onDownload={handleDownload}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow p-4">
          <h2 className="text-xl font-semibold text-amspm-text mb-4 flex items-center">
            <FaHistory className="mr-2 text-amspm-primary" /> Document History
          </h2>

          {Object.keys(groupedDocuments.inactive).length === 0 ? (
            <div className="bg-gray-50 rounded-lg p-8 text-center">
              <p className="text-gray-600">No document history found for this customer.</p>
            </div>
          ) : (
            <div className="space-y-8">
              {Object.entries(groupedDocuments.inactive).map(([type, docs]) => (
                <div key={type} className="card">
                  <div className="card-header">
                    <h3 className="text-xl font-medium text-amspm-text capitalize">
                      {type.replace(/_/g, " ")} Documents
                    </h3>
                  </div>
                  <div className="card-content">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {docs.map((doc) => (
                        <DocumentCard
                          key={doc.id}
                          document={doc}
                          onPreview={handlePreview}
                          onDownload={handleDownload}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Document Preview Modal */}
      {selectedDocument && (
        <DocumentPreview
          documentUrl={selectedDocument.file_url}
          documentId={selectedDocument.id}
          documentName={selectedDocument.name}
          onClose={handleClosePreview}
        />
      )}
    </div>
  );
};

export default UserCustomerDocuments;
