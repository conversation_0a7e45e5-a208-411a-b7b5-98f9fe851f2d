import api from "../api";

export interface Activity {
  id: number;
  type: 'user' | 'customer' | 'document' | 'event';
  action: string;
  subject: string;
  timestamp: string;
  user: {
    id: number;
    name: string;
  };
}

export interface GetRecentActivitiesResponse {
  activities: Activity[];
}

export const getRecentActivities = async (limit: number = 10): Promise<GetRecentActivitiesResponse> => {
  try {
    const response = await api.get(`/audit/recent?limit=${limit}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching recent activities:", error);
    // Return empty data on error
    return {
      activities: [],
      total: 0
    };
  }
};
