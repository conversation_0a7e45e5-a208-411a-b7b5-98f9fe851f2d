import api from "../api";

export interface AuditLog {
  id: number;
  user_id: number;
  action: string;
  entity_type: string;
  entity_id: number;
  details: any;
  ip_address: string;
  user_agent: string;
  created_at: string;
  user: {
    id: number;
    email: string;
    name: string;
    role: string;
  };
}

export interface AuditLogResponse {
  logs: AuditLog[];
  page: number;
  per_page: number;
  total: number;
}

export const getAuditLogs = async (
  page: number = 1,
  perPage: number = 20,
  entityType?: string,
  action?: string,
  userId?: number,
  startDate?: string,
  endDate?: string
): Promise<AuditLogResponse> => {
  const params = new URLSearchParams();
  params.append("page", page.toString());
  params.append("per_page", perPage.toString());
  
  if (entityType) params.append("entity_type", entityType);
  if (action) params.append("action", action);
  if (userId) params.append("user_id", userId.toString());
  if (startDate) params.append("start_date", startDate);
  if (endDate) params.append("end_date", endDate);
  
  const response = await api.get(`/audit?${params.toString()}`);
  return response.data;
};

export const getUserActivity = async (
  userId: number,
  page: number = 1,
  perPage: number = 20
): Promise<AuditLogResponse> => {
  const params = new URLSearchParams();
  params.append("page", page.toString());
  params.append("per_page", perPage.toString());
  
  const response = await api.get(`/audit/user/${userId}?${params.toString()}`);
  return response.data;
};

export const getEntityHistory = async (
  entityType: string,
  entityId: number,
  page: number = 1,
  perPage: number = 20
): Promise<AuditLogResponse> => {
  const params = new URLSearchParams();
  params.append("page", page.toString());
  params.append("per_page", perPage.toString());
  
  const response = await api.get(`/audit/entity/${entityType}/${entityId}?${params.toString()}`);
  return response.data;
};
