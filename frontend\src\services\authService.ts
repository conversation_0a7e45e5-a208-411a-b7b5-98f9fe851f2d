import api from "../api";
import { sendPasswordResetEmail } from "firebase/auth";
import { auth } from "../firebase";

export const verifyToken = async (token: string) => {
  try {
    // Add a timeout to prevent hanging if the server is slow
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 15000); // Increased timeout to 15 seconds

    const response = await api.post("/auth/verify", { token }, {
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    clearTimeout(timeoutId);
    return response.data;
  } catch (error) {
    console.error("Failed to verify token:", error);
    throw error;
  }
};

// Send password reset email using Firebase
export const resetPassword = async (email: string): Promise<void> => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error) {
    console.error("Failed to send password reset email:", error);
    throw error;
  }
};

export const logout = async () => {
  try {
    // Add a timeout to prevent hanging if the server is slow
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // Increased timeout to 10 seconds

    await api.post("/auth/logout", {}, {
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    clearTimeout(timeoutId);
  } catch (error) {
    console.error("Failed to logout:", error);
    // Continue with local logout even if server logout fails
  }
};

// Fetch a CSRF token from the server
export const fetchCSRFToken = async () => {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000);

    const response = await api.get("/auth/csrf-token", {
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    clearTimeout(timeoutId);
    return response.data;
  } catch (error) {
    console.error("Failed to fetch CSRF token:", error);
    throw error;
  }
};