import api from "../api";
import { CustomerNote } from "../types/customer_note";

export const getCustomerNotes = async (customerId: number): Promise<CustomerNote[]> => {
  const response = await api.get(`/customer-notes/customer/${customerId}`);
  return response.data;
};

export const getCustomerNoteById = async (noteId: number): Promise<CustomerNote> => {
  const response = await api.get(`/customer-notes/${noteId}`);
  return response.data;
};

export const createCustomerNote = async (noteData: { customer_id: number; content: string }): Promise<CustomerNote> => {
  const response = await api.post("/customer-notes", noteData);
  return response.data;
};

export const updateCustomerNote = async (noteId: number, content: string): Promise<CustomerNote> => {
  const response = await api.put(`/customer-notes/${noteId}`, { content });
  return response.data;
};

export const deleteCustomerNote = async (noteId: number): Promise<void> => {
  await api.delete(`/customer-notes/${noteId}`);
};
