import api from "../api";
import { Document } from "../types/document";

interface UpcomingExpirationsResponse {
  documents: Document[];
  total: number;
  page: number;
  per_page: number;
  message: string;
}

export const getDocumentsByCustomer = async (customerId: number): Promise<Document[]> => {
  const response = await api.get(`/documents/customer/${customerId}`);
  return response.data;
};

export const getUpcomingExpirations = async (): Promise<UpcomingExpirationsResponse> => {
  try {
    console.log("Calling getUpcomingExpirations API...");
    const response = await api.get<UpcomingExpirationsResponse>('/documents/upcoming-expirations');
    console.log("API Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error in getUpcomingExpirations service:", error);
    throw error;
  }
};

export const getExpiredDocuments = async (): Promise<UpcomingExpirationsResponse> => {
  try {
    const response = await api.get<UpcomingExpirationsResponse>('/documents/expired-documents');
    return response.data;
  } catch (error) {
    console.error("Error in getExpiredDocuments service:", error);
    throw error;
  }
};

export const getDocumentById = async (documentId: number): Promise<Document> => {
  const response = await api.get(`/documents/${documentId}`);
  return response.data;
};

export const createDocument = async (
  customer_id: number | null,
  event_id: number | null,
  file: File | null,
  document_type: string,
  expiryType: "date" | "niet_van_toepassing",
  expiryDate?: string,
  related_document_id?: number,
  isAdminDirectUpload: boolean = false,
  isNotApplicable: boolean = false,
  useVersionStatus: boolean = true,
  versionStatus: "active" | "inactive" = "active"
): Promise<Document> => {
  // All documents now require actual files - no mock documents
  if (!file && !isNotApplicable) {
    throw new Error("A file is required for document upload");
  }

  // If document is marked as not applicable, skip creation
  if (isNotApplicable) {
    throw new Error("Document marked as not applicable - skipping creation");
  }

  try {
    const formData = new FormData();
    // Only add customer_id if it's not null
    if (customer_id !== null) {
      formData.append("customer_id", customer_id.toString());
    }
    if (event_id !== null) {
      formData.append("event_id", event_id.toString());
    }
    if (file) {
      formData.append("file", file);
    }
    formData.append("document_type", document_type);
    formData.append("is_not_applicable", isNotApplicable.toString());

    if (expiryType === "date" && expiryDate) {
      formData.append("expiry_date", expiryDate);
    }

    if (related_document_id) {
      formData.append("related_document_id", related_document_id.toString());
    }

    // Add version status options
    formData.append("use_version_status", useVersionStatus.toString());
    formData.append("version_status", versionStatus);

    // Use the default endpoint if customer_id is null
    const endpoint = (isAdminDirectUpload && customer_id !== null) ?
      `/documents/customer/${customer_id}` : "/documents";

    const response = await api.post(endpoint, formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });

    return response.data;
  } catch (error: any) {
    throw error;
  }
};



export const deleteDocument = async (documentId: number): Promise<void> => {
  await api.delete(`/documents/${documentId}`);
};
