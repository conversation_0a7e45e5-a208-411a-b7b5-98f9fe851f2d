import api from "../api";
import { DocumentTemplate } from "../types/document_template";

export const getAllTemplates = async (): Promise<DocumentTemplate[]> => {
  const response = await api.get("/document-templates");
  return response.data;
};

export const getTemplateById = async (templateId: number): Promise<DocumentTemplate> => {
  const response = await api.get(`/document-templates/${templateId}`);
  return response.data;
};

export const getTemplatesByDocumentType = async (documentType: string): Promise<DocumentTemplate[]> => {
  const response = await api.get(`/document-templates/document-type/${documentType}`);
  return response.data;
};

export const createTemplate = async (formData: FormData): Promise<DocumentTemplate> => {
  const response = await api.post("/document-templates", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

export const updateTemplate = async (templateId: number, data: Partial<DocumentTemplate>): Promise<DocumentTemplate> => {
  const response = await api.put(`/document-templates/${templateId}`, data);
  return response.data;
};

export const deleteTemplate = async (templateId: number): Promise<void> => {
  await api.delete(`/document-templates/${templateId}`);
};

export const getTemplateFileUrl = (templateId: number): string => {
  return `${api.defaults.baseURL}/document-templates/${templateId}/file`;
};

export const saveFilledTemplate = async (templateId: number, customerId: number, file: Blob, filename: string): Promise<any> => {
  const formData = new FormData();
  formData.append("file", file, filename);
  formData.append("customer_id", customerId.toString());

  const response = await api.post(`/document-templates/${templateId}/save`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};
