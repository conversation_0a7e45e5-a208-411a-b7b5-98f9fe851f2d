import api from "../api";

/**
 * Export customers to CSV format
 * @returns A CSV file containing all customers
 */
export const exportCustomersToCSV = async (): Promise<Blob> => {
  const response = await api.get("/export/customers/csv", {
    responseType: "blob",
  });
  return response.data;
};

/**
 * Export customers to JSON format
 * @returns A JSON file containing all customers
 */
export const exportCustomersToJSON = async (): Promise<Blob> => {
  const response = await api.get("/export/customers/json", {
    responseType: "blob",
  });
  return response.data;
};

/**
 * Export customers to Excel format
 * @returns An Excel file containing all customers
 */
export const exportCustomersToExcel = async (): Promise<Blob> => {
  const response = await api.get("/export/customers/excel", {
    responseType: "blob",
  });
  return response.data;
};

/**
 * Import customers from a file (CSV, JSON, or Excel)
 * @param file The file to import
 * @returns A response with import results
 */
export const importCustomers = async (file: File): Promise<{
  message: string;
  imported: number;
  failed: number;
}> => {
  const formData = new FormData();
  formData.append("file", file);

  const response = await api.post("/export/customers/import", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });

  return response.data;
};
