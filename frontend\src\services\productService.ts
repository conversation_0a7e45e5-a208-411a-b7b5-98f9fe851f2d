import api from "../api";
import { Product, ProductsResponse } from "../types/product";

export const getAllProducts = async (page: number = 1, perPage: number = 20): Promise<ProductsResponse> => {
  const response = await api.get(`/products?page=${page}&per_page=${perPage}`);
  return response.data;
};

export const getProductById = async (productId: number): Promise<Product> => {
  const response = await api.get(`/products/${productId}`);
  return response.data;
};

export const searchProducts = async (
  searchTerm: string,
  page: number = 1,
  perPage: number = 20,
  category?: string
): Promise<ProductsResponse> => {
  let url = `/products/search?q=${encodeURIComponent(searchTerm)}&page=${page}&per_page=${perPage}`;
  if (category) {
    url += `&category=${encodeURIComponent(category)}`;
  }
  const response = await api.get(url);
  return response.data;
};

export const getAjaxProducts = async (
  searchTerm: string = "",
  page: number = 1,
  perPage: number = 20
): Promise<ProductsResponse> => {
  return searchProducts(searchTerm, page, perPage, "130");
};

export const getProductsByCategory = async (
  category: string,
  page: number = 1,
  perPage: number = 20
): Promise<ProductsResponse> => {
  const response = await api.get(`/products/category/${encodeURIComponent(category)}?page=${page}&per_page=${perPage}`);
  return response.data;
};

export const getAllCategories = async (): Promise<string[]> => {
  const response = await api.get('/products/categories');
  return response.data.categories;
};

export const createProduct = async (productData: Partial<Product>): Promise<Product> => {
  const response = await api.post('/products', productData);
  return response.data;
};

export const updateProduct = async (productId: number, productData: Partial<Product>): Promise<Product> => {
  const response = await api.put(`/products/${productId}`, productData);
  return response.data;
};

export const deleteProduct = async (productId: number): Promise<void> => {
  await api.delete(`/products/${productId}`);
};

export const deleteAllProducts = async (): Promise<{ count: number }> => {
  const response = await api.delete('/products/delete-all');
  return response.data;
};

export const deleteProductsByCategory = async (category: string): Promise<{ count: number, category: string }> => {
  const response = await api.delete(`/products/category/${encodeURIComponent(category)}/delete`);
  return response.data;
};

export const importProducts = async (file: File): Promise<{
  message: string;
  imported: number;
  updated: number;
  failed: number;
}> => {
  const formData = new FormData();
  formData.append("file", file);

  const response = await api.post("/products/import", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });

  return response.data;
};

export const exportProducts = async (): Promise<Blob> => {
  const response = await api.get("/products/export", {
    responseType: "blob",
  });
  return response.data;
};
