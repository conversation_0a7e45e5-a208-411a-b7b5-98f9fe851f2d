import api from '../api';

export interface SignatureValidationResponse {
  valid: boolean;
  message: string;
}

export interface SignatureSaveResponse {
  message: string;
  file_url: string;
  storage_path: string;
}

export interface SignaturePlaceholderResponse {
  placeholder: string;
  message: string;
}

class SignatureService {
  /**
   * Validate a signature image
   * @param signatureData Base64 encoded signature image
   * @returns Promise resolving to validation result
   */
  static async validateSignature(signatureData: string): Promise<SignatureValidationResponse> {
    try {
      const response = await api.post('/signatures/validate', {
        signature_data: signatureData
      });
      return response.data;
    } catch (error: any) {
      console.error('Error validating signature:', error);
      throw new Error(error.response?.data?.error || 'Failed to validate signature');
    }
  }

  /**
   * Save a signature image to storage
   * @param signatureData Base64 encoded signature image
   * @param customerId ID of the customer
   * @param documentType Type of document (optional)
   * @returns Promise resolving to save result
   */
  static async saveSignature(
    signatureData: string, 
    customerId: number, 
    documentType: string = 'signature'
  ): Promise<SignatureSaveResponse> {
    try {
      const response = await api.post('/signatures/save', {
        signature_data: signatureData,
        customer_id: customerId,
        document_type: documentType
      });
      return response.data;
    } catch (error: any) {
      console.error('Error saving signature:', error);
      throw new Error(error.response?.data?.error || 'Failed to save signature');
    }
  }

  /**
   * Get a placeholder image for missing signatures
   * @returns Promise resolving to placeholder image data
   */
  static async getSignaturePlaceholder(): Promise<SignaturePlaceholderResponse> {
    try {
      const response = await api.get('/signatures/placeholder');
      return response.data;
    } catch (error: any) {
      console.error('Error getting signature placeholder:', error);
      throw new Error(error.response?.data?.error || 'Failed to get signature placeholder');
    }
  }

  /**
   * Check if a signature is empty or invalid
   * @param signatureData Base64 signature string
   * @returns True if signature is empty
   */
  static isSignatureEmpty(signatureData: string): boolean {
    if (!signatureData || !signatureData.startsWith('data:image/')) {
      return true;
    }
    
    // Extract base64 data
    const base64Data = signatureData.replace(/^data:image\/[a-z]+;base64,/, '');
    
    // Simple check for empty signatures
    return base64Data.length < 100;
  }

  /**
   * Validate signature format
   * @param signatureData Base64 signature string
   * @returns True if format is valid
   */
  static isValidSignatureFormat(signatureData: string): boolean {
    return typeof signatureData === 'string' && 
           signatureData.startsWith('data:image/') &&
           signatureData.includes('base64,');
  }
}

export default SignatureService;
