import api from "../api";
import { User } from "../types/user";

interface GetAllUsersResponse {
  users: User[];
  total: number;
  page: number;
  per_page: number;
}

export const getAllUsers = async (page: number = 1, perPage: number = 20): Promise<GetAllUsersResponse> => {
  const response = await api.get(`/users?page=${page}&per_page=${perPage}`);
  return response.data;
};

export const getUserById = async (userId: number): Promise<User> => {
  const response = await api.get(`/users/${userId}`);
  return response.data;
};

export const createUser = async (email: string, password: string, role: string, name?: string): Promise<User> => {
  const response = await api.post("/users", { email, password, role, name });
  return response.data;
};

export const updateUserRole = async (userId: number, role: string): Promise<User> => {
  const response = await api.put(`/users/${userId}/role`, { role });
  return response.data;
};

export const updateUserName = async (userId: number, name: string): Promise<User> => {
  const response = await api.put(`/users/${userId}/name`, { name });
  return response.data;
};

export const getUserLinkedDataCount = async (userId: number): Promise<any> => {
  const response = await api.get(`/users/${userId}/linked-data-count`);
  return response.data;
};

export const deleteUser = async (userId: number): Promise<void> => {
  await api.delete(`/users/${userId}`);
};

export const getUserPermissions = async (userId: number): Promise<User> => {
  const response = await api.get(`/users/${userId}/permissions`);
  return response.data;
};

export const updateUserPermissions = async (userId: number, permissions: UserPermissions): Promise<User> => {
  const response = await api.put(`/users/${userId}/permissions`, { permissions });
  return response.data;
};

export const getDocumentTypes = async (): Promise<string[]> => {
  try {
    const response = await api.get('/users/document-types');
    // Ensure we always return an array
    const documentTypes = response.data.document_types;
    if (Array.isArray(documentTypes)) {
      return documentTypes;
    } else {
      console.error('API returned non-array document_types:', documentTypes);
      return [];
    }
  } catch (error) {
    console.error('Error fetching document types:', error);
    return [];
  }
};

export const getCurrentUserPermissions = async (): Promise<User> => {
  const response = await api.get('/users/current/permissions');
  return response.data;
};

export const updateCurrentUserName = async (name: string): Promise<User> => {
  const response = await api.put('/users/profile/name', { name });
  return response.data;
};

export const updateCurrentUserPassword = async (password: string): Promise<{ message: string }> => {
  const response = await api.put('/users/profile/password', { password });
  return response.data;
};