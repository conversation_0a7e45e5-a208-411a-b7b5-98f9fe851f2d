# Dark Mode Implementation Guide

This document outlines the dark mode implementation in the AMSPM Customer Management application and provides guidelines for maintaining consistent dark mode styling.

## Color Variables

Dark mode colors are defined in two places:

1. **Tailwind Config**: `tailwind.config.js` contains the color definitions used with Tailwind classes
2. **CSS Variables**: `styles/variables.css` contains CSS variables for use in regular CSS

## Using Dark Mode Colors

### In Tailwind Classes

Use the `dark:` prefix to apply styles specifically for dark mode:

```jsx
<div className="bg-white dark:bg-dark-secondary text-amspm-text dark:text-dark-text">
  Dark mode compatible content
</div>
```

### Common Dark Mode Patterns

#### Text Colors
- Primary text: `dark:text-dark-text`
- Secondary/muted text: `dark:text-dark-text-light`
- Very muted text: `dark:text-dark-text-muted`
- Accent text: `dark:text-dark-accent`

#### Background Colors
- Main background: `dark:bg-dark-primary`
- Card/component background: `dark:bg-dark-secondary`
- Slightly lighter background (for hover, etc.): `dark:bg-dark-tertiary`
- Hover state: `dark:hover:bg-dark-hover`

#### Borders
- Standard border: `dark:border-dark-border`
- Lighter/emphasized border: `dark:border-dark-border-light`

#### Form Elements
- Input background: `dark:bg-dark-input`
- Input focus background: `dark:bg-dark-input-focus`
- Input border: `dark:border-dark-border`

#### Status Colors
- Success: `dark:text-dark-success` or `dark:bg-dark-success`
- Warning: `dark:text-dark-warning` or `dark:bg-dark-warning`
- Error: `dark:text-dark-error` or `dark:bg-dark-error`
- Info: `dark:text-dark-info` or `dark:bg-dark-info`

## Best Practices

1. **Always provide dark mode alternatives** for all color-related styles
2. **Maintain sufficient contrast** between text and background colors
3. **Use semantic color variables** rather than arbitrary color values
4. **Test dark mode regularly** to ensure good user experience
5. **Consider reduced motion preferences** for animations in dark mode

## Common Issues and Solutions

### Text Readability
- Ensure text has sufficient contrast against its background
- Use `dark:text-dark-text` for primary content and `dark:text-dark-text-light` for secondary content

### Border Visibility
- Use `dark:border-dark-border` for subtle borders
- Use `dark:border-dark-border-light` for more prominent borders

### Form Elements
- Always style form elements with appropriate dark mode colors
- Ensure focus states are visible in dark mode

### Images and Icons
- Consider providing dark mode alternatives for images with light backgrounds
- Use SVG icons that can adapt to dark mode with `currentColor`

## Testing Dark Mode

1. Toggle dark mode using the theme toggle button in the application
2. Check all pages and components for proper contrast and readability
3. Verify that all interactive elements are clearly visible and usable
4. Test on different screen sizes to ensure responsive dark mode design
