:root {
  /* Light mode variables */
  --amspm-primary: #003087;
  --amspm-primary-rgb: 0, 48, 135;
  --amspm-secondary: #FFFFFF;
  --amspm-background: #F5F5F5;
  --amspm-text: #000000;
  --amspm-text-light: #666666;
  --amspm-light-gray: #E5E5E5;
  
  /* Default button colors */
  --btn-primary-bg: var(--amspm-primary);
  --btn-primary-text: white;
  --btn-secondary-bg: white;
  --btn-secondary-text: var(--amspm-text);
}

.dark {
  /* Dark mode variables */
  --dark-primary: #121212;
  --dark-secondary: #1e1e1e;
  --dark-tertiary: #2a2a2a;
  --dark-accent: #1a75ff;
  --dark-accent-hover: #3385ff;
  --dark-text: #ffffff;
  --dark-text-light: #b3b3b3;
  --dark-text-muted: #8c8c8c;
  --dark-border: #383838;
  --dark-border-light: #4d4d4d;
  --dark-hover: #2c2c2c;
  --dark-input: #2d2d2d;
  --dark-input-focus: #3d3d3d;
  --dark-success: #10b981;
  --dark-warning: #f59e0b;
  --dark-error: #ef4444;
  --dark-info: #3b82f6;
  
  /* Button colors in dark mode */
  --btn-primary-bg: var(--dark-accent);
  --btn-primary-text: white;
  --btn-secondary-bg: var(--dark-secondary);
  --btn-secondary-text: var(--dark-text);
}
