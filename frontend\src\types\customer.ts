export interface Customer {
  id: number;
  date: string | null;  // Datum
  code: string | null;  // Code
  name: string;        // Naam
  kvk_number: string | null;  // KvKnr
  contact_person: string | null;  // Contactpersoon
  gender: string | null;  // Geslacht
  title: string | null;  // Aanhef
  address: string | null;  // Adres
  postal_code: string | null;  // Postcode
  city: string | null;  // Plaats
  country: string | null;  // Land
  address2: string | null;  // Adres2
  postal_code2: string | null;  // Postcode2
  city2: string | null;  // Plaats2
  country2: string | null;  // Land2
  phone: string | null;  // Telefoon
  mobile: string | null;  // Mobiel
  fax: string | null;  // Fax
  email: string | null;  // Email
  invoice_email: string | null;  // Email-facturen
  reminder_email: string | null;  // Email-herinneringen
  website: string | null;  // Website
  bank_account: string | null;  // Bankrekening
  giro_account: string | null;  // Girorekening
  vat_number: string | null;  // BTW-nummer
  iban: string | null;  // IBAN
  bic: string | null;  // BIC
  sepa_auth_type: string | null;  // Sepa-machtigingsoort
  mandate_reference: string | null;  // Machtigingskenmerk
  mandate_date: string | null;  // Machtigingsdatum
  customer_type: string | null;  // Soort
  no_email: boolean;  // Geen e-mail ontvangen
  payment_term: number | null;  // Betalingstermijn
  newsletter_groups: string | null;  // Nieuwsbriefgroepen
  subscriptions: string | null;  // Abonnementen
  notes: string | null;  // Algemene notities
  created_at: string;
  updated_at: string;
}
