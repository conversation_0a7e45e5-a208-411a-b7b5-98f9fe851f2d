import { Document } from "./document";

export interface Event {
  id: number;
  customer_id: number;
  customer_name: string | null;
  customer_address: string | null;
  // New fields for multiple users
  user_ids: number[];
  user_emails: string[];
  user_names: string[];
  // Legacy fields for backward compatibility
  user_id: number | null;
  user_email: string | null;
  document_id: number | null;
  event_type: string;
  description: string;
  scheduled_date: string;
  status: "pending" | "completed";
  completed_at: string | null;
  completed_by?: number;
  completed_by_name?: string;
  completed_by_email?: string;
  created_at: string;
  updated_at: string;
  document?: Document;
}
