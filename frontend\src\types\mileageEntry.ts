export interface MileageEntry {
  id: number;
  user_id: number;
  user_name: string;
  date: string;
  license_plate: string;
  start_odometer: number;
  end_odometer: number;
  kilometers: number;
  reason: string;
  description: string | null;
  status: 'pending' | 'approved' | 'rejected';
  approved_by: number | null;
  approver_name: string | null;
  approved_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface MileageEntryFormData {
  id?: number;
  user_id: number;
  date: string;
  license_plate: string;
  start_odometer: number;
  end_odometer: number;
  kilometers: number;
  reason: string;
  description?: string;
}

export interface MileageEntriesResponse {
  entries: MileageEntry[];
  total: number;
  page: number;
  per_page: number;
  month?: number;
  year?: number;
  monthly_total_kilometers?: number;
  monthly_approved_kilometers?: number;
  monthly_pending_kilometers?: number;
}
