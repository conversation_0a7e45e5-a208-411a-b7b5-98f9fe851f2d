export interface Product {
  id: number;
  product_code: string | null;
  name: string;
  description: string | null;
  gross_price: number | null;
  discount_percentage: number | null;
  net_price: number | null;
  selling_price: number | null;  // Verkoopprijs (bruto prijs * 0.5 * 1.6)
  category: string | null;
  subcategory: string | null;
  info: string | null;
  created_at: string;
  updated_at: string;
}

export interface ProductsResponse {
  products: Product[];
  total: number;
  page: number;
  per_page: number;
}
