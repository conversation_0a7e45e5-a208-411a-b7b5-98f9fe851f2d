/**
 * Possible quotation status values
 */
export type QuotationStatus = 'concept' | 'sent' | 'accepted' | 'rejected';

/**
 * Quotation item interface
 */
export interface QuotationItem {
  id: number;
  quotation_id: number;
  product_id: number | null;
  product_name: string | null;
  product_code: string | null;
  description: string;
  quantity: number;
  unit_price: number;
  discount_percentage: number | null;
  sort_order: number;
  total_price: number;
  created_at: string;
  updated_at: string;
}

/**
 * Quotation interface
 */
export interface Quotation {
  id: number;
  customer_id: number;
  customer_name: string | null;
  created_by: number;
  created_by_name: string | null;
  quotation_number: string | null;
  title: string;
  introduction: string | null;
  conclusion: string | null;
  discount_percentage: number | null;
  vat_percentage: number | null;
  status: QuotationStatus;
  valid_until: string | null;
  created_at: string;
  updated_at: string;
  document_id: number | null;
  items: QuotationItem[];
  subtotal: number;
  discount_amount: number;
  total_excl_vat: number;
  vat_amount: number;
  total_incl_vat: number;
}

export interface QuotationsResponse {
  quotations: Quotation[];
  total: number;
  page: number;
  per_page: number;
}
