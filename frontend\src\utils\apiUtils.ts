/**
 * Get the base API URL
 * @returns The base API URL
 */
export const getApiUrl = (): string => {
  // Use the environment variable if available, otherwise use the default
  // Vite uses import.meta.env instead of process.env
  const apiUrl = import.meta.env.VITE_API_URL;

  if (apiUrl) {
    // Ensure the URL doesn't end with /api if it's already included
    if (apiUrl.endsWith('/api')) {
      return apiUrl;
    }
    // Add /api if not present
    return apiUrl.endsWith('/') ? `${apiUrl}api` : `${apiUrl}/api`;
  }

  // Development fallback
  return 'https://localhost:5000/api';
};
