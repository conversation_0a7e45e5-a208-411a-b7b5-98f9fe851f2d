import * as yup from 'yup';
import { ALLOWED_DOCUMENT_TYPES } from '../constants/documentTypes';

// User validation schemas
export const loginSchema = yup.object().shape({
  email: yup.string()
    .email('Please enter a valid email address')
    .required('Email is required'),
  password: yup.string()
    .required('Password is required')
    .min(6, 'Password must be at least 6 characters')
});

export const userCreateSchema = yup.object().shape({
  email: yup.string()
    .email('Please enter a valid email address')
    .required('Email is required'),
  password: yup.string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    ),
  confirmPassword: yup.string()
    .required('Password confirmation is required')
    .oneOf([yup.ref('password')], 'Passwords must match'),
  name: yup.string().nullable(),
  role: yup.string()
    .required('Role is required')
    .oneOf(['administrator', 'verkoper', 'monteur'], 'Invalid role')
});

export const userUpdateSchema = yup.object().shape({
  name: yup.string().nullable(),
  role: yup.string()
    .oneOf(['administrator', 'verkoper', 'monteur'], 'Invalid role')
});

// Customer validation schemas
export const customerSchema = yup.object().shape({
  name: yup.string().required('Name is required'),
  code: yup.string().nullable(),
  kvk_number: yup.string().nullable(),
  address: yup.string().nullable(),
  postal_code: yup.string().nullable(),
  city: yup.string().nullable(),
  country: yup.string().nullable(),
  phone: yup.string().nullable(),
  email: yup.string().email('Please enter a valid email address').nullable(),
  invoice_email: yup.string().email('Please enter a valid invoice email address').nullable(),
  reminder_email: yup.string().email('Please enter a valid reminder email address').nullable(),
  website: yup.string().url('Please enter a valid URL').nullable(),
  contact_person: yup.string().nullable(),
  gender: yup.string().nullable().oneOf(['M', 'V', 'O', null, ''], 'Gender must be M (Man), V (Vrouw), or O (Anders)'),
  notes: yup.string().nullable(),
  bank_account: yup.string().nullable(),
  giro_account: yup.string().nullable(),
  vat_number: yup.string().nullable(),
  iban: yup.string().nullable(),
  bic: yup.string().nullable(),
  sepa_auth_type: yup.string().nullable(),
  mandate_reference: yup.string().nullable(),
  mandate_date: yup.date().nullable(),
  customer_type: yup.string().nullable(),
  payment_term: yup.number().nullable().integer('Payment term must be a whole number'),
  newsletter_groups: yup.string().nullable(),
  subscriptions: yup.string().nullable(),
  no_email: yup.boolean().default(false)
});

// Document validation schemas
export const documentSchema = yup.object().shape({
  document_type: yup.string()
    .required('Document type is required')
    .oneOf(ALLOWED_DOCUMENT_TYPES, 'Invalid document type'),
  customer_id: yup.number().required('Customer ID is required'),
  event_id: yup.number().nullable(),
  expiry_date: yup.date().nullable(),
  file: yup.mixed().when('documentNotApplicable', (documentNotApplicable, schema) =>
    documentNotApplicable ? schema.nullable() : schema.required('File is required')
  ),
  documentNotApplicable: yup.boolean().default(false)
});

// Document upload validation schema
export const documentUploadSchema = yup.object().shape({
  document_type: yup.string()
    .required('Document type is required')
    .oneOf(ALLOWED_DOCUMENT_TYPES, 'Invalid document type'),
  file: yup.mixed().when('documentNotApplicable', (documentNotApplicable, schema) =>
    documentNotApplicable ? schema.nullable() : schema.required('File is required')
  ),
  expiryType: yup.string()
    .required('Expiry type is required')
    .oneOf(['date', 'niet_van_toepassing'], 'Invalid expiry type'),
  expiry_date: yup.date().when('expiryType', (expiryType: any, schema) =>
    expiryType === 'date' ? schema.required('Expiry date is required') : schema.nullable()
  ),
  documentNotApplicable: yup.boolean().default(false)
});

// Handle event validation schema
export const handleEventSchema = yup.object().shape({
  file: yup.mixed().when('documentNotApplicable', (documentNotApplicable, schema) =>
    documentNotApplicable ? schema.nullable() : schema.required('File is required')
  ),
  expiryType: yup.string()
    .required('Expiry type is required')
    .oneOf(['date', 'niet_van_toepassing'], 'Invalid expiry type'),
  newExpiryDate: yup.string().when('expiryType', (expiryType: any, schema) =>
    expiryType === 'date' ? schema.required('Expiry date is required') : schema.nullable()
  ),
  documentNotApplicable: yup.boolean().default(false)
});

// Event validation schemas
export const eventSchema = yup.object().shape({
  event_type: yup.string()
    .required('Event type is required')
    .oneOf([
      'offerte',
      'klantnaam_zoeken',
      'werkbon',
      'onderhoudsbon',
      'onderhoudscontract',
      'meldkamercontract',
      'beveiligingscertificaat',
      'intakedocument',
      'projectietekening',
      'beveiligingsplan',
      'kabeltekeningen',
      'checklist oplevering installatie'
    ], 'Invalid event type'),
  description: yup.string().required('Description is required'),
  scheduled_date: yup.date().required('Scheduled date is required'),
  customer_id: yup.number().nullable(),
  user_id: yup.number().required('User is required'),
  document_id: yup.number().nullable()
});

// Helper function to validate data against a schema
export const validateData = async (schema: yup.AnySchema, data: any): Promise<{ isValid: boolean; errors: string[] }> => {
  try {
    await schema.validate(data, { abortEarly: false });
    return { isValid: true, errors: [] };
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      return {
        isValid: false,
        errors: error.errors
      };
    }
    return {
      isValid: false,
      errors: ['An unknown validation error occurred']
    };
  }
};
