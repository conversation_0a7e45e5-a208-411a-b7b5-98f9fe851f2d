/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        "amspm-primary": "#003087",
        "amspm-secondary": "#FFFFFF",
        "amspm-background": "#F5F5F5",
        "amspm-text": "#000000",
        "amspm-text-light": "#666666",
        "amspm-light-gray": "#E5E5E5",
        // Dark mode colors - improved for better contrast and readability
        "dark": {
          "primary": "#121212", // Main background
          "secondary": "#1e1e1e", // Card/component background
          "tertiary": "#2a2a2a", // Slightly lighter background for hover states
          "accent": "#1a75ff", // Brighter blue for better visibility in dark mode
          "accent-hover": "#3385ff", // Lighter blue for hover states
          "text": "#ffffff", // Primary text color
          "text-light": "#b3b3b3", // Secondary text color with better contrast
          "text-muted": "#8c8c8c", // Muted text with still enough contrast
          "border": "#383838", // Slightly lighter border color
          "border-light": "#4d4d4d", // Lighter border for emphasis
          "hover": "#2c2c2c", // Hover state background
          "input": "#2d2d2d", // Input background
          "input-focus": "#3d3d3d", // Input focus background
          "success": "#10b981", // Success color
          "warning": "#f59e0b", // Warning color
          "error": "#ef4444", // Error color
          "info": "#3b82f6" // Info color
        }
      },
      fontFamily: {
        montserrat: ["Montserrat", "sans-serif"],
      },
      // Mobile-specific spacing
      spacing: {
        'safe-top': 'env(safe-area-inset-top)',
        'safe-bottom': 'env(safe-area-inset-bottom)',
        'safe-left': 'env(safe-area-inset-left)',
        'safe-right': 'env(safe-area-inset-right)',
      },
      // Add CSS variables for easy access to colors in JavaScript
      backgroundColor: {
        'dark-card': 'var(--dark-secondary)',
      },
      textColor: {
        'dark-heading': 'var(--dark-text)',
      },
      borderColor: {
        'dark-default': 'var(--dark-border)',
      }
    },
  },
  plugins: [],
};
