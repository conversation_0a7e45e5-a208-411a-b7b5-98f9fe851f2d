#!/usr/bin/env python3
"""
Advanced Security Validation Script
Performs deep security analysis of the Customer Management System
"""

import os
import re
import json
import ast
from pathlib import Path
from typing import List, Dict, Tuple

class SecurityValidator:
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.passed = []
        
    def add_issue(self, category: str, message: str, severity: str = "HIGH"):
        self.issues.append({"category": category, "message": message, "severity": severity})
    
    def add_warning(self, category: str, message: str):
        self.warnings.append({"category": category, "message": message})
    
    def add_passed(self, category: str, message: str):
        self.passed.append({"category": category, "message": message})

def check_authentication_security():
    """Check authentication implementation security."""
    print("🔐 Analyzing Authentication Security...")
    validator = SecurityValidator()
    
    # Check Firebase token validation
    security_file = Path("backend/app/utils/security.py")
    if security_file.exists():
        content = security_file.read_text()
        
        if "auth.verify_id_token" in content:
            validator.add_passed("Authentication", "Firebase token validation implemented")
        else:
            validator.add_issue("Authentication", "Firebase token validation missing")
        
        if "HttpOnly" in content or "httponly=True" in content:
            validator.add_passed("Authentication", "HttpOnly cookies implemented")
        else:
            validator.add_warning("Authentication", "HttpOnly cookies not found in security.py")
        
        if "ExpiredIdTokenError" in content and "InvalidIdTokenError" in content:
            validator.add_passed("Authentication", "Proper token error handling")
        else:
            validator.add_issue("Authentication", "Incomplete token error handling")
    
    return validator

def check_authorization_security():
    """Check authorization and role-based access control."""
    print("🛡️ Analyzing Authorization Security...")
    validator = SecurityValidator()
    
    security_file = Path("backend/app/utils/security.py")
    if security_file.exists():
        content = security_file.read_text()
        
        if "role_required" in content and "roles_required" in content:
            validator.add_passed("Authorization", "Role-based access control implemented")
        else:
            validator.add_issue("Authorization", "Role-based access control incomplete")
        
        if "request.current_user" in content:
            validator.add_passed("Authorization", "User context properly set")
        else:
            validator.add_warning("Authorization", "User context handling unclear")
    
    return validator

def check_input_validation():
    """Check input validation and sanitization."""
    print("🔍 Analyzing Input Validation...")
    validator = SecurityValidator()
    
    # Check for Marshmallow schemas
    schema_files = list(Path("backend/app/schemas").glob("*.py"))
    if schema_files:
        validator.add_passed("Input Validation", f"Found {len(schema_files)} schema files")
        
        for schema_file in schema_files:
            content = schema_file.read_text()
            if "validate=" in content and "ValidationError" in content:
                validator.add_passed("Input Validation", f"Validation in {schema_file.name}")
    else:
        validator.add_issue("Input Validation", "No schema files found")
    
    # Check frontend validation
    frontend_validation = Path("frontend/src/utils/validation.ts")
    if frontend_validation.exists():
        content = frontend_validation.read_text()
        if "yup" in content and "schema" in content:
            validator.add_passed("Input Validation", "Frontend validation with Yup")
        else:
            validator.add_warning("Input Validation", "Frontend validation unclear")
    
    return validator

def check_file_security():
    """Check file upload and handling security."""
    print("📁 Analyzing File Security...")
    validator = SecurityValidator()
    
    file_validation = Path("backend/app/utils/file_validation.py")
    if file_validation.exists():
        content = file_validation.read_text()
        
        if "validate_file_extension" in content:
            validator.add_passed("File Security", "File extension validation")
        else:
            validator.add_issue("File Security", "File extension validation missing")
        
        if "validate_file_size" in content:
            validator.add_passed("File Security", "File size validation")
        else:
            validator.add_issue("File Security", "File size validation missing")
        
        if "validate_file_content" in content:
            validator.add_passed("File Security", "File content validation")
        else:
            validator.add_issue("File Security", "File content validation missing")
    else:
        validator.add_issue("File Security", "File validation module missing")
    
    return validator

def check_database_security():
    """Check database security measures."""
    print("🗄️ Analyzing Database Security...")
    validator = SecurityValidator()
    
    # Check for SQLAlchemy usage (prevents SQL injection)
    model_files = list(Path("backend/app/models").glob("*.py"))
    if model_files:
        for model_file in model_files:
            content = model_file.read_text()
            if "db.Model" in content and "db.Column" in content:
                validator.add_passed("Database Security", f"SQLAlchemy ORM in {model_file.name}")
    
    # Check for raw SQL usage (potential security risk)
    backend_files = list(Path("backend").rglob("*.py"))
    raw_sql_found = False
    for file_path in backend_files:
        try:
            content = file_path.read_text()
            if re.search(r'execute\s*\(\s*["\']', content) and "text(" in content:
                validator.add_warning("Database Security", f"Raw SQL found in {file_path}")
                raw_sql_found = True
        except:
            continue
    
    if not raw_sql_found:
        validator.add_passed("Database Security", "No unsafe raw SQL usage detected")
    
    return validator

def check_cors_csrf_security():
    """Check CORS and CSRF protection."""
    print("🌐 Analyzing CORS/CSRF Security...")
    validator = SecurityValidator()
    
    app_init = Path("backend/app/__init__.py")
    if app_init.exists():
        content = app_init.read_text()
        
        if "CORS(" in content and "origins" in content:
            validator.add_passed("CORS/CSRF", "CORS configuration found")
        else:
            validator.add_issue("CORS/CSRF", "CORS configuration missing")
        
        if "SeaSurf" in content or "csrf" in content:
            validator.add_passed("CORS/CSRF", "CSRF protection implemented")
        else:
            validator.add_issue("CORS/CSRF", "CSRF protection missing")
        
        if "supports_credentials=True" in content:
            validator.add_passed("CORS/CSRF", "CORS credentials support enabled")
        else:
            validator.add_warning("CORS/CSRF", "CORS credentials support unclear")
    
    return validator

def check_security_headers():
    """Check security headers implementation."""
    print("🛡️ Analyzing Security Headers...")
    validator = SecurityValidator()
    
    app_init = Path("backend/app/__init__.py")
    if app_init.exists():
        content = app_init.read_text()
        
        security_headers = [
            ("X-Content-Type-Options", "nosniff protection"),
            ("X-Frame-Options", "clickjacking protection"),
            ("X-XSS-Protection", "XSS protection"),
            ("Content-Security-Policy", "CSP protection")
        ]
        
        for header, description in security_headers:
            if header in content:
                validator.add_passed("Security Headers", f"{description} implemented")
            else:
                validator.add_issue("Security Headers", f"{description} missing")
    
    return validator

def check_rate_limiting():
    """Check rate limiting implementation."""
    print("🚦 Analyzing Rate Limiting...")
    validator = SecurityValidator()
    
    rate_limiter = Path("backend/app/utils/custom_rate_limiter.py")
    if rate_limiter.exists():
        content = rate_limiter.read_text()
        
        if "rate_limit" in content and "sliding window" in content.lower():
            validator.add_passed("Rate Limiting", "Custom rate limiter implemented")
        else:
            validator.add_warning("Rate Limiting", "Rate limiter implementation unclear")
        
        if "429" in content:
            validator.add_passed("Rate Limiting", "Proper HTTP 429 responses")
        else:
            validator.add_warning("Rate Limiting", "HTTP 429 response unclear")
    else:
        validator.add_issue("Rate Limiting", "Rate limiter module missing")
    
    return validator

def check_logging_security():
    """Check logging and audit security."""
    print("📊 Analyzing Logging Security...")
    validator = SecurityValidator()
    
    # Check for audit logging
    audit_service = Path("backend/app/services/audit_service.py")
    if audit_service.exists():
        validator.add_passed("Logging", "Audit service implemented")
    else:
        validator.add_warning("Logging", "Audit service not found")
    
    # Check for response sanitization
    sanitizer = Path("backend/app/utils/response_sanitizer.py")
    if sanitizer.exists():
        content = sanitizer.read_text()
        if "sanitize_response" in content:
            validator.add_passed("Logging", "Response sanitization implemented")
        else:
            validator.add_warning("Logging", "Response sanitization unclear")
    
    return validator

def check_environment_security():
    """Check environment and configuration security."""
    print("⚙️ Analyzing Environment Security...")
    validator = SecurityValidator()
    
    # Check .env is in .gitignore
    gitignore = Path(".gitignore")
    if gitignore.exists():
        content = gitignore.read_text()
        if ".env" in content:
            validator.add_passed("Environment", ".env files protected by .gitignore")
        else:
            validator.add_issue("Environment", ".env files not in .gitignore")
    
    # Check for production config
    prod_config = Path("backend/.env.production")
    if prod_config.exists():
        validator.add_passed("Environment", "Production environment template exists")
    else:
        validator.add_warning("Environment", "Production environment template missing")
    
    return validator

def main():
    """Run comprehensive security validation."""
    print("🔐 COMPREHENSIVE SECURITY VALIDATION")
    print("=" * 60)
    
    validators = [
        check_authentication_security(),
        check_authorization_security(),
        check_input_validation(),
        check_file_security(),
        check_database_security(),
        check_cors_csrf_security(),
        check_security_headers(),
        check_rate_limiting(),
        check_logging_security(),
        check_environment_security()
    ]
    
    # Aggregate results
    total_issues = sum(len(v.issues) for v in validators)
    total_warnings = sum(len(v.warnings) for v in validators)
    total_passed = sum(len(v.passed) for v in validators)
    
    print("\n" + "=" * 60)
    print("📊 SECURITY VALIDATION RESULTS")
    print("=" * 60)
    
    # Show critical issues
    if total_issues > 0:
        print(f"\n❌ CRITICAL ISSUES FOUND ({total_issues}):")
        for validator in validators:
            for issue in validator.issues:
                print(f"  🚨 {issue['category']}: {issue['message']}")
    
    # Show warnings
    if total_warnings > 0:
        print(f"\n⚠️ WARNINGS ({total_warnings}):")
        for validator in validators:
            for warning in validator.warnings:
                print(f"  ⚠️  {warning['category']}: {warning['message']}")
    
    # Show passed checks
    print(f"\n✅ PASSED CHECKS ({total_passed}):")
    for validator in validators:
        for passed in validator.passed:
            print(f"  ✅ {passed['category']}: {passed['message']}")
    
    # Final verdict
    print("\n" + "=" * 60)
    if total_issues == 0:
        print("🟢 SECURITY VALIDATION: PASSED")
        print("✅ Application is SECURE and ready for production deployment")
        security_score = max(85, 100 - (total_warnings * 2))
        print(f"🏆 Security Score: {security_score}/100")
        return 0
    else:
        print("🔴 SECURITY VALIDATION: FAILED")
        print("❌ Critical security issues must be resolved before deployment")
        return 1

if __name__ == "__main__":
    exit(main())
